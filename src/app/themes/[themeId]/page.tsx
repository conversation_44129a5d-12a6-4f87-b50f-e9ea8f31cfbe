'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon } from 'lucide-react';
import ThemeDetail from '@/components/ThemeDetail';

export default function ThemeDetailPage() {
  const router = useRouter();
  const params = useParams();
  const themeId = params.themeId as string;

  const [theme, setTheme] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchThemeDetails = async () => {
      if (!themeId) return;

      setLoading(true);
      setError(null);

      try {
        console.log(`Fetching theme details for: ${themeId}`);
        // Use relative URL and ensure we're using the correct port
        const res = await fetch(`/api/wordpress-theme/${themeId}`, {
          // Ensure we don't use a cached response
          cache: 'no-store'
        });

        const data = await res.json();
        console.log('Theme data received:', data);

        // Check if we got a fallback response (which will have status 200 but might include _error)
        if (data._error && data._error.fallback) {
          console.log('Received fallback theme data with error:', data._error.message);
          // We still use the fallback data, but we can log it
          setTheme(data);
        } else if (!res.ok) {
          console.error('Error response:', data);
          throw new Error(data.error || `Failed to fetch theme details (${res.status})`);
        } else if (data.error) {
          throw new Error(data.error);
        } else {
          console.log('Theme data received successfully');
          setTheme(data);
        }
      } catch (err: any) {
        console.error('Error fetching theme details:', err);
        setError(err.message || 'Failed to load theme details');
      } finally {
        setLoading(false);
      }
    };

    fetchThemeDetails();
  }, [themeId]);

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container px-4 py-6 mx-auto">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="ghost"
            className="flex items-center gap-2"
            onClick={() => router.push('/themes')}
          >
            <ArrowLeftIcon size={16} />
            Back to Themes
          </Button>
        </div>

        <ThemeDetail
          theme={theme}
          loading={loading}
          error={error}
        />
      </div>
    </main>
  );
}
