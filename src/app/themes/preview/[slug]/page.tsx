'use client';

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Laptop, Smartphone, Tablet, Monitor, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { fetchThemeDetails } from '@/lib/wordpress';

interface ThemePreviewPageProps {}

const ThemePreviewPage: React.FC<ThemePreviewPageProps> = () => {
  const params = useParams();
  const router = useRouter();
  const [theme, setTheme] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [styleVariation, setStyleVariation] = useState<'default' | 'alternate1' | 'alternate2'>('default');
  
  const slug = params?.slug as string;
  
  useEffect(() => {
    const loadTheme = async () => {
      if (!slug) return;
      
      try {
        setLoading(true);
        const themeData = await fetchThemeDetails(slug);
        setTheme(themeData);
        setError(null);
      } catch (err) {
        console.error('Error fetching theme:', err);
        setError('Failed to load theme details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    loadTheme();
  }, [slug]);
  
  // Generate a color palette based on the theme name
  const getThemeColors = () => {
    if (!theme?.name) return { primaryColor: '#3b82f6', secondaryColor: '#6366f1', accentColor: '#ec4899', textColor: '#ffffff', bgColor: '#ffffff' };
    
    // Simple hash function to generate consistent colors based on theme name
    const hash = theme.name.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    
    // Generate primary color
    const h = Math.abs(hash) % 360;
    const s = 25 + (Math.abs(hash) % 30); // 25-55% saturation
    const l = 45 + (Math.abs(hash) % 15); // 45-60% lightness
    
    const primaryColor = `hsl(${h}, ${s}%, ${l}%)`;
    const secondaryColor = `hsl(${(h + 30) % 360}, ${s}%, ${l}%)`;
    const accentColor = `hsl(${(h + 180) % 360}, ${s + 20}%, ${l}%)`;
    const textColor = l > 50 ? '#333333' : '#ffffff';
    const bgColor = l > 50 ? '#ffffff' : '#333333';
    
    return { primaryColor, secondaryColor, accentColor, textColor, bgColor };
  };
  
  const colors = getThemeColors();
  
  // Generate mock content based on theme name and category
  const getThemeContent = () => {
    if (!theme?.name) return { siteName: 'WordPress Theme', siteType: 'general' };
    
    const words = theme.name.split(/[-\s]+/).filter(Boolean);
    const capitalizedWords = words.map(word => word.charAt(0).toUpperCase() + word.slice(1));
    const siteName = capitalizedWords.join(' ');
    
    // Determine site type based on tags
    const tags = theme.tags ? Object.keys(theme.tags) : [];
    const isBusinessTheme = tags.some(tag => ['business', 'corporate', 'company', 'professional'].includes(tag.toLowerCase()));
    const isBlogTheme = tags.some(tag => ['blog', 'magazine', 'news', 'journal'].includes(tag.toLowerCase()));
    const isPortfolioTheme = tags.some(tag => ['portfolio', 'photography', 'gallery', 'creative'].includes(tag.toLowerCase()));
    const isEcommerceTheme = tags.some(tag => ['ecommerce', 'shop', 'store', 'woocommerce'].includes(tag.toLowerCase()));
    
    let siteType = 'general';
    if (isBusinessTheme) siteType = 'business';
    else if (isBlogTheme) siteType = 'blog';
    else if (isPortfolioTheme) siteType = 'portfolio';
    else if (isEcommerceTheme) siteType = 'ecommerce';
    
    return { siteName, siteType };
  };
  
  const { siteName, siteType } = getThemeContent();
  
  // Generate mock navigation items based on site type
  const getNavItems = () => {
    const commonItems = ['Home', 'About', 'Contact'];
    
    switch (siteType) {
      case 'business':
        return [...commonItems, 'Services', 'Team', 'Testimonials'];
      case 'blog':
        return [...commonItems, 'Blog', 'Categories', 'Archive'];
      case 'portfolio':
        return [...commonItems, 'Projects', 'Gallery', 'Resume'];
      case 'ecommerce':
        return [...commonItems, 'Shop', 'Products', 'Cart'];
      default:
        return commonItems;
    }
  };
  
  const navItems = getNavItems();
  
  // Generate mock content based on site type
  const getMainContent = () => {
    switch (siteType) {
      case 'business':
        return (
          <>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Professional Solutions for Your Business</h1>
            <p className="mb-6">We provide innovative solutions to help your business grow and succeed in today's competitive market.</p>
            <div className="flex flex-wrap gap-4 mb-8">
              <button className="px-6 py-2 rounded font-medium" style={{ backgroundColor: colors.primaryColor, color: colors.textColor }}>Our Services</button>
              <button className="px-6 py-2 rounded font-medium border" style={{ borderColor: colors.primaryColor, color: colors.primaryColor }}>Contact Us</button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {['Professional Team', 'Quality Service', 'Customer Support'].map((item, i) => (
                <div key={i} className="p-4 rounded shadow-sm" style={{ backgroundColor: i === 0 ? colors.primaryColor : '#ffffff', color: i === 0 ? colors.textColor : 'inherit' }}>
                  <h3 className="text-lg font-semibold mb-2">{item}</h3>
                  <p className="text-sm">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec.</p>
                </div>
              ))}
            </div>
          </>
        );
      case 'blog':
        return (
          <>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Latest Articles</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {['The Ultimate Guide', 'Top 10 Tips', 'How to Improve'].map((item, i) => (
                <div key={i} className="mb-6">
                  <div className="h-40 mb-3 rounded overflow-hidden bg-gray-200">
                    <div className="w-full h-full flex items-center justify-center" style={{ backgroundColor: i === 0 ? colors.primaryColor : colors.secondaryColor }}>
                      <span className="text-white">Featured Image</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold mb-1">{item}</h3>
                  <p className="text-sm text-gray-600 mb-2">May 15, 2023 • 5 min read</p>
                  <p className="text-sm">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis.</p>
                </div>
              ))}
            </div>
          </>
        );
      case 'portfolio':
        return (
          <>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Creative Portfolio</h1>
            <p className="mb-6">Showcasing our best work and creative projects.</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              {[colors.primaryColor, colors.secondaryColor, colors.accentColor, '#f0f0f0', '#e0e0e0', '#d0d0d0'].map((color, i) => (
                <div key={i} className="aspect-square rounded overflow-hidden">
                  <div className="w-full h-full flex items-center justify-center" style={{ backgroundColor: color }}>
                    <span style={{ color: color === '#f0f0f0' || color === '#e0e0e0' || color === '#d0d0d0' ? '#333' : '#fff' }}>Project {i+1}</span>
                  </div>
                </div>
              ))}
            </div>
          </>
        );
      case 'ecommerce':
        return (
          <>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Shop Our Products</h1>
            <p className="mb-6">Discover our collection of high-quality products.</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {['Product One', 'Product Two', 'Product Three', 'Product Four'].map((item, i) => (
                <div key={i} className="mb-6">
                  <div className="h-40 mb-3 rounded overflow-hidden bg-gray-200">
                    <div className="w-full h-full flex items-center justify-center" style={{ backgroundColor: i % 2 === 0 ? colors.primaryColor : colors.secondaryColor }}>
                      <span className="text-white">Product Image</span>
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold mb-1">{item}</h3>
                  <p className="text-sm text-gray-600 mb-2">$49.99</p>
                  <button className="px-4 py-1 text-sm rounded" style={{ backgroundColor: colors.primaryColor, color: colors.textColor }}>Add to Cart</button>
                </div>
              ))}
            </div>
          </>
        );
      default:
        return (
          <>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Welcome to {siteName}</h1>
            <p className="mb-6">This is a preview of how your website might look with this theme.</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="p-4 rounded shadow-sm" style={{ backgroundColor: colors.primaryColor, color: colors.textColor }}>
                <h3 className="text-lg font-semibold mb-2">Feature One</h3>
                <p className="text-sm">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec.</p>
              </div>
              <div className="p-4 rounded shadow-sm bg-white">
                <h3 className="text-lg font-semibold mb-2">Feature Two</h3>
                <p className="text-sm">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec.</p>
              </div>
            </div>
          </>
        );
    }
  };
  
  // Get viewport width based on selected device
  const getViewportWidth = () => {
    switch (viewMode) {
      case 'desktop': return '100%';
      case 'tablet': return '768px';
      case 'mobile': return '375px';
      default: return '100%';
    }
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="bg-red-50 text-red-600 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Error</h2>
          <p>{error}</p>
          <Button className="mt-4" onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header with controls */}
      <header className="bg-white border-b sticky top-0 z-10">
        <div className="container mx-auto px-4 py-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Button variant="ghost" onClick={() => router.back()} className="mr-2">
                <ArrowLeft size={18} />
                <span className="ml-1">Back</span>
              </Button>
              <h1 className="text-lg font-semibold">{theme?.name || 'Theme Preview'}</h1>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="hidden md:flex items-center space-x-2 mr-4">
                <Button 
                  variant={viewMode === 'desktop' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setViewMode('desktop')}
                >
                  <Monitor size={16} className="mr-1" />
                  Desktop
                </Button>
                <Button 
                  variant={viewMode === 'tablet' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setViewMode('tablet')}
                >
                  <Tablet size={16} className="mr-1" />
                  Tablet
                </Button>
                <Button 
                  variant={viewMode === 'mobile' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setViewMode('mobile')}
                >
                  <Smartphone size={16} className="mr-1" />
                  Mobile
                </Button>
              </div>
              
              <div className="hidden md:flex items-center space-x-2">
                <Button 
                  variant={styleVariation === 'default' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setStyleVariation('default')}
                >
                  Default
                </Button>
                <Button 
                  variant={styleVariation === 'alternate1' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setStyleVariation('alternate1')}
                >
                  Style 1
                </Button>
                <Button 
                  variant={styleVariation === 'alternate2' ? 'default' : 'outline'} 
                  size="sm"
                  onClick={() => setStyleVariation('alternate2')}
                >
                  Style 2
                </Button>
              </div>
              
              {theme?.download_link && (
                <Button variant="outline" size="sm" asChild>
                  <a href={theme.download_link} target="_blank" rel="noopener noreferrer">
                    <Download size={16} className="mr-1" />
                    Download
                  </a>
                </Button>
              )}
            </div>
          </div>
        </div>
      </header>
      
      {/* Preview Container */}
      <div className="flex-1 bg-gray-100 overflow-auto">
        <div 
          className="preview-frame mx-auto transition-all duration-300 bg-white shadow-md"
          style={{ 
            width: getViewportWidth(),
            maxWidth: '100%',
            height: 'calc(100vh - 56px)',
            overflow: 'auto'
          }}
        >
          {/* Theme Preview Content */}
          <div className="theme-content">
            {/* Header */}
            <header 
              className="p-4 md:p-6" 
              style={{ 
                backgroundColor: styleVariation === 'default' ? colors.primaryColor : 
                                 styleVariation === 'alternate1' ? colors.secondaryColor : 
                                 colors.accentColor,
                color: colors.textColor
              }}
            >
              <div className="container mx-auto">
                <div className="flex flex-col md:flex-row justify-between items-center">
                  <div className="text-xl md:text-2xl font-bold mb-4 md:mb-0">{siteName}</div>
                  <nav className="hidden md:block">
                    <ul className="flex space-x-6">
                      {navItems.map((item, i) => (
                        <li key={i} className={i === 0 ? 'font-semibold' : ''}>{item}</li>
                      ))}
                    </ul>
                  </nav>
                  <div className="md:hidden">
                    <button className="p-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </header>
            
            {/* Main Content */}
            <main className="p-4 md:p-6">
              <div className="container mx-auto">
                {getMainContent()}
              </div>
            </main>
            
            {/* Footer */}
            <footer 
              className="p-4 md:p-6 mt-8" 
              style={{ 
                backgroundColor: styleVariation === 'default' ? '#f5f5f5' : 
                                 styleVariation === 'alternate1' ? colors.primaryColor : 
                                 '#333333',
                color: styleVariation === 'alternate1' ? colors.textColor : 
                       styleVariation === 'alternate2' ? '#ffffff' : 
                       'inherit'
              }}
            >
              <div className="container mx-auto">
                <div className="flex flex-col md:flex-row justify-between items-center">
                  <div className="mb-4 md:mb-0">© 2023 {siteName}. All rights reserved.</div>
                  <div className="flex space-x-4">
                    {['Privacy Policy', 'Terms of Service', 'Contact'].map((item, i) => (
                      <div key={i}>{item}</div>
                    ))}
                  </div>
                </div>
              </div>
            </footer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemePreviewPage;
