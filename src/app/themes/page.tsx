'use client';

import React, { useEffect, useState } from 'react';
import ThemesGrid from '@/components/ThemesGrid';
import { Button } from '@/components/ui/button';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArrowLeftIcon, RefreshCw } from 'lucide-react';

export default function ThemesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [keywords, setKeywords] = useState<string[]>(['business', 'modern']);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get keywords from query params if present
    const keywordsParam = searchParams.get('keywords');
    if (keywordsParam) {
      setKeywords(keywordsParam.split(',').map(k => k.trim()).filter(Boolean));
    }
    setLoading(false);
  }, [searchParams]);

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container px-4 py-6 mx-auto">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            className="flex items-center gap-2"
            onClick={() => router.back()}
          >
            <ArrowLeftIcon size={16} />
            Back to Chat
          </Button>
        </div>

        {error && (
          <div className="p-4 mb-4 text-sm text-red-600 rounded-md bg-red-50">
            {error}
          </div>
        )}

        {keywords.length > 0 && (
          <div className="p-4 mb-4 rounded-md bg-blue-50">
            <p className="text-sm text-blue-600">
              Showing themes based on: <span className="font-semibold">{keywords.join(', ')}</span>
            </p>
            <p className="mt-1 text-xs text-blue-500">
              Click &quot;Generate New Themes&quot; to get different theme suggestions
            </p>
          </div>
        )}

        <ThemesGrid
          keywords={keywords}
          onSelectTheme={(theme) => {
            console.log('Selected theme:', theme);
            // Navigate to the theme detail page using the current hostname and port
            const baseUrl = window.location.origin;
            console.log(`Navigating to theme detail: ${baseUrl}/themes/${theme.id}`);
            router.push(`/themes/${theme.id}`);
          }}
        />
      </div>
    </main>
  );
}