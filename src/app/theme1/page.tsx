'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ExternalLink, RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function Theme1Page() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const wordpressUrl = 'http://192.168.29.49/wordpress/';

  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const refreshIframe = () => {
    setIsLoading(true);
    setHasError(false);
    // Force iframe reload by changing the src
    const iframe = document.querySelector('iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.src = iframe.src;
    }
  };

  const openInNewTab = () => {
    window.open(wordpressUrl, '_blank');
  };

  return (
    <main className="min-h-screen bg-gray-50">
      {/* Header */}
      {/* <div className="bg-white border-b shadow-sm">
        <div className="container px-4 py-3 mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
                onClick={() => router.back()}
              >
                <ArrowLeft size={16} />
                Back
              </Button>
              <h1 className="text-lg font-semibold">WordPress Theme Preview</h1>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                onClick={refreshIframe}
                disabled={isLoading}
              >
                <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                onClick={openInNewTab}
              >
                <ExternalLink size={16} />
                Open in New Tab
              </Button>
            </div>
          </div>
        </div>
      </div> */}

      {/* Content */}
      <div className="relative" style={{ height: 'calc(100vh - 73px)' }}>
        {isLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin" />
              <p className="text-gray-600">Loading WordPress theme...</p>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="max-w-md p-6 mx-auto text-center">
              <div className="mb-4 text-red-500">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-semibold">Unable to Load WordPress Site</h3>
              <p className="mb-4 text-gray-600">
                The WordPress site at {wordpressUrl} could not be loaded. This might be due to:
              </p>
              <ul className="mb-4 text-sm text-left text-gray-500">
                <li>• The server is not running</li>
                <li>• Network connectivity issues</li>
                <li>• CORS or security restrictions</li>
                <li>• The URL is incorrect</li>
              </ul>
              <div className="flex justify-center gap-2">
                <Button onClick={refreshIframe} variant="outline">
                  Try Again
                </Button>
                <Button onClick={openInNewTab}>
                  Open in New Tab
                </Button>
              </div>
            </div>
          </div>
        )}

        <iframe
          src={wordpressUrl}
          className="w-full h-full border-0"
          title="WordPress Theme Preview"
          allow="fullscreen"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
        />
      </div>
    </main>
  );
}
