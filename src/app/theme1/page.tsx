'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ExternalLink, RefreshCw, X, Upload, ArrowRight, Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCustomization } from '@/hooks/useCustomization';
import { getWordPressUrl } from '@/lib/wordpress-api';

export default function Theme1Page() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notification, setNotification] = useState(null);
  const iframeRef = useRef(null);

  // Use customization hook for WordPress integration
  const {
    customizations,
    loading,
    errors,
    uploadProgress,
    hasUnsavedChanges,
    saveCustomizations,
    uploadLogoFile,
    updateColors,
    updateFonts,
    clearErrors,
    setIframeRef
  } = useCustomization('theme1');

  const wordpressUrl = getWordPressUrl();

  // Font pairs data
  const fontPairs = [
    { id: 'quicksand-work', name: 'Quicksand & Work Sans', preview: 'Ag' },
    { id: 'roboto-open', name: 'Roboto & Open Sans', preview: 'Ag' },
    { id: 'lato-source', name: 'Lato & Source Sans', preview: 'Ag' },
    { id: 'montserrat-lato', name: 'Montserrat & Lato', preview: 'Ag' },
    { id: 'poppins-inter', name: 'Poppins & Inter', preview: 'Ag' },
    { id: 'playfair-source', name: 'Playfair & Source Sans', preview: 'Ag' },
    { id: 'merriweather-open', name: 'Merriweather & Open Sans', preview: 'Ag' },
    { id: 'oswald-lato', name: 'Oswald & Lato', preview: 'Ag' },
  ];

  // Color palettes data
  const colorPalettes = [
    { id: 'original', name: 'Original', colors: ['#10b981', '#059669'] },
    { id: 'purple', name: 'Purple', colors: ['#8b5cf6', '#7c3aed'] },
    { id: 'blue', name: 'Blue', colors: ['#3b82f6', '#2563eb'] },
    { id: 'green', name: 'Green', colors: ['#22c55e', '#16a34a'] },
    { id: 'red', name: 'Red', colors: ['#ef4444', '#dc2626'] },
    { id: 'orange', name: 'Orange', colors: ['#f97316', '#ea580c'] },
    { id: 'pink', name: 'Pink', colors: ['#ec4899', '#db2777'] },
    { id: 'cyan', name: 'Cyan', colors: ['#06b6d4', '#0891b2'] },
    { id: 'teal', name: 'Teal', colors: ['#14b8a6', '#0d9488'] },
    { id: 'indigo', name: 'Indigo', colors: ['#6366f1', '#4f46e5'] },
    { id: 'yellow', name: 'Yellow', colors: ['#eab308', '#ca8a04'] },
    { id: 'slate', name: 'Slate', colors: ['#64748b', '#475569'] },
  ];

  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const refreshIframe = () => {
    setIsLoading(true);
    setHasError(false);
    // Force iframe reload by changing the src
    const iframe = document.querySelector('iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.src = iframe.src;
    }
  };

  const openInNewTab = () => {
    window.open(wordpressUrl, '_blank');
  };

  // Set iframe reference when component mounts
  useEffect(() => {
    if (iframeRef.current) {
      setIframeRef(iframeRef.current);
    }
  }, [setIframeRef]);

  // Show notifications for errors and success
  useEffect(() => {
    if (errors.upload) {
      setNotification({ type: 'error', message: errors.upload });
    } else if (errors.save) {
      setNotification({ type: 'error', message: errors.save });
    } else if (errors.preview) {
      setNotification({ type: 'error', message: errors.preview });
    }
  }, [errors]);

  // Clear notifications after 5 seconds
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null);
        clearErrors();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification, clearErrors]);

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const result = await uploadLogoFile(file);
      if (result.success) {
        setNotification({ type: 'success', message: 'Logo uploaded successfully!' });
      }
    }
  };

  const handleFontChange = async (fontId: string) => {
    const selectedFont = fontPairs.find(f => f.id === fontId);
    if (selectedFont) {
      await updateFonts({
        primary: selectedFont.name.split(' & ')[0],
        secondary: selectedFont.name.split(' & ')[1],
        pair_id: fontId
      });
    }
  };

  const handleColorChange = async (paletteId: string) => {
    const selectedPalette = colorPalettes.find(p => p.id === paletteId);
    if (selectedPalette) {
      await updateColors({
        primary: selectedPalette.colors[0],
        secondary: selectedPalette.colors[1],
        palette_id: paletteId
      });
    }
  };

  const handleSaveCustomizations = async () => {
    const result = await saveCustomizations();
    if (result.success) {
      setNotification({ type: 'success', message: 'Customizations saved successfully!' });
    }
  };

  return (
    <main className="relative min-h-screen bg-gray-50">
      {/* Customization Toggle Button */}
      <Button
        onClick={() => setSidebarOpen(!sidebarOpen)}
        className={`fixed z-50 text-white shadow-lg top-4 transition-all duration-300 bg-slate-800 hover:bg-slate-700 ${
          sidebarOpen ? 'left-[21rem] sm:left-[25rem]' : 'left-4'
        }`}
        size="icon"
      >
        <span className="text-lg font-bold">{sidebarOpen ? '‹' : '›'}</span>
      </Button>

      {/* Content */}
      <div className="relative" style={{ height: '100vh' }}>
        {isLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin" />
              <p className="text-gray-600">Loading WordPress theme...</p>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="max-w-md p-6 mx-auto text-center">
              <div className="mb-4 text-red-500">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-semibold">Unable to Load WordPress Site</h3>
              <p className="mb-4 text-gray-600">
                The WordPress site at {wordpressUrl} could not be loaded. This might be due to:
              </p>
              <ul className="mb-4 text-sm text-left text-gray-500">
                <li>• The server is not running</li>
                <li>• Network connectivity issues</li>
                <li>• CORS or security restrictions</li>
                <li>• The URL is incorrect</li>
              </ul>
              <div className="flex justify-center gap-2">
                <Button onClick={refreshIframe} variant="outline">
                  Try Again
                </Button>
                <Button onClick={openInNewTab}>
                  Open in New Tab
                </Button>
              </div>
            </div>
          </div>
        )}

        <iframe
          ref={iframeRef}
          src={wordpressUrl}
          className="w-full h-full border-0"
          title="WordPress Theme Preview"
          allow="fullscreen"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
        />
      </div>

      {/* Customization Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full w-80 sm:w-96 bg-slate-800 text-white shadow-2xl transform transition-transform duration-300 ease-in-out z-40 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-slate-700">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 bg-orange-500 rounded-lg">
                <span className="text-sm font-bold text-white">🎨</span>
              </div>
            </div>
            <Button
              onClick={() => setSidebarOpen(false)}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-slate-700"
            >
              <X size={16} />
            </Button>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 space-y-6 overflow-y-auto">
            {/* Site Logo Section */}
            <div>
              <h3 className="mb-3 text-sm font-medium">Site Logo</h3>
              {customizations.logo ? (
                <div className="space-y-3">
                  <div className="p-4 border rounded-lg border-slate-600 bg-slate-700">
                    <Image
                      src={customizations.logo.url}
                      alt={customizations.logo.alt_text || 'Current logo'}
                      width={200}
                      height={80}
                      className="object-contain w-full h-20 rounded"
                    />
                  </div>
                  <label className="block">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      disabled={loading.uploading}
                      className="hidden"
                    />
                    <div className="p-3 text-center transition-colors border border-dashed rounded-lg cursor-pointer border-slate-600 hover:border-slate-500">
                      <p className="text-xs text-slate-400">
                        {loading.uploading ? 'Uploading...' : 'Change Logo'}
                      </p>
                    </div>
                  </label>
                </div>
              ) : (
                <label className="block">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    disabled={loading.uploading}
                    className="hidden"
                  />
                  <div className="p-6 text-center transition-colors border-2 border-dashed rounded-lg cursor-pointer border-slate-600 hover:border-slate-500">
                    {loading.uploading ? (
                      <Loader2 size={20} className="mx-auto mb-2 text-slate-400 animate-spin" />
                    ) : (
                      <Upload size={20} className="mx-auto mb-2 text-slate-400" />
                    )}
                    <p className="text-sm text-slate-400">
                      {loading.uploading ? 'Uploading...' : 'Upload File Here'}
                    </p>
                  </div>
                </label>
              )}
            </div>

            {/* Font Pair Section */}
            <div>
              <h3 className="mb-3 text-sm font-medium">
                Font Pair: {fontPairs.find(f => f.id === customizations.fonts?.pair_id)?.name || 'Quicksand & Work Sans'}
              </h3>
              <div className="grid grid-cols-4 gap-2">
                {fontPairs.slice(0, 8).map((font) => (
                  <button
                    key={font.id}
                    onClick={() => handleFontChange(font.id)}
                    disabled={loading.previewing}
                    className={`aspect-square rounded-lg border-2 flex items-center justify-center text-lg font-bold transition-colors ${
                      customizations.fonts?.pair_id === font.id
                        ? 'border-orange-500 bg-slate-700'
                        : 'border-slate-600 hover:border-slate-500'
                    } ${loading.previewing ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {loading.previewing && customizations.fonts?.pair_id === font.id ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      font.preview
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Color Palette Section */}
            <div>
              <h3 className="mb-3 text-sm font-medium">
                Color Palette: {colorPalettes.find(p => p.id === customizations.colors?.palette_id)?.name || 'Original'}
              </h3>
              <div className="grid grid-cols-2 gap-3">
                {colorPalettes.map((palette) => (
                  <button
                    key={palette.id}
                    onClick={() => handleColorChange(palette.id)}
                    disabled={loading.previewing}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      customizations.colors?.palette_id === palette.id
                        ? 'border-orange-500 bg-slate-700'
                        : 'border-slate-600 hover:border-slate-500'
                    } ${loading.previewing ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <div className="flex gap-1.5 justify-center">
                      {palette.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 border rounded-full border-slate-500"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    {loading.previewing && customizations.colors?.palette_id === palette.id && (
                      <div className="flex justify-center mt-2">
                        <Loader2 size={12} className="text-orange-500 animate-spin" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 space-y-3 border-t border-slate-700">
            {/* Upload Progress */}
            {loading.uploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Uploading logo...</span>
                  <span>{Math.round(uploadProgress)}%</span>
                </div>
                <div className="w-full h-2 rounded-full bg-slate-700">
                  <div
                    className="h-2 transition-all duration-300 bg-orange-500 rounded-full"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
              </div>
            )}

            {/* Save Button */}
            <Button
              onClick={handleSaveCustomizations}
              disabled={loading.saving || !hasUnsavedChanges}
              className="w-full text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading.saving ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  {hasUnsavedChanges ? 'Save Changes' : 'Saved'}
                  <ArrowRight size={16} className="ml-2" />
                </>
              )}
            </Button>

            {/* Back to Themes */}
            <button
              onClick={() => router.push('/themes')}
              className="w-full text-sm transition-colors text-slate-400 hover:text-white"
            >
              Back to Other Designs
            </button>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Notification */}
      {notification && (
        <div className="fixed z-50 max-w-sm top-4 right-4">
          <div
            className={`p-4 rounded-lg shadow-lg border ${
              notification.type === 'error'
                ? 'bg-red-50 border-red-200 text-red-800'
                : 'bg-green-50 border-green-200 text-green-800'
            }`}
          >
            <div className="flex items-center gap-2">
              {notification.type === 'error' ? (
                <AlertCircle size={20} className="text-red-500" />
              ) : (
                <CheckCircle size={20} className="text-green-500" />
              )}
              <p className="text-sm font-medium">{notification.message}</p>
              <button
                onClick={() => setNotification(null)}
                className="ml-auto text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading Overlay for Initial Load */}
      {loading.initial && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90">
          <div className="text-center">
            <Loader2 className="w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin" />
            <p className="text-gray-600">Loading customizations...</p>
          </div>
        </div>
      )}
    </main>
  );
}
