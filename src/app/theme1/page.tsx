'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ExternalLink, RefreshCw, Settings, X, Upload, ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function Theme1Page() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedFont, setSelectedFont] = useState('quicksand-work');
  const [selectedPalette, setSelectedPalette] = useState('original');
  const wordpressUrl = 'http://192.168.29.49/wordpress/';

  // Font pairs data
  const fontPairs = [
    { id: 'quicksand-work', name: 'Quicksand & Work Sans', preview: 'Ag' },
    { id: 'roboto-open', name: 'Roboto & Open Sans', preview: 'Ag' },
    { id: 'lato-source', name: 'Lato & Source Sans', preview: 'Ag' },
    { id: 'montserrat-lato', name: 'Mont<PERSON>rat & Lato', preview: 'Ag' },
    { id: 'poppins-inter', name: 'Poppins & Inter', preview: 'Ag' },
    { id: 'playfair-source', name: 'Playfair & Source Sans', preview: 'Ag' },
    { id: 'merriweather-open', name: 'Merriweather & Open Sans', preview: 'Ag' },
    { id: 'oswald-lato', name: 'Oswald & Lato', preview: 'Ag' },
  ];

  // Color palettes data
  const colorPalettes = [
    { id: 'original', name: 'Original', colors: ['#10b981', '#059669'] },
    { id: 'purple', name: 'Purple', colors: ['#8b5cf6', '#7c3aed'] },
    { id: 'blue', name: 'Blue', colors: ['#3b82f6', '#2563eb'] },
    { id: 'green', name: 'Green', colors: ['#22c55e', '#16a34a'] },
    { id: 'red', name: 'Red', colors: ['#ef4444', '#dc2626'] },
    { id: 'orange', name: 'Orange', colors: ['#f97316', '#ea580c'] },
    { id: 'pink', name: 'Pink', colors: ['#ec4899', '#db2777'] },
    { id: 'cyan', name: 'Cyan', colors: ['#06b6d4', '#0891b2'] },
    { id: 'teal', name: 'Teal', colors: ['#14b8a6', '#0d9488'] },
    { id: 'indigo', name: 'Indigo', colors: ['#6366f1', '#4f46e5'] },
    { id: 'yellow', name: 'Yellow', colors: ['#eab308', '#ca8a04'] },
    { id: 'slate', name: 'Slate', colors: ['#64748b', '#475569'] },
  ];

  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const refreshIframe = () => {
    setIsLoading(true);
    setHasError(false);
    // Force iframe reload by changing the src
    const iframe = document.querySelector('iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.src = iframe.src;
    }
  };

  const openInNewTab = () => {
    window.open(wordpressUrl, '_blank');
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Handle file upload logic here
      console.log('Logo file selected:', file.name);
    }
  };

  return (
    <main className="relative min-h-screen bg-gray-50">
      {/* Customization Toggle Button */}
      <Button
        onClick={() => setSidebarOpen(!sidebarOpen)}
        className="fixed z-50 text-white shadow-lg top-4 right-4 bg-slate-800 hover:bg-slate-700"
        size="sm"
      >
        <Settings size={16} />
        Customize
      </Button>

      {/* Content */}
      <div className="relative" style={{ height: '100vh' }}>
        {isLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="text-center">
              <RefreshCw className="w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin" />
              <p className="text-gray-600">Loading WordPress theme...</p>
            </div>
          </div>
        )}

        {hasError && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white">
            <div className="max-w-md p-6 mx-auto text-center">
              <div className="mb-4 text-red-500">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-semibold">Unable to Load WordPress Site</h3>
              <p className="mb-4 text-gray-600">
                The WordPress site at {wordpressUrl} could not be loaded. This might be due to:
              </p>
              <ul className="mb-4 text-sm text-left text-gray-500">
                <li>• The server is not running</li>
                <li>• Network connectivity issues</li>
                <li>• CORS or security restrictions</li>
                <li>• The URL is incorrect</li>
              </ul>
              <div className="flex justify-center gap-2">
                <Button onClick={refreshIframe} variant="outline">
                  Try Again
                </Button>
                <Button onClick={openInNewTab}>
                  Open in New Tab
                </Button>
              </div>
            </div>
          </div>
        )}

        <iframe
          src={wordpressUrl}
          className="w-full h-full border-0"
          title="WordPress Theme Preview"
          allow="fullscreen"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
        />
      </div>

      {/* Customization Sidebar */}
      <div
        className={`fixed top-0 right-0 h-full w-80 sm:w-96 bg-slate-800 text-white shadow-2xl transform transition-transform duration-300 ease-in-out z-40 ${
          sidebarOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-slate-700">
            <div className="flex items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 bg-orange-500 rounded-lg">
                <span className="text-sm font-bold text-white">🎨</span>
              </div>
            </div>
            <Button
              onClick={() => setSidebarOpen(false)}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-slate-700"
            >
              <X size={16} />
            </Button>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 space-y-6 overflow-y-auto">
            {/* Site Logo Section */}
            <div>
              <h3 className="mb-3 text-sm font-medium">Site Logo</h3>
              <label className="block">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleLogoUpload}
                  className="hidden"
                />
                <div className="p-6 text-center transition-colors border-2 border-dashed rounded-lg cursor-pointer border-slate-600 hover:border-slate-500">
                  <Upload size={20} className="mx-auto mb-2 text-slate-400" />
                  <p className="text-sm text-slate-400">Upload File Here</p>
                </div>
              </label>
            </div>

            {/* Font Pair Section */}
            <div>
              <h3 className="mb-3 text-sm font-medium">
                Font Pair: {fontPairs.find(f => f.id === selectedFont)?.name || 'Quicksand & Work Sans'}
              </h3>
              <div className="grid grid-cols-4 gap-2">
                {fontPairs.slice(0, 8).map((font) => (
                  <button
                    key={font.id}
                    onClick={() => setSelectedFont(font.id)}
                    className={`aspect-square rounded-lg border-2 flex items-center justify-center text-lg font-bold transition-colors ${
                      selectedFont === font.id
                        ? 'border-orange-500 bg-slate-700'
                        : 'border-slate-600 hover:border-slate-500'
                    }`}
                  >
                    {font.preview}
                  </button>
                ))}
              </div>
            </div>

            {/* Color Palette Section */}
            <div>
              <h3 className="mb-3 text-sm font-medium">
                Color Palette: {colorPalettes.find(p => p.id === selectedPalette)?.name || 'Original'}
              </h3>
              <div className="grid grid-cols-2 gap-3">
                {colorPalettes.map((palette) => (
                  <button
                    key={palette.id}
                    onClick={() => setSelectedPalette(palette.id)}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      selectedPalette === palette.id
                        ? 'border-orange-500 bg-slate-700'
                        : 'border-slate-600 hover:border-slate-500'
                    }`}
                  >
                    <div className="flex gap-1.5 justify-center">
                      {palette.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 border rounded-full border-slate-500"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 space-y-3 border-t border-slate-700">
            <Button className="w-full text-white bg-indigo-600 hover:bg-indigo-700">
              Continue
              <ArrowRight size={16} className="ml-2" />
            </Button>
            <button
              onClick={() => router.push('/themes')}
              className="w-full text-sm transition-colors text-slate-400 hover:text-white"
            >
              Back to Other Designs
            </button>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </main>
  );
}
