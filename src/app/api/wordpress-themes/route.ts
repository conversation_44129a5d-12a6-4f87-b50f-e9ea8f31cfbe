import { NextResponse } from 'next/server';

// WordPress.org API endpoints
const WP_API_BASE = 'https://api.wordpress.org/themes/info/1.2/';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const keywordsParam = searchParams.get('keywords') || '';
  const page = parseInt(searchParams.get('page') || '1', 10);
  const perPage = parseInt(searchParams.get('perPage') || '12', 10);

  console.log('==== WordPress Themes API Request ====');
  console.log(`URL: ${request.url}`);
  console.log(`Keywords: "${keywordsParam}"`);
  console.log(`Page: ${page}, PerPage: ${perPage}`);

  if (!keywordsParam) {
    console.log('Error: Keywords parameter is required');
    return NextResponse.json({ error: 'Keywords parameter is required' }, { status: 400 });
  }

  try {
    // Split the keywords string into an array and limit to 3 keywords
    const allKeywords = keywordsParam.split(',');
    // Take only the first 3 keywords
    const keywordsArray = allKeywords.slice(0, 3).map(k => k.trim()).filter(Boolean);
    // Use the first keyword as the main search term
    const mainKeyword = keywordsArray[0] || '';

    // Check if we have at least one valid keyword
    if (!mainKeyword) {
      console.log('No valid keywords found after filtering');
      return NextResponse.json({
        error: 'No valid keywords provided',
        info: { page: 1, pages: 0, results: 0 },
        themes: []
      }, { status: 400 });
    }

    console.log(`Main keyword for search: "${mainKeyword}"`);
    console.log(`Using top 3 keywords: ${JSON.stringify(keywordsArray)}`);
    console.log(`Original keywords: ${JSON.stringify(allKeywords)}`);

    // Prepare the request to WordPress.org API
    // The API expects a specific format
    const requestBody = {
      action: 'query_themes',
      request: {
        search: mainKeyword,
        page,
        per_page: perPage,
        browse: 'popular', // Add a browse parameter to ensure we get results
        fields: {
          description: true,
          screenshot_url: true,
          rating: true,
          downloaded: true,
          last_updated: true,
          homepage: true,
          tags: true
        }
      }
    };

    console.log('WordPress.org API request body:', JSON.stringify(requestBody, null, 2));

    // Make the request to WordPress.org API
    // The API documentation says it only accepts GET requests, so let's convert our request to a GET
    console.log(`Sending request to: ${WP_API_BASE}`);

    // Build query parameters for GET request
    const queryParams = new URLSearchParams({
      action: 'query_themes',
      request: JSON.stringify(requestBody.request)
    });

    const url = `${WP_API_BASE}?${queryParams.toString()}`;
    console.log(`GET request URL: ${url}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'WordPress Theme Browser/1.0'
      }
    });

    // Log the response status
    console.log(`WordPress.org API response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`WordPress.org API error response: ${errorText}`);
      throw new Error(`WordPress.org API error: ${response.status}`);
    }

    const data = await response.json();

    // Log the response structure in more detail
    console.log('WordPress.org API response keys:', Object.keys(data));
    console.log('Response info property:', data.info ? JSON.stringify(data.info, null, 2) : 'No info property');
    console.log('Response themes property:', data.themes ? `Found ${Object.keys(data.themes).length} themes` : 'No themes property');

    // If we don't have themes or info, return an error response
    if (!data.themes || !data.info) {
      console.log('No themes or info found in response, returning error');
      return NextResponse.json({
        error: 'No themes found for the provided keywords',
        info: { page: 1, pages: 0, results: 0 },
        themes: []
      }, { status: 404 });
    }

    // Format the response
    console.log(`Returning ${Object.keys(data.themes).length} themes from WordPress.org API`);
    return NextResponse.json({
      info: {
        page: data.info.page,
        pages: data.info.pages,
        results: data.info.results
      },
      themes: data.themes || []
    });
  } catch (error) {
    console.error('Error fetching themes from WordPress.org:', error);

    // Return error response instead of fallback themes
    console.log('Returning error response');
    return NextResponse.json({
      error: 'Failed to fetch themes from WordPress.org API',
      info: { page: 1, pages: 0, results: 0 },
      themes: []
    }, { status: 500 });
  }
}
