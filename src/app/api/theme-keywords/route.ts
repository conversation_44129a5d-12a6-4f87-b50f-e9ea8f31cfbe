import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const runtime = 'edge';

/**
 * POST /api/theme-keywords
 * Body: { messages: any[] }
 * Returns: { keywords: string[] }
 *
 * 1. Receives the full conversation history in the request body (no conversationId)
 * 2. Uses OpenAI to extract 3-7 keywords relevant to WordPress themes (design, features, business type, etc)
 * 3. Returns keywords as a JSON array for use with the WordPress API
 */
export async function POST(req: Request) {
  try {
    console.log('[theme-keywords] API called');
    const { messages } = await req.json();
    console.log('[theme-keywords] Received messages:', Array.isArray(messages) ? messages.length : 'none');
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.log('[theme-keywords] No conversation history provided');
      return NextResponse.json({ error: 'No conversation history provided' }, { status: 400 });
    }

    // Prompt for extracting WordPress theme keywords - limit to exactly 3 keywords
    const prompt = `Given the following conversation between a user and an AI assistant about building a WordPress website, extract EXACTLY 3 keywords that are most relevant for searching WordPress themes. Focus on business type, design preferences, features, and any style or industry cues. Return ONLY a JSON array of strings with exactly 3 keywords (no explanation).\n\nConversation:\n${messages.map((m: any) => `${m.role.toUpperCase()}: ${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('\n')}`;

    console.log('[theme-keywords] Sending prompt to OpenAI');
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      response_format: { type: 'json_object' },
    });

    const content = response.choices[0].message?.content || '{"keywords":["business"]}';
    let parsed;
    try {
      parsed = JSON.parse(content);
    } catch (e) {
      console.log('[theme-keywords] Failed to parse OpenAI response:', content);
      parsed = { keywords: ["business"] };
    }

    // Ensure we have exactly 3 keywords
    let keywords = Array.isArray(parsed.keywords) ? parsed.keywords : ["business"];

    // If we have fewer than 3 keywords, add default ones to reach 3
    if (keywords.length < 3) {
      const defaultKeywords = ["business", "modern", "professional"];

      // Add default keywords that aren't already in the list
      for (const defaultKeyword of defaultKeywords) {
        if (keywords.length < 3 && !keywords.includes(defaultKeyword)) {
          keywords.push(defaultKeyword);
        }
      }

      // If we still don't have 3 keywords (unlikely), just use the defaults
      if (keywords.length < 3) {
        keywords = defaultKeywords.slice(0, 3);
      }
    }
    // If we have more than 3 keywords, take only the first 3
    else if (keywords.length > 3) {
      keywords = keywords.slice(0, 3);
    }

    console.log('[theme-keywords] Extracted keywords (limited to 3):', keywords);
    return NextResponse.json({ keywords });
  } catch (error: any) {
    console.error('[theme-keywords] Error extracting theme keywords:', error);
    return NextResponse.json({ error: 'Failed to extract theme keywords', keywords: ["business", "modern", "professional"] }, { status: 500 });
  }
}
