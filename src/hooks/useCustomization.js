/**
 * Custom hook for managing theme customization state and API interactions
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  getThemeCustomizations,
  saveThemeCustomizations,
  previewThemeCustomizations,
  uploadLogo,
  sendCustomizationToIframe,
  setupIframeMessageListener,
  validateCustomizations
} from '@/lib/wordpress-api';

export const useCustomization = (themeId = 'default-theme') => {
  // State management
  const [customizations, setCustomizations] = useState({
    colors: {
      primary: '#3b82f6',
      secondary: '#6366f1',
      accent: '#ec4899'
    },
    fonts: {
      primary: 'Inter',
      secondary: 'Open Sans',
      pair_id: 'inter-opensans'
    },
    logo: null
  });

  const [loading, setLoading] = useState({
    initial: true,
    saving: false,
    uploading: false,
    previewing: false
  });

  const [errors, setErrors] = useState({
    load: null,
    save: null,
    upload: null,
    preview: null
  });

  const [uploadProgress, setUploadProgress] = useState(0);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const iframeRef = useRef(null);
  const cleanupMessageListener = useRef(null);

  /**
   * Load initial customizations from WordPress
   */
  const loadCustomizations = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, initial: true }));
      setErrors(prev => ({ ...prev, load: null }));

      const data = await getThemeCustomizations(themeId);
      
      if (validateCustomizations(data)) {
        setCustomizations(data);
      } else {
        console.warn('Invalid customization data received, using defaults');
      }
    } catch (error) {
      console.error('Failed to load customizations:', error);
      setErrors(prev => ({ ...prev, load: error.message }));
    } finally {
      setLoading(prev => ({ ...prev, initial: false }));
    }
  }, [themeId]);

  /**
   * Save customizations to WordPress
   */
  const saveCustomizations = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, saving: true }));
      setErrors(prev => ({ ...prev, save: null }));

      if (!validateCustomizations(customizations)) {
        throw new Error('Invalid customization data');
      }

      await saveThemeCustomizations(themeId, customizations);
      setHasUnsavedChanges(false);
      
      return { success: true };
    } catch (error) {
      console.error('Failed to save customizations:', error);
      setErrors(prev => ({ ...prev, save: error.message }));
      return { success: false, error: error.message };
    } finally {
      setLoading(prev => ({ ...prev, saving: false }));
    }
  }, [themeId, customizations]);

  /**
   * Preview customizations without saving
   */
  const previewCustomizations = useCallback(async (previewData = null) => {
    try {
      setLoading(prev => ({ ...prev, previewing: true }));
      setErrors(prev => ({ ...prev, preview: null }));

      const dataToPreview = previewData || customizations;
      
      if (!validateCustomizations(dataToPreview)) {
        throw new Error('Invalid customization data for preview');
      }

      // Send to WordPress API for server-side preview
      await previewThemeCustomizations(dataToPreview);

      // Send to iframe for real-time preview
      if (iframeRef.current) {
        sendCustomizationToIframe(iframeRef.current, dataToPreview);
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to preview customizations:', error);
      setErrors(prev => ({ ...prev, preview: error.message }));
      return { success: false, error: error.message };
    } finally {
      setLoading(prev => ({ ...prev, previewing: false }));
    }
  }, [customizations]);

  /**
   * Upload logo file
   */
  const uploadLogoFile = useCallback(async (file) => {
    try {
      setLoading(prev => ({ ...prev, uploading: true }));
      setErrors(prev => ({ ...prev, upload: null }));
      setUploadProgress(0);

      const result = await uploadLogo(file, (progress) => {
        setUploadProgress(progress);
      });

      // Update customizations with new logo
      const newCustomizations = {
        ...customizations,
        logo: {
          url: result.url,
          attachment_id: result.attachment_id,
          alt_text: result.alt_text || file.name
        }
      };

      setCustomizations(newCustomizations);
      setHasUnsavedChanges(true);

      // Preview the new logo immediately
      await previewCustomizations(newCustomizations);

      return { success: true, data: result };
    } catch (error) {
      console.error('Failed to upload logo:', error);
      setErrors(prev => ({ ...prev, upload: error.message }));
      return { success: false, error: error.message };
    } finally {
      setLoading(prev => ({ ...prev, uploading: false }));
      setUploadProgress(0);
    }
  }, [customizations, previewCustomizations]);

  /**
   * Update color palette
   */
  const updateColors = useCallback(async (colors) => {
    const newCustomizations = {
      ...customizations,
      colors: { ...customizations.colors, ...colors }
    };

    setCustomizations(newCustomizations);
    setHasUnsavedChanges(true);

    // Preview changes immediately
    await previewCustomizations(newCustomizations);
  }, [customizations, previewCustomizations]);

  /**
   * Update font pair
   */
  const updateFonts = useCallback(async (fonts) => {
    const newCustomizations = {
      ...customizations,
      fonts: { ...customizations.fonts, ...fonts }
    };

    setCustomizations(newCustomizations);
    setHasUnsavedChanges(true);

    // Preview changes immediately
    await previewCustomizations(newCustomizations);
  }, [customizations, previewCustomizations]);

  /**
   * Reset customizations to defaults
   */
  const resetCustomizations = useCallback(async () => {
    const defaultCustomizations = {
      colors: {
        primary: '#3b82f6',
        secondary: '#6366f1',
        accent: '#ec4899'
      },
      fonts: {
        primary: 'Inter',
        secondary: 'Open Sans',
        pair_id: 'inter-opensans'
      },
      logo: null
    };

    setCustomizations(defaultCustomizations);
    setHasUnsavedChanges(true);

    // Preview reset changes
    await previewCustomizations(defaultCustomizations);
  }, [previewCustomizations]);

  /**
   * Clear all errors
   */
  const clearErrors = useCallback(() => {
    setErrors({
      load: null,
      save: null,
      upload: null,
      preview: null
    });
  }, []);

  /**
   * Set iframe reference for communication
   */
  const setIframeRef = useCallback((iframe) => {
    iframeRef.current = iframe;
  }, []);

  // Load initial customizations on mount
  useEffect(() => {
    loadCustomizations();
  }, [loadCustomizations]);

  // Set up iframe message listener
  useEffect(() => {
    const cleanup = setupIframeMessageListener((message) => {
      console.log('Received message from WordPress iframe:', message);
      
      // Handle different message types from WordPress
      switch (message.type) {
        case 'CUSTOMIZATION_APPLIED':
          console.log('Customization successfully applied in WordPress');
          break;
        case 'CUSTOMIZATION_ERROR':
          console.error('Customization error in WordPress:', message.error);
          setErrors(prev => ({ ...prev, preview: message.error }));
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    });

    cleanupMessageListener.current = cleanup;

    return () => {
      if (cleanupMessageListener.current) {
        cleanupMessageListener.current();
      }
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupMessageListener.current) {
        cleanupMessageListener.current();
      }
    };
  }, []);

  return {
    // State
    customizations,
    loading,
    errors,
    uploadProgress,
    hasUnsavedChanges,

    // Actions
    loadCustomizations,
    saveCustomizations,
    previewCustomizations,
    uploadLogoFile,
    updateColors,
    updateFonts,
    resetCustomizations,
    clearErrors,
    setIframeRef,

    // Utilities
    isLoading: Object.values(loading).some(Boolean),
    hasErrors: Object.values(errors).some(Boolean)
  };
};
