import {
  ActionBarPrimitive,
  BranchPickerPrimitive,
  ComposerPrimitive,
  MessagePrimitive,
  ThreadPrimitive,
  useThread,
  useComposerRuntime,
} from "@assistant-ui/react";
import type { FC } from "react";
import {
  ArrowDownIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  PencilIcon,
  RefreshCwIcon,
  SendHorizontalIcon,
  LayoutGridIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import React, { useState, useMemo, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { MarkdownText } from "@/components/assistant-ui/markdown-text";
import { TooltipIconButton } from "@/components/assistant-ui/tooltip-icon-button";
import { ImageSelector } from "@/components/assistant-ui/image-selector";
import { PexelsImage, extractKeywordsFromConversation } from "@/lib/pexels-api";

export const Thread: FC = () => {
  return (
    <ThreadPrimitive.Root
      className="box-border flex flex-col h-full overflow-hidden bg-background"
      style={{
        ["--thread-max-width" as string]: "42rem",
      }}
    >
      <ThreadPrimitive.Viewport className="flex flex-col items-center h-full px-4 pt-8 overflow-y-scroll scroll-smooth bg-inherit">
        <ThreadWelcome />

        <ThreadPrimitive.Messages
          components={{
            UserMessage: UserMessage,
            EditComposer: EditComposer,
            AssistantMessage: AssistantMessage,
          }}
        />

        <ThreadPrimitive.If empty={false}>
          <div className="flex-grow min-h-8" />
        </ThreadPrimitive.If>

        <div className="sticky bottom-0 mt-3 flex w-full max-w-[var(--thread-max-width)] flex-col items-center justify-end rounded-t-lg bg-inherit pb-4">
          <ThreadScrollToBottom />
          <Composer />
        </div>
      </ThreadPrimitive.Viewport>
    </ThreadPrimitive.Root>
  );
};

const ThreadScrollToBottom: FC = () => {
  return (
    <ThreadPrimitive.ScrollToBottom asChild>
      <TooltipIconButton
        tooltip="Scroll to bottom"
        variant="outline"
        className="absolute rounded-full -top-8 disabled:invisible"
      >
        <ArrowDownIcon />
      </TooltipIconButton>
    </ThreadPrimitive.ScrollToBottom>
  );
};

const ThreadWelcome: FC = () => {
  return (
    <ThreadPrimitive.Empty>
      <div className="flex w-full max-w-[var(--thread-max-width)] flex-grow flex-col">
        <div className="flex flex-col items-center justify-center flex-grow w-full">
          <p className="mt-4 font-medium">
            Hi,
            <br /> Let&apos;s get started with your site setup.
            <br />
            <b> First, tell me what your site is all about.</b>
          </p>
        </div>
        {/* <ThreadWelcomeSuggestions /> */}
      </div>
    </ThreadPrimitive.Empty>
  );
};

// Commented out unused component
/*const ThreadWelcomeSuggestions: FC = () => {
  return (
    <div className="flex items-stretch justify-center w-full gap-4 mt-3">
      <ThreadPrimitive.Suggestion
        className="flex flex-col items-center justify-center max-w-sm p-3 transition-colors ease-in border rounded-lg hover:bg-muted/80 grow basis-0"
        prompt="What is the weather in Tokyo?"
        method="replace"
        autoSend
      >
        <span className="text-sm font-semibold line-clamp-2 text-ellipsis">
          What is the weather in Tokyo?
        </span>
      </ThreadPrimitive.Suggestion>
      <ThreadPrimitive.Suggestion
        className="flex flex-col items-center justify-center max-w-sm p-3 transition-colors ease-in border rounded-lg hover:bg-muted/80 grow basis-0"
        prompt="What is assistant-ui?"
        method="replace"
        autoSend
      >
        <span className="text-sm font-semibold line-clamp-2 text-ellipsis">
          What is assistant-ui?
        </span>
      </ThreadPrimitive.Suggestion>
    </div>
  );
};*/

const Composer: FC = () => {
  const { messages } = useThread();
  const composerRuntime = useComposerRuntime();
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [showImageSelector, setShowImageSelector] = useState(false);
  const [selectedImages, setSelectedImages] = useState<PexelsImage[]>([]);
  const [keywords, setKeywords] = useState<string[]>(["business"]);

  // Detect if the last assistant message is specifically the contact info question
  const isContactInfoQuestion = useMemo(() => {
    if (!messages || messages.length === 0) return false;

    // Find the most recent assistant message
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      if (message.role === "assistant") {
        const content = Array.isArray(message.content)
          ? message.content
              .map((c: any) => (typeof c === "string" ? c : c.text || ""))
              .join(" ")
          : message.content;

        // Check if this most recent assistant message is the contact info question
        if (
          typeof content === "string" &&
          content.toLowerCase().includes("provide your contact information")
        ) {
          return true;
        }

        // If we found the most recent assistant message and it's not the contact question, return false
        return false;
      }
    }
    return false;
  }, [messages]);

  // Detect if the last assistant message is asking for image selection
  const isImageSelectionQuestion = useMemo(() => {
    if (!messages || messages.length === 0) return false;

    // Find the most recent assistant message
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      if (message.role === "assistant") {
        const content = Array.isArray(message.content)
          ? message.content
              .map((c: any) => (typeof c === "string" ? c : c.text || ""))
              .join(" ")
          : message.content;

        // Check if this most recent assistant message is asking for image selection
        if (
          typeof content === "string" &&
          (content.toLowerCase().includes("select the images") ||
            content.toLowerCase().includes("images you'd like to include"))
        ) {
          console.log("✅ Found image selection question!");
          return true;
        }

        // If we found the most recent assistant message and it's not the image selection question, return false
        return false;
      }
    }
    return false;
  }, [messages]);

  // Track if we've already processed the current image selection question
  const [processedImageQuestion, setProcessedImageQuestion] = useState(false);

  // Reset processedImageQuestion when new assistant messages are added
  useEffect(() => {
    // Check if the last message is from the assistant and it's not the image selection question
    if (messages && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.role === "assistant") {
        const content = Array.isArray(lastMessage.content)
          ? lastMessage.content
              .map((c: any) => (typeof c === "string" ? c : c.text || ""))
              .join(" ")
          : lastMessage.content;

        // If this is a new assistant message and not the image selection question,
        // reset the processed flag
        if (
          typeof content === "string" &&
          !content.toLowerCase().includes("select the images") &&
          !content.toLowerCase().includes("images you'd like to include")
        ) {
          console.log(
            "New assistant message detected, resetting processedImageQuestion"
          );
          setProcessedImageQuestion(false);
        }
      }
    }
  }, [messages]);

  // Show image selector when the image selection question is detected
  useEffect(() => {
    // Only proceed if we have an image selection question, the selector isn't already shown,
    // and we haven't already processed this question
    if (
      isImageSelectionQuestion &&
      !showImageSelector &&
      !processedImageQuestion
    ) {
      console.log(
        "⭐ Activating image selector based on direct question detection!"
      );

      // Mark this question as processed to prevent re-triggering
      setProcessedImageQuestion(true);

      // Extract keywords from the conversation for image search
      const getKeywords = async () => {
        try {
          // Use the async function to get keywords from the entire conversation
          const extractedKeywords = await extractKeywordsFromConversation(
            Array.from(messages)
          );
          console.log("Extracted keywords:", extractedKeywords);
          setKeywords(extractedKeywords);
          setShowImageSelector(true); // Actually show the component
        } catch (error) {
          console.error("Error getting keywords:", error);
          setKeywords(["business"]); // Fallback
          setShowImageSelector(true);
        }
      };

      getKeywords();
    }
  }, [
    isImageSelectionQuestion,
    showImageSelector,
    messages,
    processedImageQuestion,
  ]);

  // Handle image selection
  const handleImageSelection = (images: PexelsImage[]) => {
    // Set selected images and hide the selector
    setSelectedImages(images);
    setShowImageSelector(false);

    // Only proceed if we have images and a composer runtime
    if (composerRuntime && images.length > 0) {
      // Format images for message
      const imageUrls = images.map((img) => img.src.medium).join("\n");
      const messageText = `Selected images for my website:\n${imageUrls}`;

      // Set the text in the composer
      composerRuntime.setText(messageText);

      // Send immediately
      composerRuntime.send();

      // Reset states after sending
      setSelectedImages([]);
      setProcessedImageQuestion(false);
    } else {
      // Reset the processed flag even if no images were selected
      setProcessedImageQuestion(false);
    }
  };

  // Update the composer value when email or phone changes
  useEffect(() => {
    if (isContactInfoQuestion && composerRuntime) {
      composerRuntime.setText(
        email || phone ? `email : ${email}\nphone : ${phone}` : ""
      );
    }
  }, [email, phone, isContactInfoQuestion, composerRuntime]);

  // Validate inputs before enabling send button
  const isValid = email.length > 0 && phone.length > 0;

  // Show image selector when triggered
  if (showImageSelector) {
    return (
      <div className="w-full">
        <ImageSelector
          keywords={keywords}
          onSelectImages={handleImageSelection}
          onCancel={() => {
            setShowImageSelector(false);
            setProcessedImageQuestion(false); // Reset so we can process future image questions
          }}
        />
      </div>
    );
  }

  // Show image preview when images are selected
  if (selectedImages.length > 0) {
    return (
      <ComposerPrimitive.Root className="focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in">
        <div className="w-full p-3">
          <div className="flex flex-wrap gap-2 mb-3">
            {selectedImages.map((image) => (
              <div
                key={image.id}
                className="relative w-20 h-20 overflow-hidden rounded"
              >
                <Image
                  src={image.src.small}
                  alt={image.alt || "Selected image"}
                  className="object-cover"
                  fill
                  sizes="80px"
                />
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-500">
            {selectedImages.length} images selected
          </p>
        </div>
        <ThreadPrimitive.If running={false}>
          <ComposerPrimitive.Send asChild>
            <TooltipIconButton
              tooltip="Send"
              variant="default"
              className="my-2.5 size-8 p-2 transition-opacity ease-in"
            >
              <SendHorizontalIcon />
            </TooltipIconButton>
          </ComposerPrimitive.Send>
        </ThreadPrimitive.If>
        <ThreadPrimitive.If running>
          <ComposerPrimitive.Cancel asChild>
            <TooltipIconButton
              tooltip="Cancel"
              variant="default"
              className="my-2.5 size-8 p-2 transition-opacity ease-in"
            >
              <CircleStopIcon />
            </TooltipIconButton>
          </ComposerPrimitive.Cancel>
        </ThreadPrimitive.If>
      </ComposerPrimitive.Root>
    );
  }

  // Only show the specialized contact form for the contact info question
  if (isContactInfoQuestion) {
    return (
      <ComposerPrimitive.Root className="focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in">
        <div className="flex flex-col w-full gap-2 p-2">
          <input
            type="email"
            placeholder="Email"
            className="px-2 py-2 border rounded"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            autoFocus
          />
          <input
            type="tel"
            placeholder="Phone number"
            className="px-2 py-2 border rounded"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
          />
        </div>
        <ThreadPrimitive.If running={false}>
          <ComposerPrimitive.Send asChild disabled={!isValid}>
            <TooltipIconButton
              tooltip="Send"
              variant="default"
              className="my-2.5 size-8 p-2 transition-opacity ease-in"
              disabled={!isValid}
            >
              <SendHorizontalIcon />
            </TooltipIconButton>
          </ComposerPrimitive.Send>
        </ThreadPrimitive.If>
        <ThreadPrimitive.If running>
          <ComposerPrimitive.Cancel asChild>
            <TooltipIconButton
              tooltip="Cancel"
              variant="default"
              className="my-2.5 size-8 p-2 transition-opacity ease-in"
            >
              <CircleStopIcon />
            </TooltipIconButton>
          </ComposerPrimitive.Cancel>
        </ThreadPrimitive.If>
      </ComposerPrimitive.Root>
    );
  }

  // Otherwise show regular composer
  return (
    <ComposerPrimitive.Root className="focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in">
      <ComposerPrimitive.Input
        rows={1}
        autoFocus
        placeholder="Write your answer"
        className="flex-grow px-2 py-4 text-sm bg-transparent border-none outline-none resize-none placeholder:text-muted-foreground max-h-40 focus:ring-0 disabled:cursor-not-allowed"
      />
      <ComposerAction />
    </ComposerPrimitive.Root>
  );
};

const ComposerAction: FC = () => {
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <ComposerPrimitive.Send asChild>
          <TooltipIconButton
            tooltip="Send"
            variant="default"
            className="my-2.5 size-8 p-2 transition-opacity ease-in"
          >
            <SendHorizontalIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Send>
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <ComposerPrimitive.Cancel asChild>
          <TooltipIconButton
            tooltip="Cancel"
            variant="default"
            className="my-2.5 size-8 p-2 transition-opacity ease-in"
          >
            <CircleStopIcon />
          </TooltipIconButton>
        </ComposerPrimitive.Cancel>
      </ThreadPrimitive.If>
    </>
  );
};

const UserMessage: FC = () => {
  return (
    <MessagePrimitive.Root className="grid auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-2 [&:where(>*)]:col-start-2 w-full max-w-[var(--thread-max-width)] py-4">
      <UserActionBar />

      <div className="bg-muted text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words rounded-3xl px-5 py-2.5 col-start-2 row-start-2">
        <MessagePrimitive.Content />
      </div>

      <BranchPicker className="justify-end col-start-1 row-start-3 -mr-1 col-span-full" />
    </MessagePrimitive.Root>
  );
};

const UserActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      className="flex flex-col items-end col-start-1 row-start-2 mr-3 mt-2.5"
    >
      <ActionBarPrimitive.Edit asChild>
        <TooltipIconButton tooltip="Edit">
          <PencilIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Edit>
    </ActionBarPrimitive.Root>
  );
};

const EditComposer: FC = () => {
  return (
    <ComposerPrimitive.Root className="bg-muted my-4 flex w-full max-w-[var(--thread-max-width)] flex-col gap-2 rounded-xl">
      <ComposerPrimitive.Input className="flex w-full h-8 p-4 pb-0 bg-transparent outline-none resize-none text-foreground" />

      <div className="flex items-center self-end justify-center gap-2 mx-3 mb-3">
        <ComposerPrimitive.Cancel asChild>
          <Button variant="ghost">Cancel</Button>
        </ComposerPrimitive.Cancel>
        <ComposerPrimitive.Send asChild>
          <Button>Send</Button>
        </ComposerPrimitive.Send>
      </div>
    </ComposerPrimitive.Root>
  );
};

const AssistantMessage: FC = () => {
  const router = useRouter();
  const { messages } = useThread(); // removed conversationId
  const [showThemesButton, setShowThemesButton] = useState(false);
  const [loading, setLoading] = useState(false);

  // Check if the last user message contains image selection (question #5)
  useEffect(() => {
    if (!messages || messages.length === 0) return;

    // Look for a user message about image selection
    let foundImageSelection = false;
    let foundAssistantResponseAfterImages = false;

    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];

      // Check if this is a user message with image selection
      if (message.role === "user") {
        const content = Array.isArray(message.content)
          ? message.content
              .map((c: any) => (typeof c === "string" ? c : c.text || ""))
              .join(" ")
          : message.content;

        if (typeof content === "string" && content.toLowerCase().includes("selected images for my website")) {
          foundImageSelection = true;
        }
      }

      // Check if there's an assistant message after the image selection
      if (foundImageSelection && message.role === "assistant") {
        foundAssistantResponseAfterImages = true;
        break;
      }
    }

    setShowThemesButton(foundImageSelection && foundAssistantResponseAfterImages);
  }, [messages]);

  const navigateToThemes = async () => {
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      router.push("/themes");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch("/api/theme-keywords", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ messages }),
      });
      const data = await res.json();
      if (data.keywords && Array.isArray(data.keywords)) {
        // Pass keywords as query param
        const params = new URLSearchParams({ keywords: data.keywords.join(",") });
        router.push(`/themes?${params.toString()}`);
      } else {
        router.push("/themes");
      }
    } catch (e) {
      router.push("/themes");
    } finally {
      setLoading(false);
    }
  };

  return (
    <MessagePrimitive.Root className="grid grid-cols-[auto_auto_1fr] grid-rows-[auto_auto_1fr] relative w-full max-w-[var(--thread-max-width)] py-4">
      <div className="text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words leading-7 col-span-2 col-start-2 row-start-1 my-1.5">
        <MessagePrimitive.Content components={{ Text: MarkdownText }} />

        {showThemesButton && (
          <div className="mt-4">
            <Button
              onClick={navigateToThemes}
              className="flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700"
              disabled={loading}
            >
              <LayoutGridIcon size={16} />
              {loading ? "Loading..." : "View Website Themes"}
            </Button>
          </div>
        )}
      </div>

      <AssistantActionBar />

      <BranchPicker className="col-start-2 row-start-2 mr-2 -ml-2" />
    </MessagePrimitive.Root>
  );
};

const AssistantActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      autohideFloat="single-branch"
      className="text-muted-foreground flex gap-1 col-start-3 row-start-2 -ml-1 data-[floating]:bg-background data-[floating]:absolute data-[floating]:rounded-md data-[floating]:border data-[floating]:p-1 data-[floating]:shadow-sm"
    >
      <ActionBarPrimitive.Copy asChild>
        <TooltipIconButton tooltip="Copy">
          <MessagePrimitive.If copied>
            <CheckIcon />
          </MessagePrimitive.If>
          <MessagePrimitive.If copied={false}>
            <CopyIcon />
          </MessagePrimitive.If>
        </TooltipIconButton>
      </ActionBarPrimitive.Copy>
      <ActionBarPrimitive.Reload asChild>
        <TooltipIconButton tooltip="Refresh">
          <RefreshCwIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Reload>
    </ActionBarPrimitive.Root>
  );
};

const BranchPicker: FC<BranchPickerPrimitive.Root.Props> = ({
  className,
  ...rest
}) => {
  return (
    <BranchPickerPrimitive.Root
      hideWhenSingleBranch
      className={cn(
        "text-muted-foreground inline-flex items-center text-xs",
        className
      )}
      {...rest}
    >
      <BranchPickerPrimitive.Previous asChild>
        <TooltipIconButton tooltip="Previous">
          <ChevronLeftIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Previous>
      <span className="font-medium">
        <BranchPickerPrimitive.Number /> / <BranchPickerPrimitive.Count />
      </span>
      <BranchPickerPrimitive.Next asChild>
        <TooltipIconButton tooltip="Next">
          <ChevronRightIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Next>
    </BranchPickerPrimitive.Root>
  );
};

const CircleStopIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="currentColor"
      width="16"
      height="16"
    >
      <rect width="10" height="10" x="3" y="3" rx="2" />
    </svg>
  );
};
