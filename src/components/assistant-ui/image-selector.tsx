import React, { useState, useEffect } from "react";
import { PexelsImage, searchPexelsImages } from "@/lib/pexels-api";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

// Fallback sample images for development or when the API fails
const FALLBACK_IMAGES: PexelsImage[] = [
  {
    id: "fallback-1",
    width: 500,
    height: 300,
    url: "",
    alt: "Workout Equipment",
    photographer: "Sam<PERSON>",
    photographer_url: "",
    photographer_id: 0,
    avg_color: "#888888",
    src: {
      original: "/placeholder-gym-1.jpg",
      large2x: "/placeholder-gym-1.jpg",
      large: "/placeholder-gym-1.jpg",
      medium: "/placeholder-gym-1.jpg",
      small: "/placeholder-gym-1.jpg",
      portrait: "/placeholder-gym-1.jpg",
      landscape: "/placeholder-gym-1.jpg",
      tiny: "/placeholder-gym-1.jpg",
    },
    liked: false,
  },
  {
    id: "fallback-2",
    width: 500,
    height: 300,
    url: "",
    alt: "Fitness Room",
    photographer: "<PERSON><PERSON>",
    photographer_url: "",
    photographer_id: 0,
    avg_color: "#888888",
    src: {
      original: "/placeholder-gym-2.jpg",
      large2x: "/placeholder-gym-2.jpg",
      large: "/placeholder-gym-2.jpg",
      medium: "/placeholder-gym-2.jpg",
      small: "/placeholder-gym-2.jpg",
      portrait: "/placeholder-gym-2.jpg",
      landscape: "/placeholder-gym-2.jpg",
      tiny: "/placeholder-gym-2.jpg",
    },
    liked: false,
  },
  {
    id: "fallback-3",
    width: 500,
    height: 300,
    url: "",
    alt: "Training Area",
    photographer: "Sample",
    photographer_url: "",
    photographer_id: 0,
    avg_color: "#888888",
    src: {
      original: "/placeholder-gym-3.jpg",
      large2x: "/placeholder-gym-3.jpg",
      large: "/placeholder-gym-3.jpg",
      medium: "/placeholder-gym-3.jpg",
      small: "/placeholder-gym-3.jpg",
      portrait: "/placeholder-gym-3.jpg",
      landscape: "/placeholder-gym-3.jpg",
      tiny: "/placeholder-gym-3.jpg",
    },
    liked: false,
  },
];

interface ImageSelectorProps {
  keywords: string[];
  onSelectImages: (images: PexelsImage[]) => void;
  onCancel: () => void;
}

export const ImageSelector: React.FC<ImageSelectorProps> = ({
  keywords,
  onSelectImages,
  onCancel,
}) => {
  const [images, setImages] = useState<PexelsImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<PexelsImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchImages = async () => {
      try {
        setLoading(true);
        // Join keywords with OR for better search results
        const searchQuery = keywords.join(" OR ");
        console.log("Searching for images with query:", searchQuery);
        const fetchedImages = await searchPexelsImages(searchQuery);
        
        if (fetchedImages && fetchedImages.length > 0) {
          console.log(`Found ${fetchedImages.length} images from API`);
          setImages(fetchedImages);
          setError(null);
        } else {
          console.log("No images found from API, using fallbacks");
          // If API returns no images, use fallbacks
          setImages(FALLBACK_IMAGES);
          setError("Using sample images. Connect Pexels API for real images.");
        }
      } catch (err) {
        console.error("Error fetching images:", err);
        // Use fallback images for development or when API fails
        setImages(FALLBACK_IMAGES); 
        setError("Using sample images. Connect Pexels API for production.");
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, [keywords]);

  const toggleImageSelection = (image: PexelsImage) => {
    if (selectedImages.some((img) => img.id === image.id)) {
      setSelectedImages(selectedImages.filter((img) => img.id !== image.id));
    } else {
      setSelectedImages([...selectedImages, image]);
    }
  };

  const handleSubmit = () => {
    onSelectImages(selectedImages);
  };

  return (
    <div className="w-full max-w-[var(--thread-max-width)] flex flex-col bg-white rounded-lg border shadow-sm">
      <div className="p-4 border-b">
        <h3 className="text-lg font-medium">Select images for your website</h3>
        <p className="text-sm text-gray-500">
          Based on your business: {keywords.join(", ")}
        </p>
      </div>

      {loading && (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      )}

      {error && (
        <div className="p-4 text-red-500 text-center">
          <p>{error}</p>
          <Button 
            variant="outline" 
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </div>
      )}

      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 p-4 max-h-[400px] overflow-y-auto">
        {images.map((image) => (
          <div 
            key={image.id} 
            className={`relative cursor-pointer rounded-lg overflow-hidden transition-all ${
              selectedImages.some(img => img.id === image.id) 
                ? "ring-2 ring-blue-500 scale-95" 
                : "hover:opacity-90"
            }`}
            onClick={() => toggleImageSelection(image)}
          >
            <div className="aspect-w-16 aspect-h-9 relative">
              <Image
                src={image.src.medium}
                alt={image.alt || "Image for website"}
                width={300}
                height={200}
                className="object-cover w-full h-full"
              />
            </div>
            {selectedImages.some(img => img.id === image.id) && (
              <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                ✓
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="flex justify-end gap-2 p-4 border-t">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          disabled={selectedImages.length === 0} 
          onClick={handleSubmit}
        >
          Select {selectedImages.length > 0 ? `(${selectedImages.length})` : ""}
        </Button>
      </div>
    </div>
  );
};
