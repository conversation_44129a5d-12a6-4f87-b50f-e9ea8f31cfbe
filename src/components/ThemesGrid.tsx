import React, { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";

// Theme interface with expanded properties
export interface Theme {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  category: string;
  // Additional properties that might be available
  author?: string;
  version?: string;
  rating?: number;
  downloaded?: number;
  last_updated?: string;
  preview_url?: string;
  homepage?: string;
  tags?: Record<string, string>;
}

interface ThemesGridProps {
  keywords: string[];
  onSelectTheme: (theme: Theme) => void;
}

const ThemesGrid: React.FC<ThemesGridProps> = ({ keywords, onSelectTheme }) => {
  const [themes, setThemes] = useState<Theme[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchThemes = async () => {
      setLoading(true);
      setError(null);
      try {
        // Call your WordPress themes API
        const res = await fetch(`/api/wordpress-themes?keywords=${encodeURIComponent(keywords.join(","))}`);
        if (!res.ok) throw new Error("Failed to fetch themes");
        const data = await res.json();
        if (!data.themes || !Array.isArray(data.themes)) throw new Error("No themes found");
        // Map to local Theme type with expanded properties
        const mapped = data.themes.map((t: any) => ({
          id: t.slug,
          name: t.name,
          description: t.description?.slice(0, 100) + (t.description?.length > 100 ? "..." : ""),
          imageUrl: t.screenshot_url || "https://placehold.co/600x400/e2e8f0/1e293b?text=No+Preview",
          category: t.tags ? Object.keys(t.tags)[0] || "WordPress" : "WordPress",
          // Additional properties
          author: t.author,
          version: t.version,
          rating: t.rating,
          downloaded: t.downloaded,
          last_updated: t.last_updated,
          preview_url: t.preview_url,
          homepage: t.homepage,
          tags: t.tags
        }));
        setThemes(mapped);
      } catch (err: any) {
        setError(err.message || "Unknown error");
      } finally {
        setLoading(false);
      }
    };
    fetchThemes();
  }, [keywords]);

  if (loading) return <div className="flex justify-center py-10"><Loader2 className="animate-spin mr-2" /> Loading themes...</div>;
  if (error) return <div className="p-4 text-red-600 bg-red-50 rounded">{error}</div>;
  if (!themes.length) return <div className="p-4 text-gray-500">No themes found.</div>;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {themes.map((theme) => (
        <div
          key={theme.id}
          className="bg-white rounded-lg shadow hover:shadow-lg transition cursor-pointer border border-gray-200 flex flex-col"
          onClick={() => onSelectTheme(theme)}
        >
          <img
            src={theme.imageUrl}
            alt={theme.name}
            className="w-full h-40 object-cover rounded-t-lg"
            loading="lazy"
          />
          <div className="p-4 flex-1 flex flex-col">
            <h3 className="font-semibold text-lg mb-1">{theme.name}</h3>
            <p className="text-sm text-gray-600 flex-1">{theme.description}</p>
            <span className="inline-block mt-2 text-xs bg-blue-100 text-blue-700 rounded px-2 py-0.5">{theme.category}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ThemesGrid;
