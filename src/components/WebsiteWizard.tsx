"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";

const steps = [
  "Website Info",
  "Brief",
  "Contact Info",
  "Design & Images",
  "Confirm & Submit",
];

export default function WebsiteWizard() {
  const [currentStep, setCurrentStep] = useState(0);

  const goToStep = (index: number) => {
    if (index <= currentStep) setCurrentStep(index);
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 0) setCurrentStep(currentStep - 1);
  };

  const [siteName, setSiteName] = useState("");
  const [category, setCategory] = useState("");
  const [brief, setBrief] = useState("");
  const [imageUrl, setImageUrl] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);
  const [loadingImage, setLoadingImage] = useState(false);
  const [loadingContent, setLoadingContent] = useState(false);
  const [contact, setContact] = useState({
    email: "",
    phone: "",
    address: "",
    facebook: "",
    twitter: "",
    instagram: "",
  });

  /*const generateImage = async () => {
    setLoadingImage(true);
    const res = await fetch('/api/generate-image', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt: brief || 'Modern WordPress homepage' })
    });
    const data = await res.json();
    setImageUrl(data.imageUrl);
    setLoadingImage(false);
  };*/

  const handleGenerateImage = async () => {
    const res = await fetch("/api/generate-image", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        prompt: "modern website homepage for a digital agency",
      }),
    });

    const data = await res.json();
    if (data.imageUrl) {
      setImageUrl(data.imageUrl);
    } else {
      console.error("Image generation failed:", data.error);
      alert("Failed to generate image: " + data.error);
    }
  };

  const generateContent = async () => {
    setLoadingContent(true);
    const res = await fetch("/api/generate-content", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ brief }),
    });
    const data = await res.json();
    setGeneratedContent(data);
    setLoadingContent(false);
  };

  return (
    <div className="w-full max-w-3xl p-4 mx-auto space-y-4">
      {/* Step Indicators */}
      <div className="flex items-center justify-between mb-4">
        {steps.map((step, index) => (
          <button
            key={index}
            onClick={() => goToStep(index)}
            className={`flex-1 px-2 py-1 text-sm rounded text-center ${
              index === currentStep
                ? "bg-blue-600 text-white"
                : index < currentStep
                ? "bg-blue-200 text-blue-900"
                : "bg-gray-100 text-gray-500"
            }`}
          >
            {index + 1}. {step}
          </button>
        ))}
      </div>

      {/* Animated Step Content */}
      <div className="border rounded-xl shadow p-6 min-h-[300px] flex flex-col justify-between bg-white">
        <div className="flex-1 relative overflow-hidden min-h-[300px]">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -30 }}
              transition={{ duration: 0.3 }}
              className="absolute w-full"
            >
              {currentStep === 0 && (
                <div>
                  <h2 className="mb-2 text-xl font-semibold">Website Info</h2>
                  <input
                    type="text"
                    placeholder="Website Name"
                    className="w-full p-2 mb-3 border rounded"
                  />
                  <input
                    type="text"
                    placeholder="Website Category"
                    className="w-full p-2 border rounded"
                  />
                </div>
              )}
              {currentStep === 1 && (
                <div>
                  <h2 className="mb-2 text-xl font-semibold">Brief</h2>
                  <textarea
                    placeholder="Tell us about your website..."
                    className="w-full h-40 p-2 border rounded"
                    maxLength={3000}
                  />
                  <Button className="mt-2">Improve with AI ✨</Button>
                </div>
              )}
              {currentStep === 2 && (
                <div>
                  <h2 className="mb-2 text-xl font-semibold">Contact Info</h2>
                  <input
                    placeholder="Email"
                    className="w-full p-2 mb-2 border rounded"
                  />
                  <input
                    placeholder="Phone Number"
                    className="w-full p-2 mb-2 border rounded"
                  />
                  <input
                    placeholder="Address"
                    className="w-full p-2 mb-2 border rounded"
                  />
                  <input
                    placeholder="Social Media URL(s)"
                    className="w-full p-2 border rounded"
                  />
                </div>
              )}
              {currentStep === 3 && (
                <div>
                  <h2 className="mb-2 text-xl font-semibold">
                    Design & Images
                  </h2>
                  <p className="mb-2 text-gray-600">
                    Auto-generate content, choose templates, or generate images.
                  </p>
                  <Button className="mr-2" onClick={handleGenerateImage}>
                    Generate Images with AI 🖼️
                  </Button>
                  {imageUrl && (
                    <img
                      src={imageUrl}
                      alt="Generated"
                      className="mt-4 rounded-lg"
                    />
                  )}
                  <Button className="mr-2" onClick={generateContent}>
                    Generate Content with AI 🖼️
                  </Button>
                  {generatedContent && (
                    <div className="p-4 mt-4 space-y-2 rounded shadow bg-gray-50">
                      <h2 className="text-xl font-semibold">
                        {generatedContent.heroHeadline}
                      </h2>
                      <p>{generatedContent.heroSubtext}</p>
                      <h3 className="mt-4 font-semibold">About</h3>
                      <p>{generatedContent.about}</p>
                      <h3 className="mt-4 font-semibold">Services</h3>
                      <p>{generatedContent.services}</p>
                      <h3 className="mt-4 font-semibold">Call to Action</h3>
                      <p>{generatedContent.contactCta}</p>
                    </div>
                  )}

                  <Button variant="outline">Choose Template</Button>
                </div>
              )}
              {currentStep === 4 && (
                <div>
                  <h2 className="mb-2 text-xl font-semibold">
                    Confirm & Submit
                  </h2>
                  <p className="text-gray-600">
                    Review your details and click submit to build your WordPress
                    site.
                  </p>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-4">
          <Button
            onClick={prevStep}
            disabled={currentStep === 0}
            variant="outline"
          >
            Back
          </Button>
          <Button
            onClick={nextStep}
            disabled={currentStep === steps.length - 1}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
