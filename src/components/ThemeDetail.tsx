'use client';

import React from 'react';
import { Loader2, Star, Download, Calendar, User, Tag, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ThemePreview from '@/components/ThemePreview';

interface ThemeDetailProps {
  theme: any;
  loading: boolean;
  error: string | null;
}

const ThemeDetail: React.FC<ThemeDetailProps> = ({ theme, loading, error }) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <Loader2 className="animate-spin mr-2" size={24} />
        <span className="text-lg">Loading theme details...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 text-red-600 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Error</h2>
        <p>{error}</p>
      </div>
    );
  }

  if (!theme) {
    return (
      <div className="p-6 bg-yellow-50 text-yellow-700 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Theme Not Found</h2>
        <p>The requested theme could not be found.</p>
      </div>
    );
  }

  // Check if this is fallback data
  const isFallback = theme._error && theme._error.fallback;
  const fallbackNote = theme._note;

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format rating
  const formatRating = (rating: number) => {
    return rating ? (rating / 100 * 5).toFixed(1) : 'No ratings';
  };

  // Format downloads
  const formatDownloads = (downloads: number) => {
    if (!downloads) return 'Unknown';
    if (downloads >= 1000000) {
      return `${(downloads / 1000000).toFixed(1)}M+`;
    } else if (downloads >= 1000) {
      return `${(downloads / 1000).toFixed(1)}K+`;
    }
    return downloads.toString();
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Fallback Notice */}
      {(isFallback || fallbackNote) && (
        <div className="p-4 bg-amber-50 border-b border-amber-200">
          <p className="text-amber-700 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {isFallback && theme._error.message ?
              theme._error.message :
              fallbackNote || "Showing limited theme information. Unable to connect to WordPress.org for complete details."}
          </p>
        </div>
      )}

      {/* Theme Header */}
      <div className="p-6 border-b">
        <h1 className="text-3xl font-bold mb-2">{theme.name}</h1>
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
          {theme.author && (
            <div className="flex items-center">
              <User size={16} className="mr-1" />
              <span>By: <span dangerouslySetInnerHTML={{ __html: theme.author }} /></span>
            </div>
          )}
          {theme.version && (
            <div>Version: {theme.version}</div>
          )}
          {theme.last_updated && (
            <div className="flex items-center">
              <Calendar size={16} className="mr-1" />
              <span>Updated: {formatDate(theme.last_updated)}</span>
            </div>
          )}
          {theme.rating && (
            <div className="flex items-center">
              <Star size={16} className="mr-1 text-yellow-500" />
              <span>{formatRating(theme.rating)}/5</span>
            </div>
          )}
          {theme.downloaded && (
            <div className="flex items-center">
              <Download size={16} className="mr-1" />
              <span>{formatDownloads(theme.downloaded)} downloads</span>
            </div>
          )}
        </div>
      </div>

      {/* Theme Preview */}
      <div className="p-6 border-b">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Theme Preview</h2>
          <Button variant="outline" size="sm" asChild>
            <a href={`/themes/preview/${theme.slug}`} target="_blank" rel="noopener noreferrer">
              Full Preview
            </a>
          </Button>
        </div>
        <ThemePreview theme={theme} />
      </div>

      {/* Theme Description */}
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold mb-4">Description</h2>
        <div
          className="prose max-w-none"
          dangerouslySetInnerHTML={{ __html: theme.description || 'No description available.' }}
        />
      </div>

      {/* Theme Features */}
      {theme.tags && Object.keys(theme.tags).length > 0 && (
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold mb-4">Features & Tags</h2>
          <div className="flex flex-wrap gap-2">
            {Object.keys(theme.tags).map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full flex items-center"
              >
                <Tag size={14} className="mr-1" />
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Theme Requirements */}
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold mb-4">Requirements</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium mb-2">WordPress Version</h3>
            <p>{theme.requires || 'Not specified'}</p>
          </div>
          <div>
            <h3 className="font-medium mb-2">PHP Version</h3>
            <p>{theme.requires_php || 'Not specified'}</p>
          </div>
        </div>
      </div>

      {/* External Links */}
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-4">External Links</h2>
        <div className="flex flex-wrap gap-4">
          {theme.homepage && (
            <Button variant="outline" className="flex items-center gap-2" asChild>
              <a href={theme.homepage} target="_blank" rel="noopener noreferrer">
                <ExternalLink size={16} />
                Theme Homepage
              </a>
            </Button>
          )}
          {theme.download_link && (
            <Button variant="outline" className="flex items-center gap-2" asChild>
              <a href={theme.download_link} target="_blank" rel="noopener noreferrer">
                <Download size={16} />
                Download Theme
              </a>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ThemeDetail;
