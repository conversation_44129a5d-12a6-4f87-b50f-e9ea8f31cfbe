/** @type {import('next').NextConfig} */
const nextConfig = {
  // Removed experimental.appDir as it's now the default in Next.js 14
  images: {
    // Using remotePatterns instead of domains as recommended by Next.js
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.wordpress.org',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '**.wp.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'ps.w.org',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '**.wp.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 's.w.org',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'themes.svn.wordpress.org',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'wp-themes.com',
        pathname: '**',
      }
    ]
  },
};

module.exports = nextConfig;
