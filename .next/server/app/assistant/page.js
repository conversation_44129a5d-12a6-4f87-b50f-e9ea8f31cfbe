/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/assistant/page";
exports.ids = ["app/assistant/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassistant%2Fpage&page=%2Fassistant%2Fpage&appPaths=%2Fassistant%2Fpage&pagePath=private-next-app-dir%2Fassistant%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassistant%2Fpage&page=%2Fassistant%2Fpage&appPaths=%2Fassistant%2Fpage&pagePath=private-next-app-dir%2Fassistant%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'assistant',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/assistant/page.tsx */ \"(rsc)/./src/app/assistant/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/assistant/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/assistant/page\",\n        pathname: \"/assistant\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassistant%2Fpage&page=%2Fassistant%2Fpage&appPaths=%2Fassistant%2Fpage&pagePath=private-next-app-dir%2Fassistant%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fassistant%2Fpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fassistant%2Fpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/assistant/page.tsx */ \"(ssr)/./src/app/assistant/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGYXNzaXN0YW50JTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8/Nzg5OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvc3JjL2FwcC9hc3Npc3RhbnQvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fassistant%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/assistant.tsx":
/*!*******************************!*\
  !*** ./src/app/assistant.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Assistant: () => (/* binding */ Assistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/context/providers/AssistantRuntimeProvider.js\");\n/* harmony import */ var _assistant_ui_react_ai_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @assistant-ui/react-ai-sdk */ \"(ssr)/./node_modules/@assistant-ui/react-ai-sdk/dist/useChatRuntime.js\");\n/* harmony import */ var _components_assistant_ui_thread__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/assistant-ui/thread */ \"(ssr)/./src/components/assistant-ui/thread.tsx\");\n/* __next_internal_client_entry_do_not_use__ Assistant auto */ \n\n\n\nconst Assistant = ()=>{\n    const runtime = (0,_assistant_ui_react_ai_sdk__WEBPACK_IMPORTED_MODULE_2__.useChatRuntime)({\n        api: \"/api/chat\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_3__.AssistantRuntimeProvider, {\n        runtime: runtime,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-dvh\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_thread__WEBPACK_IMPORTED_MODULE_1__.Thread, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/assistant.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/assistant.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/assistant.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Fzc2lzdGFudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUrRDtBQUNIO0FBQ0Y7QUFHbkQsTUFBTUcsWUFBWTtJQUN2QixNQUFNQyxVQUFVSCwwRUFBY0EsQ0FBQztRQUM3QkksS0FBSztJQUNQO0lBRUEscUJBQ0UsOERBQUNMLHlFQUF3QkE7UUFBQ0ksU0FBU0E7a0JBQ2pDLDRFQUFDRTtZQUFJQyxXQUFVO3NCQUViLDRFQUFDTCxtRUFBTUE7Ozs7Ozs7Ozs7Ozs7OztBQUlmLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vc3JjL2FwcC9hc3Npc3RhbnQudHN4PzhhMGMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IEFzc2lzdGFudFJ1bnRpbWVQcm92aWRlciB9IGZyb20gXCJAYXNzaXN0YW50LXVpL3JlYWN0XCI7XG5pbXBvcnQgeyB1c2VDaGF0UnVudGltZSB9IGZyb20gXCJAYXNzaXN0YW50LXVpL3JlYWN0LWFpLXNka1wiO1xuaW1wb3J0IHsgVGhyZWFkIH0gZnJvbSBcIkAvY29tcG9uZW50cy9hc3Npc3RhbnQtdWkvdGhyZWFkXCI7XG5pbXBvcnQgeyBUaHJlYWRMaXN0IH0gZnJvbSBcIkAvY29tcG9uZW50cy9hc3Npc3RhbnQtdWkvdGhyZWFkLWxpc3RcIjtcblxuZXhwb3J0IGNvbnN0IEFzc2lzdGFudCA9ICgpID0+IHtcbiAgY29uc3QgcnVudGltZSA9IHVzZUNoYXRSdW50aW1lKHtcbiAgICBhcGk6IFwiL2FwaS9jaGF0XCIsXG4gIH0pO1xuXG4gIHJldHVybiAoXG4gICAgPEFzc2lzdGFudFJ1bnRpbWVQcm92aWRlciBydW50aW1lPXtydW50aW1lfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1kdmhcIj5cbiAgICAgICAgey8qIDxUaHJlYWRMaXN0IC8+ICovfVxuICAgICAgICA8VGhyZWFkIC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0Fzc2lzdGFudFJ1bnRpbWVQcm92aWRlcj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiQXNzaXN0YW50UnVudGltZVByb3ZpZGVyIiwidXNlQ2hhdFJ1bnRpbWUiLCJUaHJlYWQiLCJBc3Npc3RhbnQiLCJydW50aW1lIiwiYXBpIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/assistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/assistant/page.tsx":
/*!************************************!*\
  !*** ./src/app/assistant/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AssistantPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _assistant__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../assistant */ \"(ssr)/./src/app/assistant.tsx\");\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AssistantPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant__WEBPACK_IMPORTED_MODULE_1__.Assistant, {}, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Fzc2lzdGFudC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFeUM7QUFDaUI7QUFFM0MsU0FBU0U7SUFDdEIscUJBQ0UsOERBQUNELG9FQUFlQTtrQkFDZCw0RUFBQ0QsaURBQVNBOzs7Ozs7Ozs7O0FBR2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvYXNzaXN0YW50L3BhZ2UudHN4PzgzNzUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IEFzc2lzdGFudCB9IGZyb20gXCIuLi9hc3Npc3RhbnRcIjtcbmltcG9ydCB7IFRvb2x0aXBQcm92aWRlciB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdG9vbHRpcFwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBc3Npc3RhbnRQYWdlKCkge1xuICByZXR1cm4gKFxuICAgIDxUb29sdGlwUHJvdmlkZXI+XG4gICAgICA8QXNzaXN0YW50IC8+XG4gICAgPC9Ub29sdGlwUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXNzaXN0YW50IiwiVG9vbHRpcFByb3ZpZGVyIiwiQXNzaXN0YW50UGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/assistant/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/assistant-ui/image-selector.tsx":
/*!********************************************************!*\
  !*** ./src/components/assistant-ui/image-selector.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSelector: () => (/* binding */ ImageSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_pexels_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/pexels-api */ \"(ssr)/./src/lib/pexels-api.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n\n\n\n\n\n// Fallback sample images for development or when the API fails\nconst FALLBACK_IMAGES = [\n    {\n        id: \"fallback-1\",\n        width: 500,\n        height: 300,\n        url: \"\",\n        alt: \"Workout Equipment\",\n        photographer: \"Sample\",\n        photographer_url: \"\",\n        photographer_id: 0,\n        avg_color: \"#888888\",\n        src: {\n            original: \"/placeholder-gym-1.jpg\",\n            large2x: \"/placeholder-gym-1.jpg\",\n            large: \"/placeholder-gym-1.jpg\",\n            medium: \"/placeholder-gym-1.jpg\",\n            small: \"/placeholder-gym-1.jpg\",\n            portrait: \"/placeholder-gym-1.jpg\",\n            landscape: \"/placeholder-gym-1.jpg\",\n            tiny: \"/placeholder-gym-1.jpg\"\n        },\n        liked: false\n    },\n    {\n        id: \"fallback-2\",\n        width: 500,\n        height: 300,\n        url: \"\",\n        alt: \"Fitness Room\",\n        photographer: \"Sample\",\n        photographer_url: \"\",\n        photographer_id: 0,\n        avg_color: \"#888888\",\n        src: {\n            original: \"/placeholder-gym-2.jpg\",\n            large2x: \"/placeholder-gym-2.jpg\",\n            large: \"/placeholder-gym-2.jpg\",\n            medium: \"/placeholder-gym-2.jpg\",\n            small: \"/placeholder-gym-2.jpg\",\n            portrait: \"/placeholder-gym-2.jpg\",\n            landscape: \"/placeholder-gym-2.jpg\",\n            tiny: \"/placeholder-gym-2.jpg\"\n        },\n        liked: false\n    },\n    {\n        id: \"fallback-3\",\n        width: 500,\n        height: 300,\n        url: \"\",\n        alt: \"Training Area\",\n        photographer: \"Sample\",\n        photographer_url: \"\",\n        photographer_id: 0,\n        avg_color: \"#888888\",\n        src: {\n            original: \"/placeholder-gym-3.jpg\",\n            large2x: \"/placeholder-gym-3.jpg\",\n            large: \"/placeholder-gym-3.jpg\",\n            medium: \"/placeholder-gym-3.jpg\",\n            small: \"/placeholder-gym-3.jpg\",\n            portrait: \"/placeholder-gym-3.jpg\",\n            landscape: \"/placeholder-gym-3.jpg\",\n            tiny: \"/placeholder-gym-3.jpg\"\n        },\n        liked: false\n    }\n];\nconst ImageSelector = ({ keywords, onSelectImages, onCancel })=>{\n    const [images, setImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchImages = async ()=>{\n            try {\n                setLoading(true);\n                // Join keywords with OR for better search results\n                const searchQuery = keywords.join(\" OR \");\n                console.log(\"Searching for images with query:\", searchQuery);\n                const fetchedImages = await (0,_lib_pexels_api__WEBPACK_IMPORTED_MODULE_2__.searchPexelsImages)(searchQuery);\n                if (fetchedImages && fetchedImages.length > 0) {\n                    console.log(`Found ${fetchedImages.length} images from API`);\n                    setImages(fetchedImages);\n                    setError(null);\n                } else {\n                    console.log(\"No images found from API, using fallbacks\");\n                    // If API returns no images, use fallbacks\n                    setImages(FALLBACK_IMAGES);\n                    setError(\"Using sample images. Connect Pexels API for real images.\");\n                }\n            } catch (err) {\n                console.error(\"Error fetching images:\", err);\n                // Use fallback images for development or when API fails\n                setImages(FALLBACK_IMAGES);\n                setError(\"Using sample images. Connect Pexels API for production.\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchImages();\n    }, [\n        keywords\n    ]);\n    const toggleImageSelection = (image)=>{\n        if (selectedImages.some((img)=>img.id === image.id)) {\n            setSelectedImages(selectedImages.filter((img)=>img.id !== image.id));\n        } else {\n            setSelectedImages([\n                ...selectedImages,\n                image\n            ]);\n        }\n    };\n    const handleSubmit = ()=>{\n        onSelectImages(selectedImages);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-[var(--thread-max-width)] flex flex-col bg-white rounded-lg border shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Select images for your website\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500\",\n                        children: [\n                            \"Based on your business: \",\n                            keywords.join(\", \")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 text-red-500 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        className: \"mt-2\",\n                        onClick: ()=>window.location.reload(),\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 gap-4 p-4 max-h-[400px] overflow-y-auto\",\n                children: images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `relative cursor-pointer rounded-lg overflow-hidden transition-all ${selectedImages.some((img)=>img.id === image.id) ? \"ring-2 ring-blue-500 scale-95\" : \"hover:opacity-90\"}`,\n                        onClick: ()=>toggleImageSelection(image),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aspect-w-16 aspect-h-9 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: image.src.medium,\n                                    alt: image.alt || \"Image for website\",\n                                    width: 300,\n                                    height: 200,\n                                    className: \"object-cover w-full h-full\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined),\n                            selectedImages.some((img)=>img.id === image.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center\",\n                                children: \"✓\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, image.id, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2 p-4 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        disabled: selectedImages.length === 0,\n                        onClick: handleSubmit,\n                        children: [\n                            \"Select \",\n                            selectedImages.length > 0 ? `(${selectedImages.length})` : \"\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/image-selector.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/assistant-ui/image-selector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/assistant-ui/markdown-text.tsx":
/*!*******************************************************!*\
  !*** ./src/components/assistant-ui/markdown-text.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarkdownText: () => (/* binding */ MarkdownText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _assistant_ui_react_markdown_styles_dot_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @assistant-ui/react-markdown/styles/dot.css */ \"(ssr)/./node_modules/@assistant-ui/react-markdown/styles/dot.css\");\n/* harmony import */ var _assistant_ui_react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @assistant-ui/react-markdown */ \"(ssr)/./node_modules/@assistant-ui/react-markdown/dist/primitives/MarkdownText.js\");\n/* harmony import */ var _assistant_ui_react_markdown__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @assistant-ui/react-markdown */ \"(ssr)/./node_modules/@assistant-ui/react-markdown/dist/memoization.js\");\n/* harmony import */ var _assistant_ui_react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @assistant-ui/react-markdown */ \"(ssr)/./node_modules/@assistant-ui/react-markdown/dist/overrides/PreOverride.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_CopyIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,CopyIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_CopyIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,CopyIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/assistant-ui/tooltip-icon-button */ \"(ssr)/./src/components/assistant-ui/tooltip-icon-button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarkdownText auto */ \n\n\n\n\n\n\n\nconst MarkdownTextImpl = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react_markdown__WEBPACK_IMPORTED_MODULE_5__.MarkdownTextPrimitive, {\n        remarkPlugins: [\n            remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        ],\n        className: \"aui-md\",\n        components: defaultComponents\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\nconst MarkdownText = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.memo)(MarkdownTextImpl);\nconst CodeHeader = ({ language, code })=>{\n    const { isCopied, copyToClipboard } = useCopyToClipboard();\n    const onCopy = ()=>{\n        if (!code || isCopied) return;\n        copyToClipboard(code);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between gap-4 rounded-t-lg bg-zinc-900 px-4 py-2 text-sm font-semibold text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"lowercase [&>span]:text-xs\",\n                children: language\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_3__.TooltipIconButton, {\n                tooltip: \"Copy\",\n                onClick: onCopy,\n                children: [\n                    !isCopied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_CopyIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 23\n                    }, undefined),\n                    isCopied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_CopyIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 22\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\nconst useCopyToClipboard = ({ copiedDuration = 3000 } = {})=>{\n    const [isCopied, setIsCopied] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const copyToClipboard = (value)=>{\n        if (!value) return;\n        navigator.clipboard.writeText(value).then(()=>{\n            setIsCopied(true);\n            setTimeout(()=>setIsCopied(false), copiedDuration);\n        });\n    };\n    return {\n        isCopied,\n        copyToClipboard\n    };\n};\nconst defaultComponents = (0,_assistant_ui_react_markdown__WEBPACK_IMPORTED_MODULE_9__.memoizeMarkdownComponents)({\n    h1: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-8 scroll-m-20 text-4xl font-extrabold tracking-tight last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 69,\n            columnNumber: 5\n        }, undefined),\n    h2: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-4 mt-8 scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0 last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 72,\n            columnNumber: 5\n        }, undefined),\n    h3: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-4 mt-6 scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0 last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 75,\n            columnNumber: 5\n        }, undefined),\n    h4: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-4 mt-6 scroll-m-20 text-xl font-semibold tracking-tight first:mt-0 last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 78,\n            columnNumber: 5\n        }, undefined),\n    h5: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"my-4 text-lg font-semibold first:mt-0 last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 81,\n            columnNumber: 5\n        }, undefined),\n    h6: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"my-4 font-semibold first:mt-0 last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 84,\n            columnNumber: 5\n        }, undefined),\n    p: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"mb-5 mt-5 leading-7 first:mt-0 last:mb-0\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 87,\n            columnNumber: 5\n        }, undefined),\n    a: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-primary font-medium underline underline-offset-4\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 90,\n            columnNumber: 5\n        }, undefined),\n    blockquote: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-l-2 pl-6 italic\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 93,\n            columnNumber: 5\n        }, undefined),\n    ul: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"my-5 ml-6 list-disc [&>li]:mt-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 96,\n            columnNumber: 5\n        }, undefined),\n    ol: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"my-5 ml-6 list-decimal [&>li]:mt-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 99,\n            columnNumber: 5\n        }, undefined),\n    hr: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"my-5 border-b\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 102,\n            columnNumber: 5\n        }, undefined),\n    table: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"my-5 w-full border-separate border-spacing-0 overflow-y-auto\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 105,\n            columnNumber: 5\n        }, undefined),\n    th: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-muted px-4 py-2 text-left font-bold first:rounded-tl-lg last:rounded-tr-lg [&[align=center]]:text-center [&[align=right]]:text-right\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 108,\n            columnNumber: 5\n        }, undefined),\n    td: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-b border-l px-4 py-2 text-left last:border-r [&[align=center]]:text-center [&[align=right]]:text-right\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 111,\n            columnNumber: 5\n        }, undefined),\n    tr: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"m-0 border-b p-0 first:border-t [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 114,\n            columnNumber: 5\n        }, undefined),\n    sup: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"sup\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"[&>a]:text-xs [&>a]:no-underline\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 117,\n            columnNumber: 5\n        }, undefined),\n    pre: ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"overflow-x-auto rounded-b-lg bg-black p-4 text-white\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 120,\n            columnNumber: 5\n        }, undefined),\n    code: function Code({ className, ...props }) {\n        const isCodeBlock = (0,_assistant_ui_react_markdown__WEBPACK_IMPORTED_MODULE_10__.useIsMarkdownCodeBlock)();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(!isCodeBlock && \"bg-muted rounded border font-semibold\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/markdown-text.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    },\n    CodeHeader\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/assistant-ui/markdown-text.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/assistant-ui/thread.tsx":
/*!************************************************!*\
  !*** ./src/components/assistant-ui/thread.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Thread: () => (/* binding */ Thread)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/thread/ThreadRoot.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/thread/ThreadViewport.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/thread/ThreadMessages.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/thread/ThreadIf.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/thread/ThreadScrollToBottom.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/thread/ThreadEmpty.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/context/react/ThreadContext.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/context/react/ComposerContext.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/composer/ComposerRoot.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/composer/ComposerSend.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/composer/ComposerCancel.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/composer/ComposerInput.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/message/MessageRoot.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/message/MessageContent.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/actionBar/ActionBarRoot.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/actionBar/ActionBarEdit.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/actionBar/ActionBarCopy.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/message/MessageIf.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/actionBar/ActionBarReload.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/branchPicker/BranchPickerRoot.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/branchPicker/BranchPickerPrevious.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/branchPicker/BranchPickerNumber.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/branchPicker/BranchPickerCount.js\");\n/* harmony import */ var _assistant_ui_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @assistant-ui/react */ \"(ssr)/./node_modules/@assistant-ui/react/dist/primitives/branchPicker/BranchPickerNext.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-grid.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,CheckIcon,ChevronLeftIcon,ChevronRightIcon,CopyIcon,LayoutGridIcon,PencilIcon,RefreshCwIcon,SendHorizontalIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_assistant_ui_markdown_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/assistant-ui/markdown-text */ \"(ssr)/./src/components/assistant-ui/markdown-text.tsx\");\n/* harmony import */ var _components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/assistant-ui/tooltip-icon-button */ \"(ssr)/./src/components/assistant-ui/tooltip-icon-button.tsx\");\n/* harmony import */ var _components_assistant_ui_image_selector__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/assistant-ui/image-selector */ \"(ssr)/./src/components/assistant-ui/image-selector.tsx\");\n/* harmony import */ var _lib_pexels_api__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/pexels-api */ \"(ssr)/./src/lib/pexels-api.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst Thread = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_10__.ThreadPrimitiveRoot, {\n        className: \"box-border flex flex-col h-full overflow-hidden bg-background\",\n        style: {\n            [\"--thread-max-width\"]: \"42rem\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_11__.ThreadPrimitiveViewport, {\n            className: \"flex flex-col items-center h-full px-4 pt-8 overflow-y-scroll scroll-smooth bg-inherit\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThreadWelcome, {}, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_12__.ThreadPrimitiveMessages, {\n                    components: {\n                        UserMessage: UserMessage,\n                        EditComposer: EditComposer,\n                        AssistantMessage: AssistantMessage\n                    }\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                    empty: false,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow min-h-8\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sticky bottom-0 mt-3 flex w-full max-w-[var(--thread-max-width)] flex-col items-center justify-end rounded-t-lg bg-inherit pb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThreadScrollToBottom, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Composer, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\nconst ThreadScrollToBottom = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_14__.ThreadPrimitiveScrollToBottom, {\n        asChild: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n            tooltip: \"Scroll to bottom\",\n            variant: \"outline\",\n            className: \"absolute rounded-full -top-8 disabled:invisible\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\nconst ThreadWelcome = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_16__.ThreadPrimitiveEmpty, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex w-full max-w-[var(--thread-max-width)] flex-grow flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center flex-grow w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 font-medium\",\n                    children: [\n                        \"Hi,\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined),\n                        \" Let's get started with your site setup.\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                            children: \" First, tell me what your site is all about.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, undefined);\n};\n// Commented out unused component\n/*const ThreadWelcomeSuggestions: FC = () => {\n  return (\n    <div className=\"flex items-stretch justify-center w-full gap-4 mt-3\">\n      <ThreadPrimitive.Suggestion\n        className=\"flex flex-col items-center justify-center max-w-sm p-3 transition-colors ease-in border rounded-lg hover:bg-muted/80 grow basis-0\"\n        prompt=\"What is the weather in Tokyo?\"\n        method=\"replace\"\n        autoSend\n      >\n        <span className=\"text-sm font-semibold line-clamp-2 text-ellipsis\">\n          What is the weather in Tokyo?\n        </span>\n      </ThreadPrimitive.Suggestion>\n      <ThreadPrimitive.Suggestion\n        className=\"flex flex-col items-center justify-center max-w-sm p-3 transition-colors ease-in border rounded-lg hover:bg-muted/80 grow basis-0\"\n        prompt=\"What is assistant-ui?\"\n        method=\"replace\"\n        autoSend\n      >\n        <span className=\"text-sm font-semibold line-clamp-2 text-ellipsis\">\n          What is assistant-ui?\n        </span>\n      </ThreadPrimitive.Suggestion>\n    </div>\n  );\n};*/ const Composer = ()=>{\n    const { messages } = (0,_assistant_ui_react__WEBPACK_IMPORTED_MODULE_17__.useThread)();\n    const composerRuntime = (0,_assistant_ui_react__WEBPACK_IMPORTED_MODULE_18__.useComposerRuntime)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [phone, setPhone] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [showImageSelector, setShowImageSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        \"business\"\n    ]);\n    // Detect if the last assistant message is specifically the contact info question\n    const isContactInfoQuestion = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!messages || messages.length === 0) return false;\n        // Find the most recent assistant message\n        for(let i = messages.length - 1; i >= 0; i--){\n            const message = messages[i];\n            if (message.role === \"assistant\") {\n                const content = Array.isArray(message.content) ? message.content.map((c)=>typeof c === \"string\" ? c : c.text || \"\").join(\" \") : message.content;\n                // Check if this most recent assistant message is the contact info question\n                if (typeof content === \"string\" && content.toLowerCase().includes(\"provide your contact information\")) {\n                    return true;\n                }\n                // If we found the most recent assistant message and it's not the contact question, return false\n                return false;\n            }\n        }\n        return false;\n    }, [\n        messages\n    ]);\n    // Detect if the last assistant message is asking for image selection\n    const isImageSelectionQuestion = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!messages || messages.length === 0) return false;\n        // Find the most recent assistant message\n        for(let i = messages.length - 1; i >= 0; i--){\n            const message = messages[i];\n            if (message.role === \"assistant\") {\n                const content = Array.isArray(message.content) ? message.content.map((c)=>typeof c === \"string\" ? c : c.text || \"\").join(\" \") : message.content;\n                // Check if this most recent assistant message is asking for image selection\n                if (typeof content === \"string\" && (content.toLowerCase().includes(\"select the images\") || content.toLowerCase().includes(\"images you'd like to include\"))) {\n                    console.log(\"✅ Found image selection question!\");\n                    return true;\n                }\n                // If we found the most recent assistant message and it's not the image selection question, return false\n                return false;\n            }\n        }\n        return false;\n    }, [\n        messages\n    ]);\n    // Track if we've already processed the current image selection question\n    const [processedImageQuestion, setProcessedImageQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Reset processedImageQuestion when new assistant messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Check if the last message is from the assistant and it's not the image selection question\n        if (messages && messages.length > 0) {\n            const lastMessage = messages[messages.length - 1];\n            if (lastMessage.role === \"assistant\") {\n                const content = Array.isArray(lastMessage.content) ? lastMessage.content.map((c)=>typeof c === \"string\" ? c : c.text || \"\").join(\" \") : lastMessage.content;\n                // If this is a new assistant message and not the image selection question,\n                // reset the processed flag\n                if (typeof content === \"string\" && !content.toLowerCase().includes(\"select the images\") && !content.toLowerCase().includes(\"images you'd like to include\")) {\n                    console.log(\"New assistant message detected, resetting processedImageQuestion\");\n                    setProcessedImageQuestion(false);\n                }\n            }\n        }\n    }, [\n        messages\n    ]);\n    // Show image selector when the image selection question is detected\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Only proceed if we have an image selection question, the selector isn't already shown,\n        // and we haven't already processed this question\n        if (isImageSelectionQuestion && !showImageSelector && !processedImageQuestion) {\n            console.log(\"⭐ Activating image selector based on direct question detection!\");\n            // Mark this question as processed to prevent re-triggering\n            setProcessedImageQuestion(true);\n            // Extract keywords from the conversation for image search\n            const getKeywords = async ()=>{\n                try {\n                    // Use the async function to get keywords from the entire conversation\n                    const extractedKeywords = await (0,_lib_pexels_api__WEBPACK_IMPORTED_MODULE_9__.extractKeywordsFromConversation)(Array.from(messages));\n                    console.log(\"Extracted keywords:\", extractedKeywords);\n                    setKeywords(extractedKeywords);\n                    setShowImageSelector(true); // Actually show the component\n                } catch (error) {\n                    console.error(\"Error getting keywords:\", error);\n                    setKeywords([\n                        \"business\"\n                    ]); // Fallback\n                    setShowImageSelector(true);\n                }\n            };\n            getKeywords();\n        }\n    }, [\n        isImageSelectionQuestion,\n        showImageSelector,\n        messages,\n        processedImageQuestion\n    ]);\n    // Handle image selection\n    const handleImageSelection = (images)=>{\n        // Set selected images and hide the selector\n        setSelectedImages(images);\n        setShowImageSelector(false);\n        // Only proceed if we have images and a composer runtime\n        if (composerRuntime && images.length > 0) {\n            // Format images for message\n            const imageUrls = images.map((img)=>img.src.medium).join(\"\\n\");\n            const messageText = `Selected images for my website:\\n${imageUrls}`;\n            // Set the text in the composer\n            composerRuntime.setText(messageText);\n            // Send immediately\n            composerRuntime.send();\n            // Reset states after sending\n            setSelectedImages([]);\n            setProcessedImageQuestion(false);\n        } else {\n            // Reset the processed flag even if no images were selected\n            setProcessedImageQuestion(false);\n        }\n    };\n    // Update the composer value when email or phone changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isContactInfoQuestion && composerRuntime) {\n            composerRuntime.setText(email || phone ? `email : ${email}\\nphone : ${phone}` : \"\");\n        }\n    }, [\n        email,\n        phone,\n        isContactInfoQuestion,\n        composerRuntime\n    ]);\n    // Validate inputs before enabling send button\n    const isValid = email.length > 0 && phone.length > 0;\n    // Show image selector when triggered\n    if (showImageSelector) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_image_selector__WEBPACK_IMPORTED_MODULE_8__.ImageSelector, {\n                keywords: keywords,\n                onSelectImages: handleImageSelection,\n                onCancel: ()=>{\n                    setShowImageSelector(false);\n                    setProcessedImageQuestion(false); // Reset so we can process future image questions\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 308,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show image preview when images are selected\n    if (selectedImages.length > 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_19__.ComposerPrimitiveRoot, {\n            className: \"focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full p-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 mb-3\",\n                            children: selectedImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-20 h-20 overflow-hidden rounded\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: image.src.small,\n                                        alt: image.alt || \"Selected image\",\n                                        className: \"object-cover\",\n                                        fill: true,\n                                        sizes: \"80px\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, image.id, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\n                                selectedImages.length,\n                                \" images selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                    running: false,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_20__.ComposerPrimitiveSend, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                            tooltip: \"Send\",\n                            variant: \"default\",\n                            className: \"my-2.5 size-8 p-2 transition-opacity ease-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                    running: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_22__.ComposerPrimitiveCancel, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                            tooltip: \"Cancel\",\n                            variant: \"default\",\n                            className: \"my-2.5 size-8 p-2 transition-opacity ease-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleStopIcon, {}, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 324,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Only show the specialized contact form for the contact info question\n    if (isContactInfoQuestion) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_19__.ComposerPrimitiveRoot, {\n            className: \"focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col w-full gap-2 p-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            placeholder: \"Email\",\n                            className: \"px-2 py-2 border rounded\",\n                            value: email,\n                            onChange: (e)=>setEmail(e.target.value),\n                            autoFocus: true\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"tel\",\n                            placeholder: \"Phone number\",\n                            className: \"px-2 py-2 border rounded\",\n                            value: phone,\n                            onChange: (e)=>setPhone(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                    running: false,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_20__.ComposerPrimitiveSend, {\n                        asChild: true,\n                        disabled: !isValid,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                            tooltip: \"Send\",\n                            variant: \"default\",\n                            className: \"my-2.5 size-8 p-2 transition-opacity ease-in\",\n                            disabled: !isValid,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                    running: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_22__.ComposerPrimitiveCancel, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                            tooltip: \"Cancel\",\n                            variant: \"default\",\n                            className: \"my-2.5 size-8 p-2 transition-opacity ease-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleStopIcon, {}, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 375,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Otherwise show regular composer\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_19__.ComposerPrimitiveRoot, {\n        className: \"focus-within:border-ring/20 flex w-full flex-wrap items-end rounded-lg border bg-inherit px-2.5 shadow-sm transition-colors ease-in\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_23__.ComposerPrimitiveInput, {\n                rows: 1,\n                autoFocus: true,\n                placeholder: \"Write your answer\",\n                className: \"flex-grow px-2 py-4 text-sm bg-transparent border-none outline-none resize-none placeholder:text-muted-foreground max-h-40 focus:ring-0 disabled:cursor-not-allowed\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComposerAction, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, undefined);\n};\nconst ComposerAction = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                running: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_20__.ComposerPrimitiveSend, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                        tooltip: \"Send\",\n                        variant: \"default\",\n                        className: \"my-2.5 size-8 p-2 transition-opacity ease-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 437,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_13__.ThreadPrimitiveIf, {\n                running: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_22__.ComposerPrimitiveCancel, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                        tooltip: \"Cancel\",\n                        variant: \"default\",\n                        className: \"my-2.5 size-8 p-2 transition-opacity ease-in\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircleStopIcon, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 448,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst UserMessage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_24__.MessagePrimitiveRoot, {\n        className: \"grid auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-2 [&:where(>*)]:col-start-2 w-full max-w-[var(--thread-max-width)] py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserActionBar, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-muted text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words rounded-3xl px-5 py-2.5 col-start-2 row-start-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_25__.MessagePrimitiveContent, {}, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BranchPicker, {\n                className: \"justify-end col-start-1 row-start-3 -mr-1 col-span-full\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 465,\n        columnNumber: 5\n    }, undefined);\n};\nconst UserActionBar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_26__.ActionBarPrimitiveRoot, {\n        hideWhenRunning: true,\n        autohide: \"not-last\",\n        className: \"flex flex-col items-end col-start-1 row-start-2 mr-3 mt-2.5\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_27__.ActionBarPrimitiveEdit, {\n            asChild: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                tooltip: \"Edit\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 484,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 479,\n        columnNumber: 5\n    }, undefined);\n};\nconst EditComposer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_19__.ComposerPrimitiveRoot, {\n        className: \"bg-muted my-4 flex w-full max-w-[var(--thread-max-width)] flex-col gap-2 rounded-xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_23__.ComposerPrimitiveInput, {\n                className: \"flex w-full h-8 p-4 pb-0 bg-transparent outline-none resize-none text-foreground\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center self-end justify-center gap-2 mx-3 mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_22__.ComposerPrimitiveCancel, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_20__.ComposerPrimitiveSend, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            children: \"Send\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 498,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 495,\n        columnNumber: 5\n    }, undefined);\n};\nconst AssistantMessage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { messages } = (0,_assistant_ui_react__WEBPACK_IMPORTED_MODULE_17__.useThread)(); // removed conversationId\n    const [showThemesButton, setShowThemesButton] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Check if the last user message contains image selection (question #5)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!messages || messages.length === 0) return;\n        // Look for a user message about image selection\n        let foundImageSelection = false;\n        let foundAssistantResponseAfterImages = false;\n        for(let i = messages.length - 1; i >= 0; i--){\n            const message = messages[i];\n            // Check if this is a user message with image selection\n            if (message.role === \"user\") {\n                const content = Array.isArray(message.content) ? message.content.map((c)=>typeof c === \"string\" ? c : c.text || \"\").join(\" \") : message.content;\n                if (typeof content === \"string\" && content.toLowerCase().includes(\"selected images for my website\")) {\n                    foundImageSelection = true;\n                }\n            }\n            // Check if there's an assistant message after the image selection\n            if (foundImageSelection && message.role === \"assistant\") {\n                foundAssistantResponseAfterImages = true;\n                break;\n            }\n        }\n        setShowThemesButton(foundImageSelection && foundAssistantResponseAfterImages);\n    }, [\n        messages\n    ]);\n    const navigateToThemes = async ()=>{\n        if (!messages || !Array.isArray(messages) || messages.length === 0) {\n            router.push(\"/themes\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const res = await fetch(\"/api/theme-keywords\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    messages\n                })\n            });\n            const data = await res.json();\n            if (data.keywords && Array.isArray(data.keywords)) {\n                // Pass keywords as query param\n                const params = new URLSearchParams({\n                    keywords: data.keywords.join(\",\")\n                });\n                router.push(`/themes?${params.toString()}`);\n            } else {\n                router.push(\"/themes\");\n            }\n        } catch (e) {\n            router.push(\"/themes\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_24__.MessagePrimitiveRoot, {\n        className: \"grid grid-cols-[auto_auto_1fr] grid-rows-[auto_auto_1fr] relative w-full max-w-[var(--thread-max-width)] py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-foreground max-w-[calc(var(--thread-max-width)*0.8)] break-words leading-7 col-span-2 col-start-2 row-start-1 my-1.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_25__.MessagePrimitiveContent, {\n                        components: {\n                            Text: _components_assistant_ui_markdown_text__WEBPACK_IMPORTED_MODULE_6__.MarkdownText\n                        }\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 9\n                    }, undefined),\n                    showThemesButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            onClick: navigateToThemes,\n                            className: \"flex items-center gap-2 text-white bg-blue-600 hover:bg-blue-700\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 15\n                                }, undefined),\n                                loading ? \"Loading...\" : \"View Website Themes\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 583,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 579,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AssistantActionBar, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 596,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BranchPicker, {\n                className: \"col-start-2 row-start-2 mr-2 -ml-2\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 598,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 578,\n        columnNumber: 5\n    }, undefined);\n};\nconst AssistantActionBar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_26__.ActionBarPrimitiveRoot, {\n        hideWhenRunning: true,\n        autohide: \"not-last\",\n        autohideFloat: \"single-branch\",\n        className: \"text-muted-foreground flex gap-1 col-start-3 row-start-2 -ml-1 data-[floating]:bg-background data-[floating]:absolute data-[floating]:rounded-md data-[floating]:border data-[floating]:p-1 data-[floating]:shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_30__.ActionBarPrimitiveCopy, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                    tooltip: \"Copy\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_31__.MessagePrimitiveIf, {\n                            copied: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_31__.MessagePrimitiveIf, {\n                            copied: false,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {}, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 612,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 611,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_34__.ActionBarPrimitiveReload, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                    tooltip: \"Refresh\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 623,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 605,\n        columnNumber: 5\n    }, undefined);\n};\nconst BranchPicker = ({ className, ...rest })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_36__.BranchPickerPrimitiveRoot, {\n        hideWhenSingleBranch: true,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"text-muted-foreground inline-flex items-center text-xs\", className),\n        ...rest,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_37__.BranchPickerPrimitivePrevious, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                    tooltip: \"Previous\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 643,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-medium\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_39__.BranchPickerPrimitiveNumber, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, undefined),\n                    \" / \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_40__.BranchPickerPrimitiveCount, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 44\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 648,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assistant_ui_react__WEBPACK_IMPORTED_MODULE_41__.BranchPickerPrimitiveNext, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_assistant_ui_tooltip_icon_button__WEBPACK_IMPORTED_MODULE_7__.TooltipIconButton, {\n                    tooltip: \"Next\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_CheckIcon_ChevronLeftIcon_ChevronRightIcon_CopyIcon_LayoutGridIcon_PencilIcon_RefreshCwIcon_SendHorizontalIcon_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                    lineNumber: 652,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n                lineNumber: 651,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 635,\n        columnNumber: 5\n    }, undefined);\n};\nconst CircleStopIcon = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 16 16\",\n        fill: \"currentColor\",\n        width: \"16\",\n        height: \"16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n            width: \"10\",\n            height: \"10\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n            lineNumber: 669,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/thread.tsx\",\n        lineNumber: 662,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/assistant-ui/thread.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/assistant-ui/tooltip-icon-button.tsx":
/*!*************************************************************!*\
  !*** ./src/components/assistant-ui/tooltip-icon-button.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TooltipIconButton: () => (/* binding */ TooltipIconButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(ssr)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ TooltipIconButton auto */ \n\n\n\n\nconst TooltipIconButton = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ children, tooltip, side = \"bottom\", className, ...rest }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    ...rest,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-6 p-1\", className),\n                    ref: ref,\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: tooltip\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                side: side,\n                children: tooltip\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/assistant-ui/tooltip-icon-button.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n});\nTooltipIconButton.displayName = \"TooltipIconButton\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/assistant-ui/tooltip-icon-button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Provider;\nconst Tooltip = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TooltipTrigger = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ui/tooltip.tsx\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ui/tooltip.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/tooltip.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/pexels-api.ts":
/*!*******************************!*\
  !*** ./src/lib/pexels-api.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractKeywordsFromConversation: () => (/* binding */ extractKeywordsFromConversation),\n/* harmony export */   searchPexelsImages: () => (/* binding */ searchPexelsImages)\n/* harmony export */ });\n// Pexels API integration\nasync function searchPexelsImages(query, perPage = 15) {\n    try {\n        // Instead of calling Pexels API directly, use our server-side API route\n        // This avoids CORS issues and keeps the API key secure on the server\n        const response = await fetch(`/api/images?query=${encodeURIComponent(query)}&perPage=${perPage}`);\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(`API error: ${response.status} - ${errorData.error || \"Unknown error\"}`);\n        }\n        const data = await response.json();\n        return data.photos || [];\n    } catch (error) {\n        console.error(\"Error fetching images:\", error);\n        return [];\n    }\n}\nasync function extractKeywordsFromConversation(messages) {\n    try {\n        // Call our AI-powered keyword extraction endpoint\n        const response = await fetch(\"/api/extract-keywords\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                messages\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`API error: ${response.status}`);\n        }\n        const data = await response.json();\n        // If we got keywords from the AI, use them\n        if (data.keywords && Array.isArray(data.keywords) && data.keywords.length > 0) {\n            console.log(\"AI extracted keywords:\", data.keywords);\n            return data.keywords;\n        }\n        // Fall back to the original method if AI extraction fails\n        return fallbackKeywordExtraction(messages);\n    } catch (error) {\n        console.error(\"Error extracting keywords with AI:\", error);\n        // Fall back to the original method\n        return fallbackKeywordExtraction(messages);\n    }\n}\n// Keep the original method as a fallback\nfunction fallbackKeywordExtraction(messages) {\n    console.log(\"Using fallback keyword extraction\");\n    // Extract keywords from the last user message\n    const lastUserMessage = [\n        ...messages\n    ].reverse().find((msg)=>msg.role === \"user\");\n    if (!lastUserMessage || !lastUserMessage.content) {\n        return [\n            \"business\"\n        ];\n    }\n    const content = typeof lastUserMessage.content === \"string\" ? lastUserMessage.content : lastUserMessage.content.map((c)=>typeof c === \"string\" ? c : c.text || \"\").join(\" \");\n    // Simple keyword extraction - remove common words and punctuation\n    const commonWords = [\n        \"and\",\n        \"the\",\n        \"is\",\n        \"in\",\n        \"to\",\n        \"of\",\n        \"for\",\n        \"with\",\n        \"a\",\n        \"an\",\n        \"our\",\n        \"we\",\n        \"i\",\n        \"my\"\n    ];\n    const words = content.toLowerCase().replace(/[^\\w\\s]/g, \"\").split(/\\s+/).filter((word)=>word.length > 3 && !commonWords.includes(word));\n    // Get unique keywords - without using Set spread to avoid compatibility issues\n    const uniqueWords = [];\n    for (const word of words){\n        if (!uniqueWords.includes(word)) {\n            uniqueWords.push(word);\n        }\n    }\n    // Return up to 3 most relevant keywords, or fallback to \"business\" if none found\n    return uniqueWords.slice(0, 3).length > 0 ? uniqueWords.slice(0, 3) : [\n        \"business\"\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/pexels-api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvbGliL3V0aWxzLnRzPzdjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/assistant/page.tsx":
/*!************************************!*\
  !*** ./src/app/assistant/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/assistant/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRXZCLE1BQU1BLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1dvcmRQcmVzcyBBSSBCdWlsZGVyJyxcbiAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgV29yZFByZXNzIHNpdGUgZ2VuZXJhdGlvbiB3aXRoIEFJJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/lucide-react","vendor-chunks/@assistant-ui","vendor-chunks/zod-to-json-schema","vendor-chunks/mdast-util-to-markdown","vendor-chunks/assistant-stream","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/@floating-ui","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/@babel","vendor-chunks/style-to-js","vendor-chunks/zod","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-gfm","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/classnames","vendor-chunks/use-latest","vendor-chunks/use-isomorphic-layout-effect","vendor-chunks/use-composed-ref","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/react-textarea-autosize","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fassistant%2Fpage&page=%2Fassistant%2Fpage&appPaths=%2Fassistant%2Fpage&pagePath=private-next-app-dir%2Fassistant%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();