/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/theme1/page";
exports.ids = ["app/theme1/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftheme1%2Fpage&page=%2Ftheme1%2Fpage&appPaths=%2Ftheme1%2Fpage&pagePath=private-next-app-dir%2Ftheme1%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftheme1%2Fpage&page=%2Ftheme1%2Fpage&appPaths=%2Ftheme1%2Fpage&pagePath=private-next-app-dir%2Ftheme1%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'theme1',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/theme1/page.tsx */ \"(rsc)/./src/app/theme1/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/theme1/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/theme1/page\",\n        pathname: \"/theme1\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftheme1%2Fpage&page=%2Ftheme1%2Fpage&appPaths=%2Ftheme1%2Fpage&pagePath=private-next-app-dir%2Ftheme1%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Ftheme1%2Fpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Ftheme1%2Fpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/theme1/page.tsx */ \"(ssr)/./src/app/theme1/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGdGhlbWUxJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8/ZWUyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvc3JjL2FwcC90aGVtZTEvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Ftheme1%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/theme1/page.tsx":
/*!*********************************!*\
  !*** ./src/app/theme1/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Theme1Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Loader2,RefreshCw,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useCustomization__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useCustomization */ \"(ssr)/./src/hooks/useCustomization.js\");\n/* harmony import */ var _lib_wordpress_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/wordpress-api */ \"(ssr)/./src/lib/wordpress-api.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Theme1Page() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notification, setNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const iframeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use customization hook for WordPress integration\n    const { customizations, loading, errors, uploadProgress, hasUnsavedChanges, saveCustomizations, uploadLogoFile, updateColors, updateFonts, clearErrors, setIframeRef } = (0,_hooks_useCustomization__WEBPACK_IMPORTED_MODULE_5__.useCustomization)(\"theme1\");\n    const wordpressUrl = (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_6__.getWordPressUrl)();\n    // Font pairs data\n    const fontPairs = [\n        {\n            id: \"quicksand-work\",\n            name: \"Quicksand & Work Sans\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"roboto-open\",\n            name: \"Roboto & Open Sans\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"lato-source\",\n            name: \"Lato & Source Sans\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"montserrat-lato\",\n            name: \"Montserrat & Lato\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"poppins-inter\",\n            name: \"Poppins & Inter\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"playfair-source\",\n            name: \"Playfair & Source Sans\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"merriweather-open\",\n            name: \"Merriweather & Open Sans\",\n            preview: \"Ag\"\n        },\n        {\n            id: \"oswald-lato\",\n            name: \"Oswald & Lato\",\n            preview: \"Ag\"\n        }\n    ];\n    // Color palettes data\n    const colorPalettes = [\n        {\n            id: \"original\",\n            name: \"Original\",\n            colors: [\n                \"#10b981\",\n                \"#059669\"\n            ]\n        },\n        {\n            id: \"purple\",\n            name: \"Purple\",\n            colors: [\n                \"#8b5cf6\",\n                \"#7c3aed\"\n            ]\n        },\n        {\n            id: \"blue\",\n            name: \"Blue\",\n            colors: [\n                \"#3b82f6\",\n                \"#2563eb\"\n            ]\n        },\n        {\n            id: \"green\",\n            name: \"Green\",\n            colors: [\n                \"#22c55e\",\n                \"#16a34a\"\n            ]\n        },\n        {\n            id: \"red\",\n            name: \"Red\",\n            colors: [\n                \"#ef4444\",\n                \"#dc2626\"\n            ]\n        },\n        {\n            id: \"orange\",\n            name: \"Orange\",\n            colors: [\n                \"#f97316\",\n                \"#ea580c\"\n            ]\n        },\n        {\n            id: \"pink\",\n            name: \"Pink\",\n            colors: [\n                \"#ec4899\",\n                \"#db2777\"\n            ]\n        },\n        {\n            id: \"cyan\",\n            name: \"Cyan\",\n            colors: [\n                \"#06b6d4\",\n                \"#0891b2\"\n            ]\n        },\n        {\n            id: \"teal\",\n            name: \"Teal\",\n            colors: [\n                \"#14b8a6\",\n                \"#0d9488\"\n            ]\n        },\n        {\n            id: \"indigo\",\n            name: \"Indigo\",\n            colors: [\n                \"#6366f1\",\n                \"#4f46e5\"\n            ]\n        },\n        {\n            id: \"yellow\",\n            name: \"Yellow\",\n            colors: [\n                \"#eab308\",\n                \"#ca8a04\"\n            ]\n        },\n        {\n            id: \"slate\",\n            name: \"Slate\",\n            colors: [\n                \"#64748b\",\n                \"#475569\"\n            ]\n        }\n    ];\n    const handleIframeLoad = ()=>{\n        setIsLoading(false);\n        setHasError(false);\n    };\n    const handleIframeError = ()=>{\n        setIsLoading(false);\n        setHasError(true);\n    };\n    const refreshIframe = ()=>{\n        setIsLoading(true);\n        setHasError(false);\n        // Force iframe reload by changing the src\n        const iframe = document.querySelector(\"iframe\");\n        if (iframe) {\n            iframe.src = iframe.src;\n        }\n    };\n    const openInNewTab = ()=>{\n        window.open(wordpressUrl, \"_blank\");\n    };\n    // Set iframe reference when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (iframeRef.current) {\n            setIframeRef(iframeRef.current);\n        }\n    }, [\n        setIframeRef\n    ]);\n    // Show notifications for errors and success\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (errors.upload) {\n            setNotification({\n                type: \"error\",\n                message: errors.upload\n            });\n        } else if (errors.save) {\n            setNotification({\n                type: \"error\",\n                message: errors.save\n            });\n        } else if (errors.preview) {\n            setNotification({\n                type: \"error\",\n                message: errors.preview\n            });\n        }\n    }, [\n        errors\n    ]);\n    // Clear notifications after 5 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (notification) {\n            const timer = setTimeout(()=>{\n                setNotification(null);\n                clearErrors();\n            }, 5000);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        notification,\n        clearErrors\n    ]);\n    const handleLogoUpload = async (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            const result = await uploadLogoFile(file);\n            if (result.success) {\n                setNotification({\n                    type: \"success\",\n                    message: \"Logo uploaded successfully!\"\n                });\n            }\n        }\n    };\n    const handleFontChange = async (fontId)=>{\n        const selectedFont = fontPairs.find((f)=>f.id === fontId);\n        if (selectedFont) {\n            await updateFonts({\n                primary: selectedFont.name.split(\" & \")[0],\n                secondary: selectedFont.name.split(\" & \")[1],\n                pair_id: fontId\n            });\n        }\n    };\n    const handleColorChange = async (paletteId)=>{\n        const selectedPalette = colorPalettes.find((p)=>p.id === paletteId);\n        if (selectedPalette) {\n            await updateColors({\n                primary: selectedPalette.colors[0],\n                secondary: selectedPalette.colors[1],\n                palette_id: paletteId\n            });\n        }\n    };\n    const handleSaveCustomizations = async ()=>{\n        const result = await saveCustomizations();\n        if (result.success) {\n            setNotification({\n                type: \"success\",\n                message: \"Customizations saved successfully!\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                className: `fixed z-50 text-white shadow-lg top-4 transition-all duration-300 bg-slate-800 hover:bg-slate-700 ${sidebarOpen ? \"left-[21rem] sm:left-[25rem]\" : \"left-4\"}`,\n                size: \"icon\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-bold\",\n                    children: sidebarOpen ? \"‹\" : \"›\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    height: \"100vh\"\n                },\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-10 flex items-center justify-center bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Loading WordPress theme...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, this),\n                    hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-10 flex items-center justify-center bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md p-6 mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 text-red-500\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-16 h-16 mx-auto\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"mb-2 text-lg font-semibold\",\n                                    children: \"Unable to Load WordPress Site\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-4 text-gray-600\",\n                                    children: [\n                                        \"The WordPress site at \",\n                                        wordpressUrl,\n                                        \" could not be loaded. This might be due to:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"mb-4 text-sm text-left text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• The server is not running\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Network connectivity issues\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• CORS or security restrictions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• The URL is incorrect\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: refreshIframe,\n                                            variant: \"outline\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: openInNewTab,\n                                            children: \"Open in New Tab\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                        ref: iframeRef,\n                        src: wordpressUrl,\n                        className: \"w-full h-full border-0\",\n                        title: \"WordPress Theme Preview\",\n                        allow: \"fullscreen\",\n                        onLoad: handleIframeLoad,\n                        onError: handleIframeError,\n                        sandbox: \"allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed top-0 left-0 h-full w-80 sm:w-96 bg-slate-800 text-white shadow-2xl transform transition-transform duration-300 ease-in-out z-40 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-slate-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center w-8 h-8 bg-orange-500 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-bold text-white\",\n                                            children: \"\\uD83C\\uDFA8\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setSidebarOpen(false),\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-white hover:bg-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-4 space-y-6 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-3 text-sm font-medium\",\n                                            children: \"Site Logo\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        customizations.logo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg border-slate-600 bg-slate-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: customizations.logo.url,\n                                                        alt: customizations.logo.alt_text || \"Current logo\",\n                                                        width: 200,\n                                                        height: 80,\n                                                        className: \"object-contain w-full h-20 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"file\",\n                                                            accept: \"image/*\",\n                                                            onChange: handleLogoUpload,\n                                                            disabled: loading.uploading,\n                                                            className: \"hidden\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 text-center transition-colors border border-dashed rounded-lg cursor-pointer border-slate-600 hover:border-slate-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-400\",\n                                                                children: loading.uploading ? \"Uploading...\" : \"Change Logo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleLogoUpload,\n                                                    disabled: loading.uploading,\n                                                    className: \"hidden\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 text-center transition-colors border-2 border-dashed rounded-lg cursor-pointer border-slate-600 hover:border-slate-500\",\n                                                    children: [\n                                                        loading.uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            size: 20,\n                                                            className: \"mx-auto mb-2 text-slate-400 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            size: 20,\n                                                            className: \"mx-auto mb-2 text-slate-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-400\",\n                                                            children: loading.uploading ? \"Uploading...\" : \"Upload File Here\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-3 text-sm font-medium\",\n                                            children: [\n                                                \"Font Pair: \",\n                                                fontPairs.find((f)=>f.id === customizations.fonts?.pair_id)?.name || \"Quicksand & Work Sans\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-2\",\n                                            children: fontPairs.slice(0, 8).map((font)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleFontChange(font.id),\n                                                    disabled: loading.previewing,\n                                                    className: `aspect-square rounded-lg border-2 flex items-center justify-center text-lg font-bold transition-colors ${customizations.fonts?.pair_id === font.id ? \"border-orange-500 bg-slate-700\" : \"border-slate-600 hover:border-slate-500\"} ${loading.previewing ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                                    children: loading.previewing && customizations.fonts?.pair_id === font.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 16,\n                                                        className: \"animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this) : font.preview\n                                                }, font.id, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-3 text-sm font-medium\",\n                                            children: [\n                                                \"Color Palette: \",\n                                                colorPalettes.find((p)=>p.id === customizations.colors?.palette_id)?.name || \"Original\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-3\",\n                                            children: colorPalettes.map((palette)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleColorChange(palette.id),\n                                                    disabled: loading.previewing,\n                                                    className: `p-3 rounded-lg border-2 transition-colors ${customizations.colors?.palette_id === palette.id ? \"border-orange-500 bg-slate-700\" : \"border-slate-600 hover:border-slate-500\"} ${loading.previewing ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-1.5 justify-center\",\n                                                            children: palette.colors.map((color, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 border rounded-full border-slate-500\",\n                                                                    style: {\n                                                                        backgroundColor: color\n                                                                    }\n                                                                }, index, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        loading.previewing && customizations.colors?.palette_id === palette.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-center mt-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                size: 12,\n                                                                className: \"text-orange-500 animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, palette.id, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-3 border-t border-slate-700\",\n                            children: [\n                                loading.uploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Uploading logo...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        Math.round(uploadProgress),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-2 rounded-full bg-slate-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-2 transition-all duration-300 bg-orange-500 rounded-full\",\n                                                style: {\n                                                    width: `${uploadProgress}%`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSaveCustomizations,\n                                    disabled: loading.saving || !hasUnsavedChanges,\n                                    className: \"w-full text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading.saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16,\n                                                className: \"mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Saving...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            hasUnsavedChanges ? \"Save Changes\" : \"Saved\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 16,\n                                                className: \"ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/themes\"),\n                                    className: \"w-full text-sm transition-colors text-slate-400 hover:text-white\",\n                                    children: \"Back to Other Designs\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black bg-opacity-50\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, this),\n            notification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed z-50 max-w-sm top-4 right-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `p-4 rounded-lg shadow-lg border ${notification.type === \"error\" ? \"bg-red-50 border-red-200 text-red-800\" : \"bg-green-50 border-green-200 text-green-800\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            notification.type === \"error\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20,\n                                className: \"text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                className: \"text-green-500\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: notification.message\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setNotification(null),\n                                className: \"ml-auto text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                lineNumber: 422,\n                columnNumber: 9\n            }, this),\n            loading.initial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 flex items-center justify-center bg-white bg-opacity-90\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Loader2_RefreshCw_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-8 h-8 mx-auto mb-2 text-blue-500 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Loading customizations...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n                lineNumber: 450,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/theme1/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4Qix5U0FDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4PzZhMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTEgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIFsmX3N2Z106cG9pbnRlci1ldmVudHMtbm9uZSBbJl9zdmddOnNpemUtNCBbJl9zdmddOnNocmluay0wXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBzaGFkb3cgaG92ZXI6YmctcHJpbWFyeS85MFwiLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBzaGFkb3ctc20gaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBzaGFkb3ctc20gaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXNtIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxuICAgICAgICBnaG9zdDogXCJob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBsaW5rOiBcInRleHQtcHJpbWFyeSB1bmRlcmxpbmUtb2Zmc2V0LTQgaG92ZXI6dW5kZXJsaW5lXCIsXG4gICAgICB9LFxuICAgICAgc2l6ZToge1xuICAgICAgICBkZWZhdWx0OiBcImgtOSBweC00IHB5LTJcIixcbiAgICAgICAgc206IFwiaC04IHJvdW5kZWQtbWQgcHgtMyB0ZXh0LXhzXCIsXG4gICAgICAgIGxnOiBcImgtMTAgcm91bmRlZC1tZCBweC04XCIsXG4gICAgICAgIGljb246IFwiaC05IHctOVwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb21wXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzaXplIiwic20iLCJsZyIsImljb24iLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiQ29tcCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useCustomization.js":
/*!***************************************!*\
  !*** ./src/hooks/useCustomization.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCustomization: () => (/* binding */ useCustomization)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/wordpress-api */ \"(ssr)/./src/lib/wordpress-api.js\");\n/**\n * Custom hook for managing theme customization state and API interactions\n */ \n\nconst useCustomization = (themeId = \"default-theme\")=>{\n    // State management\n    const [customizations, setCustomizations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        colors: {\n            primary: \"#3b82f6\",\n            secondary: \"#6366f1\",\n            accent: \"#ec4899\"\n        },\n        fonts: {\n            primary: \"Inter\",\n            secondary: \"Open Sans\",\n            pair_id: \"inter-opensans\"\n        },\n        logo: null\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        initial: true,\n        saving: false,\n        uploading: false,\n        previewing: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        load: null,\n        save: null,\n        upload: null,\n        preview: null\n    });\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const iframeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const cleanupMessageListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    /**\n   * Load initial customizations from WordPress\n   */ const loadCustomizations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            setLoading((prev)=>({\n                    ...prev,\n                    initial: true\n                }));\n            setErrors((prev)=>({\n                    ...prev,\n                    load: null\n                }));\n            const data = await (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.getThemeCustomizations)(themeId);\n            if ((0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.validateCustomizations)(data)) {\n                setCustomizations(data);\n            } else {\n                console.warn(\"Invalid customization data received, using defaults\");\n            }\n        } catch (error) {\n            console.error(\"Failed to load customizations:\", error);\n            setErrors((prev)=>({\n                    ...prev,\n                    load: error.message\n                }));\n        } finally{\n            setLoading((prev)=>({\n                    ...prev,\n                    initial: false\n                }));\n        }\n    }, [\n        themeId\n    ]);\n    /**\n   * Save customizations to WordPress\n   */ const saveCustomizations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        try {\n            setLoading((prev)=>({\n                    ...prev,\n                    saving: true\n                }));\n            setErrors((prev)=>({\n                    ...prev,\n                    save: null\n                }));\n            if (!(0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.validateCustomizations)(customizations)) {\n                throw new Error(\"Invalid customization data\");\n            }\n            await (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.saveThemeCustomizations)(themeId, customizations);\n            setHasUnsavedChanges(false);\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Failed to save customizations:\", error);\n            setErrors((prev)=>({\n                    ...prev,\n                    save: error.message\n                }));\n            return {\n                success: false,\n                error: error.message\n            };\n        } finally{\n            setLoading((prev)=>({\n                    ...prev,\n                    saving: false\n                }));\n        }\n    }, [\n        themeId,\n        customizations\n    ]);\n    /**\n   * Preview customizations without saving\n   */ const previewCustomizations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (previewData = null)=>{\n        try {\n            setLoading((prev)=>({\n                    ...prev,\n                    previewing: true\n                }));\n            setErrors((prev)=>({\n                    ...prev,\n                    preview: null\n                }));\n            const dataToPreview = previewData || customizations;\n            if (!(0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.validateCustomizations)(dataToPreview)) {\n                throw new Error(\"Invalid customization data for preview\");\n            }\n            // Send to WordPress API for server-side preview\n            await (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.previewThemeCustomizations)(dataToPreview);\n            // Send to iframe for real-time preview\n            if (iframeRef.current) {\n                (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.sendCustomizationToIframe)(iframeRef.current, dataToPreview);\n            }\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error(\"Failed to preview customizations:\", error);\n            setErrors((prev)=>({\n                    ...prev,\n                    preview: error.message\n                }));\n            return {\n                success: false,\n                error: error.message\n            };\n        } finally{\n            setLoading((prev)=>({\n                    ...prev,\n                    previewing: false\n                }));\n        }\n    }, [\n        customizations\n    ]);\n    /**\n   * Upload logo file\n   */ const uploadLogoFile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (file)=>{\n        try {\n            setLoading((prev)=>({\n                    ...prev,\n                    uploading: true\n                }));\n            setErrors((prev)=>({\n                    ...prev,\n                    upload: null\n                }));\n            setUploadProgress(0);\n            const result = await (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.uploadLogo)(file, (progress)=>{\n                setUploadProgress(progress);\n            });\n            // Update customizations with new logo\n            const newCustomizations = {\n                ...customizations,\n                logo: {\n                    url: result.url,\n                    attachment_id: result.attachment_id,\n                    alt_text: result.alt_text || file.name\n                }\n            };\n            setCustomizations(newCustomizations);\n            setHasUnsavedChanges(true);\n            // Preview the new logo immediately\n            await previewCustomizations(newCustomizations);\n            return {\n                success: true,\n                data: result\n            };\n        } catch (error) {\n            console.error(\"Failed to upload logo:\", error);\n            setErrors((prev)=>({\n                    ...prev,\n                    upload: error.message\n                }));\n            return {\n                success: false,\n                error: error.message\n            };\n        } finally{\n            setLoading((prev)=>({\n                    ...prev,\n                    uploading: false\n                }));\n            setUploadProgress(0);\n        }\n    }, [\n        customizations,\n        previewCustomizations\n    ]);\n    /**\n   * Update color palette\n   */ const updateColors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (colors)=>{\n        const newCustomizations = {\n            ...customizations,\n            colors: {\n                ...customizations.colors,\n                ...colors\n            }\n        };\n        setCustomizations(newCustomizations);\n        setHasUnsavedChanges(true);\n        // Preview changes immediately\n        await previewCustomizations(newCustomizations);\n    }, [\n        customizations,\n        previewCustomizations\n    ]);\n    /**\n   * Update font pair\n   */ const updateFonts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (fonts)=>{\n        const newCustomizations = {\n            ...customizations,\n            fonts: {\n                ...customizations.fonts,\n                ...fonts\n            }\n        };\n        setCustomizations(newCustomizations);\n        setHasUnsavedChanges(true);\n        // Preview changes immediately\n        await previewCustomizations(newCustomizations);\n    }, [\n        customizations,\n        previewCustomizations\n    ]);\n    /**\n   * Reset customizations to defaults\n   */ const resetCustomizations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        const defaultCustomizations = {\n            colors: {\n                primary: \"#3b82f6\",\n                secondary: \"#6366f1\",\n                accent: \"#ec4899\"\n            },\n            fonts: {\n                primary: \"Inter\",\n                secondary: \"Open Sans\",\n                pair_id: \"inter-opensans\"\n            },\n            logo: null\n        };\n        setCustomizations(defaultCustomizations);\n        setHasUnsavedChanges(true);\n        // Preview reset changes\n        await previewCustomizations(defaultCustomizations);\n    }, [\n        previewCustomizations\n    ]);\n    /**\n   * Clear all errors\n   */ const clearErrors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setErrors({\n            load: null,\n            save: null,\n            upload: null,\n            preview: null\n        });\n    }, []);\n    /**\n   * Set iframe reference for communication\n   */ const setIframeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((iframe)=>{\n        iframeRef.current = iframe;\n    }, []);\n    // Load initial customizations on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        loadCustomizations();\n    }, [\n        loadCustomizations\n    ]);\n    // Set up iframe message listener\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const cleanup = (0,_lib_wordpress_api__WEBPACK_IMPORTED_MODULE_1__.setupIframeMessageListener)((message)=>{\n            console.log(\"Received message from WordPress iframe:\", message);\n            // Handle different message types from WordPress\n            switch(message.type){\n                case \"CUSTOMIZATION_APPLIED\":\n                    console.log(\"Customization successfully applied in WordPress\");\n                    break;\n                case \"CUSTOMIZATION_ERROR\":\n                    console.error(\"Customization error in WordPress:\", message.error);\n                    setErrors((prev)=>({\n                            ...prev,\n                            preview: message.error\n                        }));\n                    break;\n                default:\n                    console.log(\"Unknown message type:\", message.type);\n            }\n        });\n        cleanupMessageListener.current = cleanup;\n        return ()=>{\n            if (cleanupMessageListener.current) {\n                cleanupMessageListener.current();\n            }\n        };\n    }, []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            if (cleanupMessageListener.current) {\n                cleanupMessageListener.current();\n            }\n        };\n    }, []);\n    return {\n        // State\n        customizations,\n        loading,\n        errors,\n        uploadProgress,\n        hasUnsavedChanges,\n        // Actions\n        loadCustomizations,\n        saveCustomizations,\n        previewCustomizations,\n        uploadLogoFile,\n        updateColors,\n        updateFonts,\n        resetCustomizations,\n        clearErrors,\n        setIframeRef,\n        // Utilities\n        isLoading: Object.values(loading).some(Boolean),\n        hasErrors: Object.values(errors).some(Boolean)\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useCustomization.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvbGliL3V0aWxzLnRzPzdjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/wordpress-api.js":
/*!**********************************!*\
  !*** ./src/lib/wordpress-api.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getThemeCustomizations: () => (/* binding */ getThemeCustomizations),\n/* harmony export */   getWordPressUrl: () => (/* binding */ getWordPressUrl),\n/* harmony export */   previewThemeCustomizations: () => (/* binding */ previewThemeCustomizations),\n/* harmony export */   saveThemeCustomizations: () => (/* binding */ saveThemeCustomizations),\n/* harmony export */   sendCustomizationToIframe: () => (/* binding */ sendCustomizationToIframe),\n/* harmony export */   setupIframeMessageListener: () => (/* binding */ setupIframeMessageListener),\n/* harmony export */   uploadLogo: () => (/* binding */ uploadLogo),\n/* harmony export */   validateCustomizations: () => (/* binding */ validateCustomizations)\n/* harmony export */ });\n/**\n * WordPress API Service Layer\n * Handles all WordPress REST API communication for theme customization\n */ // Configuration\nconst WP_BASE_URL = \"http://192.168.29.49/wordpress\";\nconst WP_API_BASE = `${WP_BASE_URL}/wp-json`;\nconst CUSTOMIZER_API_BASE = `${WP_API_BASE}/customizer/v1`;\n// Default headers for API requests\nconst getDefaultHeaders = ()=>({\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\",\n        // Add CORS headers\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"GET, POST, PUT, DELETE, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    });\n// Authentication headers (to be implemented with WordPress Application Passwords)\nconst getAuthHeaders = ()=>{\n    // TODO: Implement authentication with WordPress Application Passwords or JWT\n    // For now, return empty object - will be enhanced when WordPress backend is ready\n    return {};\n};\n/**\n * Generic API request handler with error handling and retry logic\n */ const apiRequest = async (url, options = {}, retries = 3)=>{\n    const defaultOptions = {\n        headers: {\n            ...getDefaultHeaders(),\n            ...getAuthHeaders(),\n            ...options.headers\n        }\n    };\n    const requestOptions = {\n        ...defaultOptions,\n        ...options,\n        headers: {\n            ...defaultOptions.headers,\n            ...options.headers\n        }\n    };\n    for(let attempt = 1; attempt <= retries; attempt++){\n        try {\n            const response = await fetch(url, requestOptions);\n            // Handle different response types\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n            }\n            // Return JSON response\n            return await response.json();\n        } catch (error) {\n            console.error(`API request attempt ${attempt} failed:`, error);\n            // If this is the last attempt, throw the error\n            if (attempt === retries) {\n                throw new Error(`API request failed after ${retries} attempts: ${error.message}`);\n            }\n            // Wait before retrying (exponential backoff)\n            await new Promise((resolve)=>setTimeout(resolve, Math.pow(2, attempt) * 1000));\n        }\n    }\n};\n/**\n * Theme Customization API Functions\n */ /**\n * Retrieve current customizations for a theme\n * @param {string} themeId - The theme identifier\n * @returns {Promise<Object>} Current customization settings\n */ const getThemeCustomizations = async (themeId)=>{\n    try {\n        const url = `${CUSTOMIZER_API_BASE}/themes/${themeId}`;\n        return await apiRequest(url);\n    } catch (error) {\n        console.error(\"Failed to get theme customizations:\", error);\n        // Return default customizations if API fails\n        return {\n            colors: {\n                primary: \"#3b82f6\",\n                secondary: \"#6366f1\",\n                accent: \"#ec4899\"\n            },\n            fonts: {\n                primary: \"Inter\",\n                secondary: \"Open Sans\",\n                pair_id: \"inter-opensans\"\n            },\n            logo: null\n        };\n    }\n};\n/**\n * Save customizations for a theme\n * @param {string} themeId - The theme identifier\n * @param {Object} customizations - The customization settings to save\n * @returns {Promise<Object>} Save operation result\n */ const saveThemeCustomizations = async (themeId, customizations)=>{\n    try {\n        const url = `${CUSTOMIZER_API_BASE}/themes/${themeId}`;\n        return await apiRequest(url, {\n            method: \"POST\",\n            body: JSON.stringify({\n                theme_id: themeId,\n                customizations,\n                timestamp: new Date().toISOString()\n            })\n        });\n    } catch (error) {\n        console.error(\"Failed to save theme customizations:\", error);\n        throw error;\n    }\n};\n/**\n * Apply temporary preview changes without saving\n * @param {Object} customizations - The customization settings to preview\n * @returns {Promise<Object>} Preview operation result\n */ const previewThemeCustomizations = async (customizations)=>{\n    try {\n        const url = `${CUSTOMIZER_API_BASE}/preview`;\n        return await apiRequest(url, {\n            method: \"POST\",\n            body: JSON.stringify({\n                customizations,\n                preview: true,\n                timestamp: new Date().toISOString()\n            })\n        });\n    } catch (error) {\n        console.error(\"Failed to preview theme customizations:\", error);\n        throw error;\n    }\n};\n/**\n * Upload logo file to WordPress Media Library\n * @param {File} file - The logo file to upload\n * @param {Function} onProgress - Progress callback function\n * @returns {Promise<Object>} Upload result with media URL and attachment ID\n */ const uploadLogo = async (file, onProgress = null)=>{\n    try {\n        // Validate file type\n        const allowedTypes = [\n            \"image/jpeg\",\n            \"image/jpg\",\n            \"image/png\",\n            \"image/gif\",\n            \"image/webp\"\n        ];\n        if (!allowedTypes.includes(file.type.toLowerCase())) {\n            throw new Error(\"Invalid file type. Please upload JPEG, PNG, GIF, or WebP images.\");\n        }\n        // Validate file size (max 2MB)\n        const maxSize = 2 * 1024 * 1024; // 2MB in bytes\n        if (file.size > maxSize) {\n            throw new Error(\"File size exceeds 2MB limit. Please choose a smaller file.\");\n        }\n        // Create FormData for file upload\n        const formData = new FormData();\n        formData.append(\"logo\", file);\n        formData.append(\"title\", file.name);\n        const url = `${CUSTOMIZER_API_BASE}/upload-logo`;\n        // Create XMLHttpRequest for progress tracking\n        return new Promise((resolve, reject)=>{\n            const xhr = new XMLHttpRequest();\n            // Track upload progress\n            if (onProgress) {\n                xhr.upload.addEventListener(\"progress\", (event)=>{\n                    if (event.lengthComputable) {\n                        const percentComplete = event.loaded / event.total * 100;\n                        onProgress(percentComplete);\n                    }\n                });\n            }\n            xhr.addEventListener(\"load\", ()=>{\n                if (xhr.status >= 200 && xhr.status < 300) {\n                    try {\n                        const response = JSON.parse(xhr.responseText);\n                        resolve(response);\n                    } catch (error) {\n                        reject(new Error(\"Invalid response format\"));\n                    }\n                } else {\n                    reject(new Error(`Upload failed: ${xhr.statusText}`));\n                }\n            });\n            xhr.addEventListener(\"error\", ()=>{\n                reject(new Error(\"Upload failed due to network error\"));\n            });\n            xhr.open(\"POST\", url);\n            // Add auth headers\n            const authHeaders = getAuthHeaders();\n            Object.entries(authHeaders).forEach(([key, value])=>{\n                xhr.setRequestHeader(key, value);\n            });\n            xhr.send(formData);\n        });\n    } catch (error) {\n        console.error(\"Failed to upload logo:\", error);\n        throw error;\n    }\n};\n/**\n * Iframe Communication Functions\n */ /**\n * Send customization updates to WordPress iframe for real-time preview\n * @param {HTMLIFrameElement} iframe - The WordPress iframe element\n * @param {Object} customizations - The customization settings to apply\n */ const sendCustomizationToIframe = (iframe, customizations)=>{\n    if (!iframe || !iframe.contentWindow) {\n        console.warn(\"Iframe not available for customization update\");\n        return;\n    }\n    try {\n        const message = {\n            type: \"THEME_CUSTOMIZATION_UPDATE\",\n            data: customizations,\n            timestamp: Date.now(),\n            source: \"nextjs-customizer\"\n        };\n        iframe.contentWindow.postMessage(message, WP_BASE_URL);\n    } catch (error) {\n        console.error(\"Failed to send customization to iframe:\", error);\n    }\n};\n/**\n * Set up iframe message listener for communication from WordPress\n * @param {Function} onMessage - Callback function to handle messages from iframe\n * @returns {Function} Cleanup function to remove event listener\n */ const setupIframeMessageListener = (onMessage)=>{\n    const messageHandler = (event)=>{\n        // Verify origin for security\n        if (event.origin !== WP_BASE_URL) {\n            return;\n        }\n        // Handle different message types\n        if (event.data && event.data.type) {\n            onMessage(event.data);\n        }\n    };\n    window.addEventListener(\"message\", messageHandler);\n    // Return cleanup function\n    return ()=>{\n        window.removeEventListener(\"message\", messageHandler);\n    };\n};\n/**\n * Utility Functions\n */ /**\n * Validate customization data structure\n * @param {Object} customizations - The customization object to validate\n * @returns {boolean} Whether the customizations are valid\n */ const validateCustomizations = (customizations)=>{\n    if (!customizations || typeof customizations !== \"object\") {\n        return false;\n    }\n    // Validate colors\n    if (customizations.colors) {\n        const { colors } = customizations;\n        if (typeof colors !== \"object\") return false;\n        // Validate color format (hex colors)\n        const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;\n        for (const [key, value] of Object.entries(colors)){\n            if (typeof value === \"string\" && !colorRegex.test(value)) {\n                console.warn(`Invalid color format for ${key}: ${value}`);\n                return false;\n            }\n        }\n    }\n    // Validate fonts\n    if (customizations.fonts) {\n        const { fonts } = customizations;\n        if (typeof fonts !== \"object\") return false;\n        // Check required font properties\n        if (fonts.primary && typeof fonts.primary !== \"string\") return false;\n        if (fonts.secondary && typeof fonts.secondary !== \"string\") return false;\n    }\n    return true;\n};\n/**\n * Get WordPress base URL for iframe src\n * @returns {string} WordPress base URL\n */ const getWordPressUrl = ()=>WP_BASE_URL;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    getThemeCustomizations,\n    saveThemeCustomizations,\n    previewThemeCustomizations,\n    uploadLogo,\n    sendCustomizationToIframe,\n    setupIframeMessageListener,\n    validateCustomizations,\n    getWordPressUrl\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/wordpress-api.js\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBRXZCLE1BQU1BLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1dvcmRQcmVzcyBBSSBCdWlsZGVyJyxcbiAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgV29yZFByZXNzIHNpdGUgZ2VuZXJhdGlvbiB3aXRoIEFJJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/theme1/page.tsx":
/*!*********************************!*\
  !*** ./src/app/theme1/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/theme1/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftheme1%2Fpage&page=%2Ftheme1%2Fpage&appPaths=%2Ftheme1%2Fpage&pagePath=private-next-app-dir%2Ftheme1%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();