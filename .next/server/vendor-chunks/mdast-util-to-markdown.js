"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-to-markdown";
exports.ids = ["vendor-chunks/mdast-util-to-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockquote: () => (/* binding */ blockquote)\n/* harmony export */ });\n/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function blockquote(node, _, state, info) {\n    const exit = state.enter(\"blockquote\");\n    const tracker = state.createTracker(info);\n    tracker.move(\"> \");\n    tracker.shift(2);\n    const value = state.indentLines(state.containerFlow(node, tracker.current()), map);\n    exit();\n    return value;\n}\n/** @type {Map} */ function map(line, _, blank) {\n    return \">\" + (blank ? \"\" : \" \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/break.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreak: () => (/* binding */ hardBreak)\n/* harmony export */ });\n/* harmony import */ var _util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/pattern-in-scope.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\");\n/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */ \n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function hardBreak(_, _1, state, info) {\n    let index = -1;\n    while(++index < state.unsafe.length){\n        // If we can’t put eols in this construct (setext headings, tables), use a\n        // space instead.\n        if (state.unsafe[index].character === \"\\n\" && (0,_util_pattern_in_scope_js__WEBPACK_IMPORTED_MODULE_0__.patternInScope)(state.stack, state.unsafe[index])) {\n            return /[ \\t]/.test(info.before) ? \"\" : \" \";\n        }\n    }\n    return \"\\\\\\n\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2JyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7OztDQUdDLEdBRXlEO0FBRTFEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNDLFVBQVVDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxLQUFLLEVBQUVDLElBQUk7SUFDMUMsSUFBSUMsUUFBUSxDQUFDO0lBRWIsTUFBTyxFQUFFQSxRQUFRRixNQUFNRyxNQUFNLENBQUNDLE1BQU0sQ0FBRTtRQUNwQywwRUFBMEU7UUFDMUUsaUJBQWlCO1FBQ2pCLElBQ0VKLE1BQU1HLE1BQU0sQ0FBQ0QsTUFBTSxDQUFDRyxTQUFTLEtBQUssUUFDbENULHlFQUFjQSxDQUFDSSxNQUFNTSxLQUFLLEVBQUVOLE1BQU1HLE1BQU0sQ0FBQ0QsTUFBTSxHQUMvQztZQUNBLE9BQU8sUUFBUUssSUFBSSxDQUFDTixLQUFLTyxNQUFNLElBQUksS0FBSztRQUMxQztJQUNGO0lBRUEsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvYnJlYWsuanM/YzMzNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0JyZWFrLCBQYXJlbnRzfSBmcm9tICdtZGFzdCdcbiAqIEBpbXBvcnQge0luZm8sIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbmltcG9ydCB7cGF0dGVybkluU2NvcGV9IGZyb20gJy4uL3V0aWwvcGF0dGVybi1pbi1zY29wZS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge0JyZWFrfSBfXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gaGFyZEJyZWFrKF8sIF8xLCBzdGF0ZSwgaW5mbykge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgc3RhdGUudW5zYWZlLmxlbmd0aCkge1xuICAgIC8vIElmIHdlIGNhbuKAmXQgcHV0IGVvbHMgaW4gdGhpcyBjb25zdHJ1Y3QgKHNldGV4dCBoZWFkaW5ncywgdGFibGVzKSwgdXNlIGFcbiAgICAvLyBzcGFjZSBpbnN0ZWFkLlxuICAgIGlmIChcbiAgICAgIHN0YXRlLnVuc2FmZVtpbmRleF0uY2hhcmFjdGVyID09PSAnXFxuJyAmJlxuICAgICAgcGF0dGVybkluU2NvcGUoc3RhdGUuc3RhY2ssIHN0YXRlLnVuc2FmZVtpbmRleF0pXG4gICAgKSB7XG4gICAgICByZXR1cm4gL1sgXFx0XS8udGVzdChpbmZvLmJlZm9yZSkgPyAnJyA6ICcgJ1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiAnXFxcXFxcbidcbn1cbiJdLCJuYW1lcyI6WyJwYXR0ZXJuSW5TY29wZSIsImhhcmRCcmVhayIsIl8iLCJfMSIsInN0YXRlIiwiaW5mbyIsImluZGV4IiwidW5zYWZlIiwibGVuZ3RoIiwiY2hhcmFjdGVyIiwic3RhY2siLCJ0ZXN0IiwiYmVmb3JlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/code.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   code: () => (/* binding */ code)\n/* harmony export */ });\n/* harmony import */ var longest_streak__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! longest-streak */ \"(ssr)/./node_modules/longest-streak/index.js\");\n/* harmony import */ var _util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-code-as-indented.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\");\n/* harmony import */ var _util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-fence.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */ \n\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function code(node, _, state, info) {\n    const marker = (0,_util_check_fence_js__WEBPACK_IMPORTED_MODULE_0__.checkFence)(state);\n    const raw = node.value || \"\";\n    const suffix = marker === \"`\" ? \"GraveAccent\" : \"Tilde\";\n    if ((0,_util_format_code_as_indented_js__WEBPACK_IMPORTED_MODULE_1__.formatCodeAsIndented)(node, state)) {\n        const exit = state.enter(\"codeIndented\");\n        const value = state.indentLines(raw, map);\n        exit();\n        return value;\n    }\n    const tracker = state.createTracker(info);\n    const sequence = marker.repeat(Math.max((0,longest_streak__WEBPACK_IMPORTED_MODULE_2__.longestStreak)(raw, marker) + 1, 3));\n    const exit = state.enter(\"codeFenced\");\n    let value = tracker.move(sequence);\n    if (node.lang) {\n        const subexit = state.enter(`codeFencedLang${suffix}`);\n        value += tracker.move(state.safe(node.lang, {\n            before: value,\n            after: \" \",\n            encode: [\n                \"`\"\n            ],\n            ...tracker.current()\n        }));\n        subexit();\n    }\n    if (node.lang && node.meta) {\n        const subexit = state.enter(`codeFencedMeta${suffix}`);\n        value += tracker.move(\" \");\n        value += tracker.move(state.safe(node.meta, {\n            before: value,\n            after: \"\\n\",\n            encode: [\n                \"`\"\n            ],\n            ...tracker.current()\n        }));\n        subexit();\n    }\n    value += tracker.move(\"\\n\");\n    if (raw) {\n        value += tracker.move(raw + \"\\n\");\n    }\n    value += tracker.move(sequence);\n    exit();\n    return value;\n}\n/** @type {Map} */ function map(line, _, blank) {\n    return (blank ? \"\" : \"    \") + line;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/definition.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */ \n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function definition(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const exit = state.enter(\"definition\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[\");\n    value += tracker.move(state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"]: \");\n    subexit();\n    if (// If there’s no url, or…\n    !node.url || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \"\\n\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emphasis: () => (/* binding */ emphasis)\n/* harmony export */ });\n/* harmony import */ var _util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */ \n\n\nemphasis.peek = emphasisPeek;\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function emphasis(node, _, state, info) {\n    const marker = (0,_util_check_emphasis_js__WEBPACK_IMPORTED_MODULE_0__.checkEmphasis)(state);\n    const exit = state.enter(\"emphasis\");\n    const tracker = state.createTracker(info);\n    const before = tracker.move(marker);\n    let between = tracker.move(state.containerPhrasing(node, {\n        after: marker,\n        before,\n        ...tracker.current()\n    }));\n    const betweenHead = between.charCodeAt(0);\n    const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.before.charCodeAt(info.before.length - 1), betweenHead, marker);\n    if (open.inside) {\n        between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1);\n    }\n    const betweenTail = between.charCodeAt(between.length - 1);\n    const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker);\n    if (close.inside) {\n        between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail);\n    }\n    const after = tracker.move(marker);\n    exit();\n    state.attentionEncodeSurroundingInfo = {\n        after: close.outside,\n        before: open.outside\n    };\n    return before + between + after;\n}\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function emphasisPeek(_, _1, state) {\n    return state.options.emphasis || \"*\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/heading.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   heading: () => (/* binding */ heading)\n/* harmony export */ });\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/format-heading-as-setext.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */ \n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function heading(node, _, state, info) {\n    const rank = Math.max(Math.min(6, node.depth || 1), 1);\n    const tracker = state.createTracker(info);\n    if ((0,_util_format_heading_as_setext_js__WEBPACK_IMPORTED_MODULE_0__.formatHeadingAsSetext)(node, state)) {\n        const exit = state.enter(\"headingSetext\");\n        const subexit = state.enter(\"phrasing\");\n        const value = state.containerPhrasing(node, {\n            ...tracker.current(),\n            before: \"\\n\",\n            after: \"\\n\"\n        });\n        subexit();\n        exit();\n        return value + \"\\n\" + (rank === 1 ? \"=\" : \"-\").repeat(// The whole size…\n        value.length - // Minus the position of the character after the last EOL (or\n        // 0 if there is none)…\n        (Math.max(value.lastIndexOf(\"\\r\"), value.lastIndexOf(\"\\n\")) + 1));\n    }\n    const sequence = \"#\".repeat(rank);\n    const exit = state.enter(\"headingAtx\");\n    const subexit = state.enter(\"phrasing\");\n    // Note: for proper tracking, we should reset the output positions when there\n    // is no content returned, because then the space is not output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    tracker.move(sequence + \" \");\n    let value = state.containerPhrasing(node, {\n        before: \"# \",\n        after: \"\\n\",\n        ...tracker.current()\n    });\n    if (/^[\\t ]/.test(value)) {\n        // To do: what effect has the character reference on tracking?\n        value = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_1__.encodeCharacterReference)(value.charCodeAt(0)) + value.slice(1);\n    }\n    value = value ? sequence + \" \" + value : sequence;\n    if (state.options.closeAtx) {\n        value += \" \" + sequence;\n    }\n    subexit();\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/html.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/**\n * @import {Html} from 'mdast'\n */ html.peek = htmlPeek;\n/**\n * @param {Html} node\n * @returns {string}\n */ function html(node) {\n    return node.value || \"\";\n}\n/**\n * @returns {string}\n */ function htmlPeek() {\n    return \"<\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2h0bWwuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRURBLEtBQUtDLElBQUksR0FBR0M7QUFFWjs7O0NBR0MsR0FDTSxTQUFTRixLQUFLRyxJQUFJO0lBQ3ZCLE9BQU9BLEtBQUtDLEtBQUssSUFBSTtBQUN2QjtBQUVBOztDQUVDLEdBQ0QsU0FBU0Y7SUFDUCxPQUFPO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9odG1sLmpzPzcxNTIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtIdG1sfSBmcm9tICdtZGFzdCdcbiAqL1xuXG5odG1sLnBlZWsgPSBodG1sUGVla1xuXG4vKipcbiAqIEBwYXJhbSB7SHRtbH0gbm9kZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGh0bWwobm9kZSkge1xuICByZXR1cm4gbm9kZS52YWx1ZSB8fCAnJ1xufVxuXG4vKipcbiAqIEByZXR1cm5zIHtzdHJpbmd9XG4gKi9cbmZ1bmN0aW9uIGh0bWxQZWVrKCkge1xuICByZXR1cm4gJzwnXG59XG4iXSwibmFtZXMiOlsiaHRtbCIsInBlZWsiLCJodG1sUGVlayIsIm5vZGUiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js":
/*!***************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageReference: () => (/* binding */ imageReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */ imageReference.peek = imageReferencePeek;\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function imageReference(node, _, state, info) {\n    const type = node.referenceType;\n    const exit = state.enter(\"imageReference\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"![\");\n    const alt = state.safe(node.alt, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    value += tracker.move(alt + \"][\");\n    subexit();\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack;\n    state.stack = [];\n    subexit = state.enter(\"reference\");\n    // Note: for proper tracking, we should reset the output positions when we end\n    // up making a `shortcut` reference, because then there is no brace output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    const reference = state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    subexit();\n    state.stack = stack;\n    exit();\n    if (type === \"full\" || !alt || alt !== reference) {\n        value += tracker.move(reference + \"]\");\n    } else if (type === \"shortcut\") {\n        // Remove the unwanted `[`.\n        value = value.slice(0, -1);\n    } else {\n        value += tracker.move(\"]\");\n    }\n    return value;\n}\n/**\n * @returns {string}\n */ function imageReferencePeek() {\n    return \"!\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/image.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   image: () => (/* binding */ image)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */ \nimage.peek = imagePeek;\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function image(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const exit = state.enter(\"image\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"![\");\n    value += tracker.move(state.safe(node.alt, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"](\");\n    subexit();\n    if (// If there’s no url but there is a title…\n    !node.url && node.title || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \")\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    value += tracker.move(\")\");\n    exit();\n    return value;\n}\n/**\n * @returns {string}\n */ function imagePeek() {\n    return \"!\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handle: () => (/* binding */ handle)\n/* harmony export */ });\n/* harmony import */ var _blockquote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blockquote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js\");\n/* harmony import */ var _break_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/break.js\");\n/* harmony import */ var _code_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/code.js\");\n/* harmony import */ var _definition_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./definition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js\");\n/* harmony import */ var _emphasis_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./emphasis.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js\");\n/* harmony import */ var _heading_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./heading.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./html.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/html.js\");\n/* harmony import */ var _image_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./image.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image.js\");\n/* harmony import */ var _image_reference_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./image-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js\");\n/* harmony import */ var _inline_code_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./inline-code.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\");\n/* harmony import */ var _link_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./link.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\");\n/* harmony import */ var _link_reference_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./link-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\");\n/* harmony import */ var _list_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./list.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\");\n/* harmony import */ var _list_item_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list-item.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\");\n/* harmony import */ var _paragraph_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./paragraph.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./root.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\");\n/* harmony import */ var _strong_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Default (CommonMark) handlers.\n */ const handle = {\n    blockquote: _blockquote_js__WEBPACK_IMPORTED_MODULE_0__.blockquote,\n    break: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    code: _code_js__WEBPACK_IMPORTED_MODULE_2__.code,\n    definition: _definition_js__WEBPACK_IMPORTED_MODULE_3__.definition,\n    emphasis: _emphasis_js__WEBPACK_IMPORTED_MODULE_4__.emphasis,\n    hardBreak: _break_js__WEBPACK_IMPORTED_MODULE_1__.hardBreak,\n    heading: _heading_js__WEBPACK_IMPORTED_MODULE_5__.heading,\n    html: _html_js__WEBPACK_IMPORTED_MODULE_6__.html,\n    image: _image_js__WEBPACK_IMPORTED_MODULE_7__.image,\n    imageReference: _image_reference_js__WEBPACK_IMPORTED_MODULE_8__.imageReference,\n    inlineCode: _inline_code_js__WEBPACK_IMPORTED_MODULE_9__.inlineCode,\n    link: _link_js__WEBPACK_IMPORTED_MODULE_10__.link,\n    linkReference: _link_reference_js__WEBPACK_IMPORTED_MODULE_11__.linkReference,\n    list: _list_js__WEBPACK_IMPORTED_MODULE_12__.list,\n    listItem: _list_item_js__WEBPACK_IMPORTED_MODULE_13__.listItem,\n    paragraph: _paragraph_js__WEBPACK_IMPORTED_MODULE_14__.paragraph,\n    root: _root_js__WEBPACK_IMPORTED_MODULE_15__.root,\n    strong: _strong_js__WEBPACK_IMPORTED_MODULE_16__.strong,\n    text: _text_js__WEBPACK_IMPORTED_MODULE_17__.text,\n    thematicBreak: _thematic_break_js__WEBPACK_IMPORTED_MODULE_18__.thematicBreak\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ047QUFDTjtBQUNZO0FBQ0o7QUFDRjtBQUNOO0FBQ0U7QUFDbUI7QUFDUjtBQUNiO0FBQ21CO0FBQ25CO0FBQ1M7QUFDQztBQUNWO0FBQ0k7QUFDSjtBQUNtQjtBQUVqRDs7Q0FFQyxHQUNNLE1BQU1tQixTQUFTO0lBQ3BCbkIsVUFBVUEsd0RBQUFBO0lBQ1ZvQixPQUFPbkIsZ0RBQVNBO0lBQ2hCQyxJQUFJQSw0Q0FBQUE7SUFDSkMsVUFBVUEsd0RBQUFBO0lBQ1ZDLFFBQVFBLG9EQUFBQTtJQUNSSCxTQUFTQSxrREFBQUE7SUFDVEksT0FBT0Esa0RBQUFBO0lBQ1BDLElBQUlBLDRDQUFBQTtJQUNKQyxLQUFLQSw4Q0FBQUE7SUFDTEMsY0FBY0EsaUVBQUFBO0lBQ2RDLFVBQVVBLHlEQUFBQTtJQUNWQyxJQUFJQSw2Q0FBQUE7SUFDSkMsYUFBYUEsZ0VBQUFBO0lBQ2JDLElBQUlBLDZDQUFBQTtJQUNKQyxRQUFRQSxzREFBQUE7SUFDUkMsU0FBU0EsdURBQUFBO0lBQ1RDLElBQUlBLDZDQUFBQTtJQUNKQyxNQUFNQSxpREFBQUE7SUFDTkMsSUFBSUEsNkNBQUFBO0lBQ0pDLGFBQWFBLGdFQUFBQTtBQUNmLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS9pbmRleC5qcz82Y2YzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YmxvY2txdW90ZX0gZnJvbSAnLi9ibG9ja3F1b3RlLmpzJ1xuaW1wb3J0IHtoYXJkQnJlYWt9IGZyb20gJy4vYnJlYWsuanMnXG5pbXBvcnQge2NvZGV9IGZyb20gJy4vY29kZS5qcydcbmltcG9ydCB7ZGVmaW5pdGlvbn0gZnJvbSAnLi9kZWZpbml0aW9uLmpzJ1xuaW1wb3J0IHtlbXBoYXNpc30gZnJvbSAnLi9lbXBoYXNpcy5qcydcbmltcG9ydCB7aGVhZGluZ30gZnJvbSAnLi9oZWFkaW5nLmpzJ1xuaW1wb3J0IHtodG1sfSBmcm9tICcuL2h0bWwuanMnXG5pbXBvcnQge2ltYWdlfSBmcm9tICcuL2ltYWdlLmpzJ1xuaW1wb3J0IHtpbWFnZVJlZmVyZW5jZX0gZnJvbSAnLi9pbWFnZS1yZWZlcmVuY2UuanMnXG5pbXBvcnQge2lubGluZUNvZGV9IGZyb20gJy4vaW5saW5lLWNvZGUuanMnXG5pbXBvcnQge2xpbmt9IGZyb20gJy4vbGluay5qcydcbmltcG9ydCB7bGlua1JlZmVyZW5jZX0gZnJvbSAnLi9saW5rLXJlZmVyZW5jZS5qcydcbmltcG9ydCB7bGlzdH0gZnJvbSAnLi9saXN0LmpzJ1xuaW1wb3J0IHtsaXN0SXRlbX0gZnJvbSAnLi9saXN0LWl0ZW0uanMnXG5pbXBvcnQge3BhcmFncmFwaH0gZnJvbSAnLi9wYXJhZ3JhcGguanMnXG5pbXBvcnQge3Jvb3R9IGZyb20gJy4vcm9vdC5qcydcbmltcG9ydCB7c3Ryb25nfSBmcm9tICcuL3N0cm9uZy5qcydcbmltcG9ydCB7dGV4dH0gZnJvbSAnLi90ZXh0LmpzJ1xuaW1wb3J0IHt0aGVtYXRpY0JyZWFrfSBmcm9tICcuL3RoZW1hdGljLWJyZWFrLmpzJ1xuXG4vKipcbiAqIERlZmF1bHQgKENvbW1vbk1hcmspIGhhbmRsZXJzLlxuICovXG5leHBvcnQgY29uc3QgaGFuZGxlID0ge1xuICBibG9ja3F1b3RlLFxuICBicmVhazogaGFyZEJyZWFrLFxuICBjb2RlLFxuICBkZWZpbml0aW9uLFxuICBlbXBoYXNpcyxcbiAgaGFyZEJyZWFrLFxuICBoZWFkaW5nLFxuICBodG1sLFxuICBpbWFnZSxcbiAgaW1hZ2VSZWZlcmVuY2UsXG4gIGlubGluZUNvZGUsXG4gIGxpbmssXG4gIGxpbmtSZWZlcmVuY2UsXG4gIGxpc3QsXG4gIGxpc3RJdGVtLFxuICBwYXJhZ3JhcGgsXG4gIHJvb3QsXG4gIHN0cm9uZyxcbiAgdGV4dCxcbiAgdGhlbWF0aWNCcmVha1xufVxuIl0sIm5hbWVzIjpbImJsb2NrcXVvdGUiLCJoYXJkQnJlYWsiLCJjb2RlIiwiZGVmaW5pdGlvbiIsImVtcGhhc2lzIiwiaGVhZGluZyIsImh0bWwiLCJpbWFnZSIsImltYWdlUmVmZXJlbmNlIiwiaW5saW5lQ29kZSIsImxpbmsiLCJsaW5rUmVmZXJlbmNlIiwibGlzdCIsImxpc3RJdGVtIiwicGFyYWdyYXBoIiwicm9vdCIsInN0cm9uZyIsInRleHQiLCJ0aGVtYXRpY0JyZWFrIiwiaGFuZGxlIiwiYnJlYWsiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js":
/*!***********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inlineCode: () => (/* binding */ inlineCode)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */ inlineCode.peek = inlineCodePeek;\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */ function inlineCode(node, _, state) {\n    let value = node.value || \"\";\n    let sequence = \"`\";\n    let index = -1;\n    // If there is a single grave accent on its own in the code, use a fence of\n    // two.\n    // If there are two in a row, use one.\n    while(new RegExp(\"(^|[^`])\" + sequence + \"([^`]|$)\").test(value)){\n        sequence += \"`\";\n    }\n    // If this is not just spaces or eols (tabs don’t count), and either the\n    // first or last character are a space, eol, or tick, then pad with spaces.\n    if (/[^ \\r\\n]/.test(value) && (/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value) || /^`|`$/.test(value))) {\n        value = \" \" + value + \" \";\n    }\n    // We have a potential problem: certain characters after eols could result in\n    // blocks being seen.\n    // For example, if someone injected the string `'\\n# b'`, then that would\n    // result in an ATX heading.\n    // We can’t escape characters in `inlineCode`, but because eols are\n    // transformed to spaces when going from markdown to HTML anyway, we can swap\n    // them out.\n    while(++index < state.unsafe.length){\n        const pattern = state.unsafe[index];\n        const expression = state.compilePattern(pattern);\n        /** @type {RegExpExecArray | null} */ let match;\n        // Only look for `atBreak`s.\n        // Btw: note that `atBreak` patterns will always start the regex at LF or\n        // CR.\n        if (!pattern.atBreak) continue;\n        while(match = expression.exec(value)){\n            let position = match.index;\n            // Support CRLF (patterns only look for one of the characters).\n            if (value.charCodeAt(position) === 10 /* `\\n` */  && value.charCodeAt(position - 1) === 13 /* `\\r` */ ) {\n                position--;\n            }\n            value = value.slice(0, position) + \" \" + value.slice(match.index + 1);\n        }\n    }\n    return sequence + value + sequence;\n}\n/**\n * @returns {string}\n */ function inlineCodePeek() {\n    return \"`\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   linkReference: () => (/* binding */ linkReference)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */ linkReference.peek = linkReferencePeek;\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function linkReference(node, _, state, info) {\n    const type = node.referenceType;\n    const exit = state.enter(\"linkReference\");\n    let subexit = state.enter(\"label\");\n    const tracker = state.createTracker(info);\n    let value = tracker.move(\"[\");\n    const text = state.containerPhrasing(node, {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    value += tracker.move(text + \"][\");\n    subexit();\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack;\n    state.stack = [];\n    subexit = state.enter(\"reference\");\n    // Note: for proper tracking, we should reset the output positions when we end\n    // up making a `shortcut` reference, because then there is no brace output.\n    // Practically, in that case, there is no content, so it doesn’t matter that\n    // we’ve tracked one too many characters.\n    const reference = state.safe(state.associationId(node), {\n        before: value,\n        after: \"]\",\n        ...tracker.current()\n    });\n    subexit();\n    state.stack = stack;\n    exit();\n    if (type === \"full\" || !text || text !== reference) {\n        value += tracker.move(reference + \"]\");\n    } else if (type === \"shortcut\") {\n        // Remove the unwanted `[`.\n        value = value.slice(0, -1);\n    } else {\n        value += tracker.move(\"]\");\n    }\n    return value;\n}\n/**\n * @returns {string}\n */ function linkReferencePeek() {\n    return \"[\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/link.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   link: () => (/* binding */ link)\n/* harmony export */ });\n/* harmony import */ var _util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-quote.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\");\n/* harmony import */ var _util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/format-link-as-autolink.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */ \n\nlink.peek = linkPeek;\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function link(node, _, state, info) {\n    const quote = (0,_util_check_quote_js__WEBPACK_IMPORTED_MODULE_0__.checkQuote)(state);\n    const suffix = quote === '\"' ? \"Quote\" : \"Apostrophe\";\n    const tracker = state.createTracker(info);\n    /** @type {Exit} */ let exit;\n    /** @type {Exit} */ let subexit;\n    if ((0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state)) {\n        // Hide the fact that we’re in phrasing, because escapes don’t work.\n        const stack = state.stack;\n        state.stack = [];\n        exit = state.enter(\"autolink\");\n        let value = tracker.move(\"<\");\n        value += tracker.move(state.containerPhrasing(node, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n        exit();\n        state.stack = stack;\n        return value;\n    }\n    exit = state.enter(\"link\");\n    subexit = state.enter(\"label\");\n    let value = tracker.move(\"[\");\n    value += tracker.move(state.containerPhrasing(node, {\n        before: value,\n        after: \"](\",\n        ...tracker.current()\n    }));\n    value += tracker.move(\"](\");\n    subexit();\n    if (// If there’s no url but there is a title…\n    !node.url && node.title || // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)) {\n        subexit = state.enter(\"destinationLiteral\");\n        value += tracker.move(\"<\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: \">\",\n            ...tracker.current()\n        }));\n        value += tracker.move(\">\");\n    } else {\n        // No whitespace, raw is prettier.\n        subexit = state.enter(\"destinationRaw\");\n        value += tracker.move(state.safe(node.url, {\n            before: value,\n            after: node.title ? \" \" : \")\",\n            ...tracker.current()\n        }));\n    }\n    subexit();\n    if (node.title) {\n        subexit = state.enter(`title${suffix}`);\n        value += tracker.move(\" \" + quote);\n        value += tracker.move(state.safe(node.title, {\n            before: value,\n            after: quote,\n            ...tracker.current()\n        }));\n        value += tracker.move(quote);\n        subexit();\n    }\n    value += tracker.move(\")\");\n    exit();\n    return value;\n}\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */ function linkPeek(node, _, state) {\n    return (0,_util_format_link_as_autolink_js__WEBPACK_IMPORTED_MODULE_1__.formatLinkAsAutolink)(node, state) ? \"<\" : \"[\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list-item.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listItem: () => (/* binding */ listItem)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-list-item-indent.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\");\n/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */ \n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function listItem(node, parent, state, info) {\n    const listItemIndent = (0,_util_check_list_item_indent_js__WEBPACK_IMPORTED_MODULE_0__.checkListItemIndent)(state);\n    let bullet = state.bulletCurrent || (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state);\n    // Add the marker value for ordered lists.\n    if (parent && parent.type === \"list\" && parent.ordered) {\n        bullet = (typeof parent.start === \"number\" && parent.start > -1 ? parent.start : 1) + (state.options.incrementListMarker === false ? 0 : parent.children.indexOf(node)) + bullet;\n    }\n    let size = bullet.length + 1;\n    if (listItemIndent === \"tab\" || listItemIndent === \"mixed\" && (parent && parent.type === \"list\" && parent.spread || node.spread)) {\n        size = Math.ceil(size / 4) * 4;\n    }\n    const tracker = state.createTracker(info);\n    tracker.move(bullet + \" \".repeat(size - bullet.length));\n    tracker.shift(size);\n    const exit = state.enter(\"listItem\");\n    const value = state.indentLines(state.containerFlow(node, tracker.current()), map);\n    exit();\n    return value;\n    /** @type {Map} */ function map(line, index, blank) {\n        if (index) {\n            return (blank ? \"\" : \" \".repeat(size)) + line;\n        }\n        return (blank ? bullet : bullet + \" \".repeat(size - bullet.length)) + line;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/list.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/* harmony import */ var _util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/check-bullet-other.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\");\n/* harmony import */ var _util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-bullet-ordered.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */ \n\n\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function list(node, parent, state, info) {\n    const exit = state.enter(\"list\");\n    const bulletCurrent = state.bulletCurrent;\n    /** @type {string} */ let bullet = node.ordered ? (0,_util_check_bullet_ordered_js__WEBPACK_IMPORTED_MODULE_0__.checkBulletOrdered)(state) : (0,_util_check_bullet_js__WEBPACK_IMPORTED_MODULE_1__.checkBullet)(state);\n    /** @type {string} */ const bulletOther = node.ordered ? bullet === \".\" ? \")\" : \".\" : (0,_util_check_bullet_other_js__WEBPACK_IMPORTED_MODULE_2__.checkBulletOther)(state);\n    let useDifferentMarker = parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false;\n    if (!node.ordered) {\n        const firstListItem = node.children ? node.children[0] : undefined;\n        // If there’s an empty first list item directly in two list items,\n        // we have to use a different bullet:\n        //\n        // ```markdown\n        // * - *\n        // ```\n        //\n        // …because otherwise it would become one big thematic break.\n        if (// Bullet could be used as a thematic break marker:\n        (bullet === \"*\" || bullet === \"-\") && // Empty first list item:\n        firstListItem && (!firstListItem.children || !firstListItem.children[0]) && // Directly in two other list items:\n        state.stack[state.stack.length - 1] === \"list\" && state.stack[state.stack.length - 2] === \"listItem\" && state.stack[state.stack.length - 3] === \"list\" && state.stack[state.stack.length - 4] === \"listItem\" && // That are each the first child.\n        state.indexStack[state.indexStack.length - 1] === 0 && state.indexStack[state.indexStack.length - 2] === 0 && state.indexStack[state.indexStack.length - 3] === 0) {\n            useDifferentMarker = true;\n        }\n        // If there’s a thematic break at the start of the first list item,\n        // we have to use a different bullet:\n        //\n        // ```markdown\n        // * ---\n        // ```\n        //\n        // …because otherwise it would become one big thematic break.\n        if ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_3__.checkRule)(state) === bullet && firstListItem) {\n            let index = -1;\n            while(++index < node.children.length){\n                const item = node.children[index];\n                if (item && item.type === \"listItem\" && item.children && item.children[0] && item.children[0].type === \"thematicBreak\") {\n                    useDifferentMarker = true;\n                    break;\n                }\n            }\n        }\n    }\n    if (useDifferentMarker) {\n        bullet = bulletOther;\n    }\n    state.bulletCurrent = bullet;\n    const value = state.containerFlow(node, info);\n    state.bulletLastUsed = bullet;\n    state.bulletCurrent = bulletCurrent;\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paragraph: () => (/* binding */ paragraph)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */ /**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function paragraph(node, _, state, info) {\n    const exit = state.enter(\"paragraph\");\n    const subexit = state.enter(\"phrasing\");\n    const value = state.containerPhrasing(node, info);\n    subexit();\n    exit();\n    return value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3BhcmFncmFwaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBRUQ7Ozs7OztDQU1DLEdBQ00sU0FBU0EsVUFBVUMsSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUM1QyxNQUFNQyxPQUFPRixNQUFNRyxLQUFLLENBQUM7SUFDekIsTUFBTUMsVUFBVUosTUFBTUcsS0FBSyxDQUFDO0lBQzVCLE1BQU1FLFFBQVFMLE1BQU1NLGlCQUFpQixDQUFDUixNQUFNRztJQUM1Q0c7SUFDQUY7SUFDQSxPQUFPRztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvcGFyYWdyYXBoLmpzPzA5NjciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmFncmFwaCwgUGFyZW50c30gZnJvbSAnbWRhc3QnXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1BhcmFncmFwaH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyYWdyYXBoKG5vZGUsIF8sIHN0YXRlLCBpbmZvKSB7XG4gIGNvbnN0IGV4aXQgPSBzdGF0ZS5lbnRlcigncGFyYWdyYXBoJylcbiAgY29uc3Qgc3ViZXhpdCA9IHN0YXRlLmVudGVyKCdwaHJhc2luZycpXG4gIGNvbnN0IHZhbHVlID0gc3RhdGUuY29udGFpbmVyUGhyYXNpbmcobm9kZSwgaW5mbylcbiAgc3ViZXhpdCgpXG4gIGV4aXQoKVxuICByZXR1cm4gdmFsdWVcbn1cbiJdLCJuYW1lcyI6WyJwYXJhZ3JhcGgiLCJub2RlIiwiXyIsInN0YXRlIiwiaW5mbyIsImV4aXQiLCJlbnRlciIsInN1YmV4aXQiLCJ2YWx1ZSIsImNvbnRhaW5lclBocmFzaW5nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/root.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-phrasing */ \"(ssr)/./node_modules/mdast-util-phrasing/lib/index.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */ \n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function root(node, _, state, info) {\n    // Note: `html` nodes are ambiguous.\n    const hasPhrasing = node.children.some(function(d) {\n        return (0,mdast_util_phrasing__WEBPACK_IMPORTED_MODULE_0__.phrasing)(d);\n    });\n    const container = hasPhrasing ? state.containerPhrasing : state.containerFlow;\n    return container.call(state, node, info);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3Jvb3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7O0NBR0MsR0FFMkM7QUFFNUM7Ozs7OztDQU1DLEdBQ00sU0FBU0MsS0FBS0MsSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUN2QyxvQ0FBb0M7SUFDcEMsTUFBTUMsY0FBY0osS0FBS0ssUUFBUSxDQUFDQyxJQUFJLENBQUMsU0FBVUMsQ0FBQztRQUNoRCxPQUFPVCw2REFBUUEsQ0FBQ1M7SUFDbEI7SUFFQSxNQUFNQyxZQUFZSixjQUFjRixNQUFNTyxpQkFBaUIsR0FBR1AsTUFBTVEsYUFBYTtJQUM3RSxPQUFPRixVQUFVRyxJQUFJLENBQUNULE9BQU9GLE1BQU1HO0FBQ3JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvcm9vdC5qcz9hNmI4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBSb290fSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge3BocmFzaW5nfSBmcm9tICdtZGFzdC11dGlsLXBocmFzaW5nJ1xuXG4vKipcbiAqIEBwYXJhbSB7Um9vdH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gcm9vdChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICAvLyBOb3RlOiBgaHRtbGAgbm9kZXMgYXJlIGFtYmlndW91cy5cbiAgY29uc3QgaGFzUGhyYXNpbmcgPSBub2RlLmNoaWxkcmVuLnNvbWUoZnVuY3Rpb24gKGQpIHtcbiAgICByZXR1cm4gcGhyYXNpbmcoZClcbiAgfSlcblxuICBjb25zdCBjb250YWluZXIgPSBoYXNQaHJhc2luZyA/IHN0YXRlLmNvbnRhaW5lclBocmFzaW5nIDogc3RhdGUuY29udGFpbmVyRmxvd1xuICByZXR1cm4gY29udGFpbmVyLmNhbGwoc3RhdGUsIG5vZGUsIGluZm8pXG59XG4iXSwibmFtZXMiOlsicGhyYXNpbmciLCJyb290Iiwibm9kZSIsIl8iLCJzdGF0ZSIsImluZm8iLCJoYXNQaHJhc2luZyIsImNoaWxkcmVuIiwic29tZSIsImQiLCJjb250YWluZXIiLCJjb250YWluZXJQaHJhc2luZyIsImNvbnRhaW5lckZsb3ciLCJjYWxsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/root.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js":
/*!******************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/strong.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   strong: () => (/* binding */ strong)\n/* harmony export */ });\n/* harmony import */ var _util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-strong.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\");\n/* harmony import */ var _util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/encode-character-reference.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\");\n/* harmony import */ var _util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/encode-info.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\");\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */ \n\n\nstrong.peek = strongPeek;\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function strong(node, _, state, info) {\n    const marker = (0,_util_check_strong_js__WEBPACK_IMPORTED_MODULE_0__.checkStrong)(state);\n    const exit = state.enter(\"strong\");\n    const tracker = state.createTracker(info);\n    const before = tracker.move(marker + marker);\n    let between = tracker.move(state.containerPhrasing(node, {\n        after: marker,\n        before,\n        ...tracker.current()\n    }));\n    const betweenHead = between.charCodeAt(0);\n    const open = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.before.charCodeAt(info.before.length - 1), betweenHead, marker);\n    if (open.inside) {\n        between = (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenHead) + between.slice(1);\n    }\n    const betweenTail = between.charCodeAt(between.length - 1);\n    const close = (0,_util_encode_info_js__WEBPACK_IMPORTED_MODULE_1__.encodeInfo)(info.after.charCodeAt(0), betweenTail, marker);\n    if (close.inside) {\n        between = between.slice(0, -1) + (0,_util_encode_character_reference_js__WEBPACK_IMPORTED_MODULE_2__.encodeCharacterReference)(betweenTail);\n    }\n    const after = tracker.move(marker + marker);\n    exit();\n    state.attentionEncodeSurroundingInfo = {\n        after: close.outside,\n        before: open.outside\n    };\n    return before + between + after;\n}\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function strongPeek(_, _1, state) {\n    return state.options.strong || \"*\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3N0cm9uZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7OztDQUdDLEdBRWtEO0FBQzJCO0FBQzdCO0FBRWpERyxPQUFPQyxJQUFJLEdBQUdDO0FBRWQ7Ozs7OztDQU1DLEdBQ00sU0FBU0YsT0FBT0csSUFBSSxFQUFFQyxDQUFDLEVBQUVDLEtBQUssRUFBRUMsSUFBSTtJQUN6QyxNQUFNQyxTQUFTVixrRUFBV0EsQ0FBQ1E7SUFDM0IsTUFBTUcsT0FBT0gsTUFBTUksS0FBSyxDQUFDO0lBQ3pCLE1BQU1DLFVBQVVMLE1BQU1NLGFBQWEsQ0FBQ0w7SUFDcEMsTUFBTU0sU0FBU0YsUUFBUUcsSUFBSSxDQUFDTixTQUFTQTtJQUVyQyxJQUFJTyxVQUFVSixRQUFRRyxJQUFJLENBQ3hCUixNQUFNVSxpQkFBaUIsQ0FBQ1osTUFBTTtRQUM1QmEsT0FBT1Q7UUFDUEs7UUFDQSxHQUFHRixRQUFRTyxPQUFPLEVBQUU7SUFDdEI7SUFFRixNQUFNQyxjQUFjSixRQUFRSyxVQUFVLENBQUM7SUFDdkMsTUFBTUMsT0FBT3JCLGdFQUFVQSxDQUNyQk8sS0FBS00sTUFBTSxDQUFDTyxVQUFVLENBQUNiLEtBQUtNLE1BQU0sQ0FBQ1MsTUFBTSxHQUFHLElBQzVDSCxhQUNBWDtJQUdGLElBQUlhLEtBQUtFLE1BQU0sRUFBRTtRQUNmUixVQUFVaEIsNkZBQXdCQSxDQUFDb0IsZUFBZUosUUFBUVMsS0FBSyxDQUFDO0lBQ2xFO0lBRUEsTUFBTUMsY0FBY1YsUUFBUUssVUFBVSxDQUFDTCxRQUFRTyxNQUFNLEdBQUc7SUFDeEQsTUFBTUksUUFBUTFCLGdFQUFVQSxDQUFDTyxLQUFLVSxLQUFLLENBQUNHLFVBQVUsQ0FBQyxJQUFJSyxhQUFhakI7SUFFaEUsSUFBSWtCLE1BQU1ILE1BQU0sRUFBRTtRQUNoQlIsVUFBVUEsUUFBUVMsS0FBSyxDQUFDLEdBQUcsQ0FBQyxLQUFLekIsNkZBQXdCQSxDQUFDMEI7SUFDNUQ7SUFFQSxNQUFNUixRQUFRTixRQUFRRyxJQUFJLENBQUNOLFNBQVNBO0lBRXBDQztJQUVBSCxNQUFNcUIsOEJBQThCLEdBQUc7UUFDckNWLE9BQU9TLE1BQU1FLE9BQU87UUFDcEJmLFFBQVFRLEtBQUtPLE9BQU87SUFDdEI7SUFDQSxPQUFPZixTQUFTRSxVQUFVRTtBQUM1QjtBQUVBOzs7OztDQUtDLEdBQ0QsU0FBU2QsV0FBV0UsQ0FBQyxFQUFFd0IsRUFBRSxFQUFFdkIsS0FBSztJQUM5QixPQUFPQSxNQUFNd0IsT0FBTyxDQUFDN0IsTUFBTSxJQUFJO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvc3Ryb25nLmpzP2M5NWYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge1BhcmVudHMsIFN0cm9uZ30gZnJvbSAnbWRhc3QnXG4gKi9cblxuaW1wb3J0IHtjaGVja1N0cm9uZ30gZnJvbSAnLi4vdXRpbC9jaGVjay1zdHJvbmcuanMnXG5pbXBvcnQge2VuY29kZUNoYXJhY3RlclJlZmVyZW5jZX0gZnJvbSAnLi4vdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcydcbmltcG9ydCB7ZW5jb2RlSW5mb30gZnJvbSAnLi4vdXRpbC9lbmNvZGUtaW5mby5qcydcblxuc3Ryb25nLnBlZWsgPSBzdHJvbmdQZWVrXG5cbi8qKlxuICogQHBhcmFtIHtTdHJvbmd9IG5vZGVcbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gX1xuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEBwYXJhbSB7SW5mb30gaW5mb1xuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHN0cm9uZyhub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICBjb25zdCBtYXJrZXIgPSBjaGVja1N0cm9uZyhzdGF0ZSlcbiAgY29uc3QgZXhpdCA9IHN0YXRlLmVudGVyKCdzdHJvbmcnKVxuICBjb25zdCB0cmFja2VyID0gc3RhdGUuY3JlYXRlVHJhY2tlcihpbmZvKVxuICBjb25zdCBiZWZvcmUgPSB0cmFja2VyLm1vdmUobWFya2VyICsgbWFya2VyKVxuXG4gIGxldCBiZXR3ZWVuID0gdHJhY2tlci5tb3ZlKFxuICAgIHN0YXRlLmNvbnRhaW5lclBocmFzaW5nKG5vZGUsIHtcbiAgICAgIGFmdGVyOiBtYXJrZXIsXG4gICAgICBiZWZvcmUsXG4gICAgICAuLi50cmFja2VyLmN1cnJlbnQoKVxuICAgIH0pXG4gIClcbiAgY29uc3QgYmV0d2VlbkhlYWQgPSBiZXR3ZWVuLmNoYXJDb2RlQXQoMClcbiAgY29uc3Qgb3BlbiA9IGVuY29kZUluZm8oXG4gICAgaW5mby5iZWZvcmUuY2hhckNvZGVBdChpbmZvLmJlZm9yZS5sZW5ndGggLSAxKSxcbiAgICBiZXR3ZWVuSGVhZCxcbiAgICBtYXJrZXJcbiAgKVxuXG4gIGlmIChvcGVuLmluc2lkZSkge1xuICAgIGJldHdlZW4gPSBlbmNvZGVDaGFyYWN0ZXJSZWZlcmVuY2UoYmV0d2VlbkhlYWQpICsgYmV0d2Vlbi5zbGljZSgxKVxuICB9XG5cbiAgY29uc3QgYmV0d2VlblRhaWwgPSBiZXR3ZWVuLmNoYXJDb2RlQXQoYmV0d2Vlbi5sZW5ndGggLSAxKVxuICBjb25zdCBjbG9zZSA9IGVuY29kZUluZm8oaW5mby5hZnRlci5jaGFyQ29kZUF0KDApLCBiZXR3ZWVuVGFpbCwgbWFya2VyKVxuXG4gIGlmIChjbG9zZS5pbnNpZGUpIHtcbiAgICBiZXR3ZWVuID0gYmV0d2Vlbi5zbGljZSgwLCAtMSkgKyBlbmNvZGVDaGFyYWN0ZXJSZWZlcmVuY2UoYmV0d2VlblRhaWwpXG4gIH1cblxuICBjb25zdCBhZnRlciA9IHRyYWNrZXIubW92ZShtYXJrZXIgKyBtYXJrZXIpXG5cbiAgZXhpdCgpXG5cbiAgc3RhdGUuYXR0ZW50aW9uRW5jb2RlU3Vycm91bmRpbmdJbmZvID0ge1xuICAgIGFmdGVyOiBjbG9zZS5vdXRzaWRlLFxuICAgIGJlZm9yZTogb3Blbi5vdXRzaWRlXG4gIH1cbiAgcmV0dXJuIGJlZm9yZSArIGJldHdlZW4gKyBhZnRlclxufVxuXG4vKipcbiAqIEBwYXJhbSB7U3Ryb25nfSBfXG4gKiBAcGFyYW0ge1BhcmVudHMgfCB1bmRlZmluZWR9IF8xXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge3N0cmluZ31cbiAqL1xuZnVuY3Rpb24gc3Ryb25nUGVlayhfLCBfMSwgc3RhdGUpIHtcbiAgcmV0dXJuIHN0YXRlLm9wdGlvbnMuc3Ryb25nIHx8ICcqJ1xufVxuIl0sIm5hbWVzIjpbImNoZWNrU3Ryb25nIiwiZW5jb2RlQ2hhcmFjdGVyUmVmZXJlbmNlIiwiZW5jb2RlSW5mbyIsInN0cm9uZyIsInBlZWsiLCJzdHJvbmdQZWVrIiwibm9kZSIsIl8iLCJzdGF0ZSIsImluZm8iLCJtYXJrZXIiLCJleGl0IiwiZW50ZXIiLCJ0cmFja2VyIiwiY3JlYXRlVHJhY2tlciIsImJlZm9yZSIsIm1vdmUiLCJiZXR3ZWVuIiwiY29udGFpbmVyUGhyYXNpbmciLCJhZnRlciIsImN1cnJlbnQiLCJiZXR3ZWVuSGVhZCIsImNoYXJDb2RlQXQiLCJvcGVuIiwibGVuZ3RoIiwiaW5zaWRlIiwic2xpY2UiLCJiZXR3ZWVuVGFpbCIsImNsb3NlIiwiYXR0ZW50aW9uRW5jb2RlU3Vycm91bmRpbmdJbmZvIiwib3V0c2lkZSIsIl8xIiwib3B0aW9ucyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/text.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */ /**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */ function text(node, _, state, info) {\n    return state.safe(node.value, info);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RleHQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7Q0FHQyxHQUVEOzs7Ozs7Q0FNQyxHQUNNLFNBQVNBLEtBQUtDLElBQUksRUFBRUMsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLElBQUk7SUFDdkMsT0FBT0QsTUFBTUUsSUFBSSxDQUFDSixLQUFLSyxLQUFLLEVBQUVGO0FBQ2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi9oYW5kbGUvdGV4dC5qcz84Y2NmIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBUZXh0fSBmcm9tICdtZGFzdCdcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7VGV4dH0gbm9kZVxuICogQHBhcmFtIHtQYXJlbnRzIHwgdW5kZWZpbmVkfSBfXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHBhcmFtIHtJbmZvfSBpbmZvXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGV4dChub2RlLCBfLCBzdGF0ZSwgaW5mbykge1xuICByZXR1cm4gc3RhdGUuc2FmZShub2RlLnZhbHVlLCBpbmZvKVxufVxuIl0sIm5hbWVzIjpbInRleHQiLCJub2RlIiwiXyIsInN0YXRlIiwiaW5mbyIsInNhZmUiLCJ2YWx1ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var _util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/check-rule-repetition.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\");\n/* harmony import */ var _util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/check-rule.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */ \n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */ function thematicBreak(_, _1, state) {\n    const value = ((0,_util_check_rule_js__WEBPACK_IMPORTED_MODULE_0__.checkRule)(state) + (state.options.ruleSpaces ? \" \" : \"\")).repeat((0,_util_check_rule_repetition_js__WEBPACK_IMPORTED_MODULE_1__.checkRuleRepetition)(state));\n    return state.options.ruleSpaces ? value.slice(0, -1) : value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvaGFuZGxlL3RoZW1hdGljLWJyZWFrLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7Q0FHQyxHQUVtRTtBQUNyQjtBQUUvQzs7Ozs7Q0FLQyxHQUNNLFNBQVNFLGNBQWNDLENBQUMsRUFBRUMsRUFBRSxFQUFFQyxLQUFLO0lBQ3hDLE1BQU1DLFFBQVEsQ0FDWkwsOERBQVNBLENBQUNJLFNBQVVBLENBQUFBLE1BQU1FLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLE1BQU0sRUFBQyxDQUFDLEVBQ3ZEQyxNQUFNLENBQUNULG1GQUFtQkEsQ0FBQ0s7SUFFN0IsT0FBT0EsTUFBTUUsT0FBTyxDQUFDQyxVQUFVLEdBQUdGLE1BQU1JLEtBQUssQ0FBQyxHQUFHLENBQUMsS0FBS0o7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL2hhbmRsZS90aGVtYXRpYy1icmVhay5qcz9jMTQwIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKiBAaW1wb3J0IHtQYXJlbnRzLCBUaGVtYXRpY0JyZWFrfSBmcm9tICdtZGFzdCdcbiAqL1xuXG5pbXBvcnQge2NoZWNrUnVsZVJlcGV0aXRpb259IGZyb20gJy4uL3V0aWwvY2hlY2stcnVsZS1yZXBldGl0aW9uLmpzJ1xuaW1wb3J0IHtjaGVja1J1bGV9IGZyb20gJy4uL3V0aWwvY2hlY2stcnVsZS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1RoZW1hdGljQnJlYWt9IF9cbiAqIEBwYXJhbSB7UGFyZW50cyB8IHVuZGVmaW5lZH0gXzFcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICovXG5leHBvcnQgZnVuY3Rpb24gdGhlbWF0aWNCcmVhayhfLCBfMSwgc3RhdGUpIHtcbiAgY29uc3QgdmFsdWUgPSAoXG4gICAgY2hlY2tSdWxlKHN0YXRlKSArIChzdGF0ZS5vcHRpb25zLnJ1bGVTcGFjZXMgPyAnICcgOiAnJylcbiAgKS5yZXBlYXQoY2hlY2tSdWxlUmVwZXRpdGlvbihzdGF0ZSkpXG5cbiAgcmV0dXJuIHN0YXRlLm9wdGlvbnMucnVsZVNwYWNlcyA/IHZhbHVlLnNsaWNlKDAsIC0xKSA6IHZhbHVlXG59XG4iXSwibmFtZXMiOlsiY2hlY2tSdWxlUmVwZXRpdGlvbiIsImNoZWNrUnVsZSIsInRoZW1hdGljQnJlYWsiLCJfIiwiXzEiLCJzdGF0ZSIsInZhbHVlIiwib3B0aW9ucyIsInJ1bGVTcGFjZXMiLCJyZXBlYXQiLCJzbGljZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js":
/*!******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOrdered: () => (/* binding */ checkBulletOrdered)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */ function checkBulletOrdered(state) {\n    const marker = state.options.bulletOrdered || \".\";\n    if (marker !== \".\" && marker !== \")\") {\n        throw new Error(\"Cannot serialize items with `\" + marker + \"` for `options.bulletOrdered`, expected `.` or `)`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQtb3JkZXJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxtQkFBbUJDLEtBQUs7SUFDdEMsTUFBTUMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxhQUFhLElBQUk7SUFFOUMsSUFBSUYsV0FBVyxPQUFPQSxXQUFXLEtBQUs7UUFDcEMsTUFBTSxJQUFJRyxNQUNSLGtDQUNFSCxTQUNBO0lBRU47SUFFQSxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2NoZWNrLWJ1bGxldC1vcmRlcmVkLmpzPzllMzMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydidWxsZXRPcmRlcmVkJ10sIG51bGwgfCB1bmRlZmluZWQ+fVxuICovXG5leHBvcnQgZnVuY3Rpb24gY2hlY2tCdWxsZXRPcmRlcmVkKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0T3JkZXJlZCB8fCAnLidcblxuICBpZiAobWFya2VyICE9PSAnLicgJiYgbWFya2VyICE9PSAnKScpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmJ1bGxldE9yZGVyZWRgLCBleHBlY3RlZCBgLmAgb3IgYClgJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja0J1bGxldE9yZGVyZWQiLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJidWxsZXRPcmRlcmVkIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js":
/*!****************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBulletOther: () => (/* binding */ checkBulletOther)\n/* harmony export */ });\n/* harmony import */ var _check_bullet_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check-bullet.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\");\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ \n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */ function checkBulletOther(state) {\n    const bullet = (0,_check_bullet_js__WEBPACK_IMPORTED_MODULE_0__.checkBullet)(state);\n    const bulletOther = state.options.bulletOther;\n    if (!bulletOther) {\n        return bullet === \"*\" ? \"-\" : \"*\";\n    }\n    if (bulletOther !== \"*\" && bulletOther !== \"+\" && bulletOther !== \"-\") {\n        throw new Error(\"Cannot serialize items with `\" + bulletOther + \"` for `options.bulletOther`, expected `*`, `+`, or `-`\");\n    }\n    if (bulletOther === bullet) {\n        throw new Error(\"Expected `bullet` (`\" + bullet + \"`) and `bulletOther` (`\" + bulletOther + \"`) to be different\");\n    }\n    return bulletOther;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkBullet: () => (/* binding */ checkBullet)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */ function checkBullet(state) {\n    const marker = state.options.bullet || \"*\";\n    if (marker !== \"*\" && marker !== \"+\" && marker !== \"-\") {\n        throw new Error(\"Cannot serialize items with `\" + marker + \"` for `options.bullet`, expected `*`, `+`, or `-`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0EsWUFBWUMsS0FBSztJQUMvQixNQUFNQyxTQUFTRCxNQUFNRSxPQUFPLENBQUNDLE1BQU0sSUFBSTtJQUV2QyxJQUFJRixXQUFXLE9BQU9BLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3RELE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1idWxsZXQuanM/ZmFlNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnMsIFN0YXRlfSBmcm9tICdtZGFzdC11dGlsLXRvLW1hcmtkb3duJ1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqIEByZXR1cm5zIHtFeGNsdWRlPE9wdGlvbnNbJ2J1bGxldCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrQnVsbGV0KHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuYnVsbGV0IHx8ICcqJ1xuXG4gIGlmIChtYXJrZXIgIT09ICcqJyAmJiBtYXJrZXIgIT09ICcrJyAmJiBtYXJrZXIgIT09ICctJykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIGl0ZW1zIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMuYnVsbGV0YCwgZXhwZWN0ZWQgYCpgLCBgK2AsIG9yIGAtYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tCdWxsZXQiLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJidWxsZXQiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js":
/*!************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEmphasis: () => (/* binding */ checkEmphasis)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */ function checkEmphasis(state) {\n    const marker = state.options.emphasis || \"*\";\n    if (marker !== \"*\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize emphasis with `\" + marker + \"` for `options.emphasis`, expected `*`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxjQUFjQyxLQUFLO0lBQ2pDLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsUUFBUSxJQUFJO0lBRXpDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixxQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1lbXBoYXNpcy5qcz84YjAzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snZW1waGFzaXMnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0VtcGhhc2lzKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuZW1waGFzaXMgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgZW1waGFzaXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5lbXBoYXNpc2AsIGV4cGVjdGVkIGAqYCwgb3IgYF9gJ1xuICAgIClcbiAgfVxuXG4gIHJldHVybiBtYXJrZXJcbn1cbiJdLCJuYW1lcyI6WyJjaGVja0VtcGhhc2lzIiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwiZW1waGFzaXMiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-fence.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFence: () => (/* binding */ checkFence)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */ function checkFence(state) {\n    const marker = state.options.fence || \"`\";\n    if (marker !== \"`\" && marker !== \"~\") {\n        throw new Error(\"Cannot serialize code with `\" + marker + \"` for `options.fence`, expected `` ` `` or `~`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxXQUFXQyxLQUFLO0lBQzlCLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJO0lBRXRDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixpQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1mZW5jZS5qcz9iZWFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1snZmVuY2UnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja0ZlbmNlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMuZmVuY2UgfHwgJ2AnXG5cbiAgaWYgKG1hcmtlciAhPT0gJ2AnICYmIG1hcmtlciAhPT0gJ34nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgY29kZSB3aXRoIGAnICtcbiAgICAgICAgbWFya2VyICtcbiAgICAgICAgJ2AgZm9yIGBvcHRpb25zLmZlbmNlYCwgZXhwZWN0ZWQgYGAgYCBgYCBvciBgfmAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIG1hcmtlclxufVxuIl0sIm5hbWVzIjpbImNoZWNrRmVuY2UiLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJmZW5jZSIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js":
/*!********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkListItemIndent: () => (/* binding */ checkListItemIndent)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */ function checkListItemIndent(state) {\n    const style = state.options.listItemIndent || \"one\";\n    if (style !== \"tab\" && style !== \"one\" && style !== \"mixed\") {\n        throw new Error(\"Cannot serialize items with `\" + style + \"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`\");\n    }\n    return style;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBLG9CQUFvQkMsS0FBSztJQUN2QyxNQUFNQyxRQUFRRCxNQUFNRSxPQUFPLENBQUNDLGNBQWMsSUFBSTtJQUU5QyxJQUFJRixVQUFVLFNBQVNBLFVBQVUsU0FBU0EsVUFBVSxTQUFTO1FBQzNELE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsUUFDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1saXN0LWl0ZW0taW5kZW50LmpzPzQzYzciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydsaXN0SXRlbUluZGVudCddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrTGlzdEl0ZW1JbmRlbnQoc3RhdGUpIHtcbiAgY29uc3Qgc3R5bGUgPSBzdGF0ZS5vcHRpb25zLmxpc3RJdGVtSW5kZW50IHx8ICdvbmUnXG5cbiAgaWYgKHN0eWxlICE9PSAndGFiJyAmJiBzdHlsZSAhPT0gJ29uZScgJiYgc3R5bGUgIT09ICdtaXhlZCcpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBpdGVtcyB3aXRoIGAnICtcbiAgICAgICAgc3R5bGUgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMubGlzdEl0ZW1JbmRlbnRgLCBleHBlY3RlZCBgdGFiYCwgYG9uZWAsIG9yIGBtaXhlZGAnXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIHN0eWxlXG59XG4iXSwibmFtZXMiOlsiY2hlY2tMaXN0SXRlbUluZGVudCIsInN0YXRlIiwic3R5bGUiLCJvcHRpb25zIiwibGlzdEl0ZW1JbmRlbnQiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-quote.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkQuote: () => (/* binding */ checkQuote)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */ function checkQuote(state) {\n    const marker = state.options.quote || '\"';\n    if (marker !== '\"' && marker !== \"'\") {\n        throw new Error(\"Cannot serialize title with `\" + marker + \"` for `options.quote`, expected `\\\"`, or `'`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FFRDs7O0NBR0MsR0FDTSxTQUFTQSxXQUFXQyxLQUFLO0lBQzlCLE1BQU1DLFNBQVNELE1BQU1FLE9BQU8sQ0FBQ0MsS0FBSyxJQUFJO0lBRXRDLElBQUlGLFdBQVcsT0FBT0EsV0FBVyxLQUFLO1FBQ3BDLE1BQU0sSUFBSUcsTUFDUixrQ0FDRUgsU0FDQTtJQUVOO0lBRUEsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1xdW90ZS5qcz8zNWI3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncXVvdGUnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1F1b3RlKHN0YXRlKSB7XG4gIGNvbnN0IG1hcmtlciA9IHN0YXRlLm9wdGlvbnMucXVvdGUgfHwgJ1wiJ1xuXG4gIGlmIChtYXJrZXIgIT09ICdcIicgJiYgbWFya2VyICE9PSBcIidcIikge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHRpdGxlIHdpdGggYCcgK1xuICAgICAgICBtYXJrZXIgK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucXVvdGVgLCBleHBlY3RlZCBgXCJgLCBvciBgXFwnYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tRdW90ZSIsInN0YXRlIiwibWFya2VyIiwib3B0aW9ucyIsInF1b3RlIiwiRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRuleRepetition: () => (/* binding */ checkRuleRepetition)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */ function checkRuleRepetition(state) {\n    const repetition = state.options.ruleRepetition || 3;\n    if (repetition < 3) {\n        throw new Error(\"Cannot serialize rules with repetition `\" + repetition + \"` for `options.ruleRepetition`, expected `3` or more\");\n    }\n    return repetition;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLXJlcGV0aXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0Esb0JBQW9CQyxLQUFLO0lBQ3ZDLE1BQU1DLGFBQWFELE1BQU1FLE9BQU8sQ0FBQ0MsY0FBYyxJQUFJO0lBRW5ELElBQUlGLGFBQWEsR0FBRztRQUNsQixNQUFNLElBQUlHLE1BQ1IsNkNBQ0VILGFBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stcnVsZS1yZXBldGl0aW9uLmpzPzNlMjAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydydWxlUmVwZXRpdGlvbiddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUnVsZVJlcGV0aXRpb24oc3RhdGUpIHtcbiAgY29uc3QgcmVwZXRpdGlvbiA9IHN0YXRlLm9wdGlvbnMucnVsZVJlcGV0aXRpb24gfHwgM1xuXG4gIGlmIChyZXBldGl0aW9uIDwgMykge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3Qgc2VyaWFsaXplIHJ1bGVzIHdpdGggcmVwZXRpdGlvbiBgJyArXG4gICAgICAgIHJlcGV0aXRpb24gK1xuICAgICAgICAnYCBmb3IgYG9wdGlvbnMucnVsZVJlcGV0aXRpb25gLCBleHBlY3RlZCBgM2Agb3IgbW9yZSdcbiAgICApXG4gIH1cblxuICByZXR1cm4gcmVwZXRpdGlvblxufVxuIl0sIm5hbWVzIjpbImNoZWNrUnVsZVJlcGV0aXRpb24iLCJzdGF0ZSIsInJlcGV0aXRpb24iLCJvcHRpb25zIiwicnVsZVJlcGV0aXRpb24iLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js":
/*!********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-rule.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkRule: () => (/* binding */ checkRule)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */ function checkRule(state) {\n    const marker = state.options.rule || \"*\";\n    if (marker !== \"*\" && marker !== \"-\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize rules with `\" + marker + \"` for `options.rule`, expected `*`, `-`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1ydWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVEOzs7Q0FHQyxHQUNNLFNBQVNBLFVBQVVDLEtBQUs7SUFDN0IsTUFBTUMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxJQUFJLElBQUk7SUFFckMsSUFBSUYsV0FBVyxPQUFPQSxXQUFXLE9BQU9BLFdBQVcsS0FBSztRQUN0RCxNQUFNLElBQUlHLE1BQ1Isa0NBQ0VILFNBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stcnVsZS5qcz9iNTA2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7T3B0aW9ucywgU3RhdGV9IGZyb20gJ21kYXN0LXV0aWwtdG8tbWFya2Rvd24nXG4gKi9cblxuLyoqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge0V4Y2x1ZGU8T3B0aW9uc1sncnVsZSddLCBudWxsIHwgdW5kZWZpbmVkPn1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNoZWNrUnVsZShzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnJ1bGUgfHwgJyonXG5cbiAgaWYgKG1hcmtlciAhPT0gJyonICYmIG1hcmtlciAhPT0gJy0nICYmIG1hcmtlciAhPT0gJ18nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0Nhbm5vdCBzZXJpYWxpemUgcnVsZXMgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5ydWxlYCwgZXhwZWN0ZWQgYCpgLCBgLWAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tSdWxlIiwic3RhdGUiLCJtYXJrZXIiLCJvcHRpb25zIiwicnVsZSIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js":
/*!**********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/check-strong.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkStrong: () => (/* binding */ checkStrong)\n/* harmony export */ });\n/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */ /**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */ function checkStrong(state) {\n    const marker = state.options.strong || \"*\";\n    if (marker !== \"*\" && marker !== \"_\") {\n        throw new Error(\"Cannot serialize strong with `\" + marker + \"` for `options.strong`, expected `*`, or `_`\");\n    }\n    return marker;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9jaGVjay1zdHJvbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztDQUVDLEdBRUQ7OztDQUdDLEdBQ00sU0FBU0EsWUFBWUMsS0FBSztJQUMvQixNQUFNQyxTQUFTRCxNQUFNRSxPQUFPLENBQUNDLE1BQU0sSUFBSTtJQUV2QyxJQUFJRixXQUFXLE9BQU9BLFdBQVcsS0FBSztRQUNwQyxNQUFNLElBQUlHLE1BQ1IsbUNBQ0VILFNBQ0E7SUFFTjtJQUVBLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvY2hlY2stc3Ryb25nLmpzP2MwYzYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtPcHRpb25zLCBTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqL1xuXG4vKipcbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiBAcmV0dXJucyB7RXhjbHVkZTxPcHRpb25zWydzdHJvbmcnXSwgbnVsbCB8IHVuZGVmaW5lZD59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjaGVja1N0cm9uZyhzdGF0ZSkge1xuICBjb25zdCBtYXJrZXIgPSBzdGF0ZS5vcHRpb25zLnN0cm9uZyB8fCAnKidcblxuICBpZiAobWFya2VyICE9PSAnKicgJiYgbWFya2VyICE9PSAnXycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IHNlcmlhbGl6ZSBzdHJvbmcgd2l0aCBgJyArXG4gICAgICAgIG1hcmtlciArXG4gICAgICAgICdgIGZvciBgb3B0aW9ucy5zdHJvbmdgLCBleHBlY3RlZCBgKmAsIG9yIGBfYCdcbiAgICApXG4gIH1cblxuICByZXR1cm4gbWFya2VyXG59XG4iXSwibmFtZXMiOlsiY2hlY2tTdHJvbmciLCJzdGF0ZSIsIm1hcmtlciIsIm9wdGlvbnMiLCJzdHJvbmciLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js":
/*!************************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeCharacterReference: () => (/* binding */ encodeCharacterReference)\n/* harmony export */ });\n/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */ function encodeCharacterReference(code) {\n    return \"&#x\" + code.toString(16).toUpperCase() + \";\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9lbmNvZGUtY2hhcmFjdGVyLXJlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7Ozs7Q0FPQyxHQUNNLFNBQVNBLHlCQUF5QkMsSUFBSTtJQUMzQyxPQUFPLFFBQVFBLEtBQUtDLFFBQVEsQ0FBQyxJQUFJQyxXQUFXLEtBQUs7QUFDbkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtdG8tbWFya2Rvd24vbGliL3V0aWwvZW5jb2RlLWNoYXJhY3Rlci1yZWZlcmVuY2UuanM/YzUyMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEVuY29kZSBhIGNvZGUgcG9pbnQgYXMgYSBjaGFyYWN0ZXIgcmVmZXJlbmNlLlxuICpcbiAqIEBwYXJhbSB7bnVtYmVyfSBjb2RlXG4gKiAgIENvZGUgcG9pbnQgdG8gZW5jb2RlLlxuICogQHJldHVybnMge3N0cmluZ31cbiAqICAgRW5jb2RlZCBjaGFyYWN0ZXIgcmVmZXJlbmNlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZW5jb2RlQ2hhcmFjdGVyUmVmZXJlbmNlKGNvZGUpIHtcbiAgcmV0dXJuICcmI3gnICsgY29kZS50b1N0cmluZygxNikudG9VcHBlckNhc2UoKSArICc7J1xufVxuIl0sIm5hbWVzIjpbImVuY29kZUNoYXJhY3RlclJlZmVyZW5jZSIsImNvZGUiLCJ0b1N0cmluZyIsInRvVXBwZXJDYXNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js":
/*!*********************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/encode-info.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeInfo: () => (/* binding */ encodeInfo)\n/* harmony export */ });\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/micromark-util-classify-character/dev/index.js\");\n/**\n * @import {EncodeSides} from '../types.js'\n */ \n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */ // Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nfunction encodeInfo(outside, inside, marker) {\n    const outsideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(outside);\n    const insideKind = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_0__.classifyCharacter)(inside);\n    // Letter outside:\n    if (outsideKind === undefined) {\n        return insideKind === undefined ? // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === \"_\" ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: false\n        } : insideKind === 1 ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: true\n        };\n    }\n    // Whitespace outside:\n    if (outsideKind === 1) {\n        return insideKind === undefined ? {\n            inside: false,\n            outside: false\n        } : insideKind === 1 ? {\n            inside: true,\n            outside: true\n        } : {\n            inside: false,\n            outside: false\n        };\n    }\n    // Punctuation outside:\n    return insideKind === undefined ? {\n        inside: false,\n        outside: false\n    } : insideKind === 1 ? {\n        inside: true,\n        outside: false\n    } : {\n        inside: false,\n        outside: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatCodeAsIndented: () => (/* binding */ formatCodeAsIndented)\n/* harmony export */ });\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */ /**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */ function formatCodeAsIndented(node, state) {\n    return Boolean(state.options.fences === false && node.value && // If there’s no info…\n    !node.lang && // And there’s a non-whitespace character…\n    /[^ \\r\\n]/.test(node.value) && // And the value doesn’t start or end in a blank…\n    !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC10by1tYXJrZG93bi9saWIvdXRpbC9mb3JtYXQtY29kZS1hcy1pbmRlbnRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7OztDQUdDLEdBRUQ7Ozs7Q0FJQyxHQUNNLFNBQVNBLHFCQUFxQkMsSUFBSSxFQUFFQyxLQUFLO0lBQzlDLE9BQU9DLFFBQ0xELE1BQU1FLE9BQU8sQ0FBQ0MsTUFBTSxLQUFLLFNBQ3ZCSixLQUFLSyxLQUFLLElBQ1Ysc0JBQXNCO0lBQ3RCLENBQUNMLEtBQUtNLElBQUksSUFDViwwQ0FBMEM7SUFDMUMsV0FBV0MsSUFBSSxDQUFDUCxLQUFLSyxLQUFLLEtBQzFCLGlEQUFpRDtJQUNqRCxDQUFDLDBDQUEwQ0UsSUFBSSxDQUFDUCxLQUFLSyxLQUFLO0FBRWhFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9tZGFzdC11dGlsLXRvLW1hcmtkb3duL2xpYi91dGlsL2Zvcm1hdC1jb2RlLWFzLWluZGVudGVkLmpzPzFkNmIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTdGF0ZX0gZnJvbSAnbWRhc3QtdXRpbC10by1tYXJrZG93bidcbiAqIEBpbXBvcnQge0NvZGV9IGZyb20gJ21kYXN0J1xuICovXG5cbi8qKlxuICogQHBhcmFtIHtDb2RlfSBub2RlXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRDb2RlQXNJbmRlbnRlZChub2RlLCBzdGF0ZSkge1xuICByZXR1cm4gQm9vbGVhbihcbiAgICBzdGF0ZS5vcHRpb25zLmZlbmNlcyA9PT0gZmFsc2UgJiZcbiAgICAgIG5vZGUudmFsdWUgJiZcbiAgICAgIC8vIElmIHRoZXJl4oCZcyBubyBpbmZv4oCmXG4gICAgICAhbm9kZS5sYW5nICYmXG4gICAgICAvLyBBbmQgdGhlcmXigJlzIGEgbm9uLXdoaXRlc3BhY2UgY2hhcmFjdGVy4oCmXG4gICAgICAvW14gXFxyXFxuXS8udGVzdChub2RlLnZhbHVlKSAmJlxuICAgICAgLy8gQW5kIHRoZSB2YWx1ZSBkb2VzbuKAmXQgc3RhcnQgb3IgZW5kIGluIGEgYmxhbmvigKZcbiAgICAgICEvXltcXHQgXSooPzpbXFxyXFxuXXwkKXwoPzpefFtcXHJcXG5dKVtcXHQgXSokLy50ZXN0KG5vZGUudmFsdWUpXG4gIClcbn1cbiJdLCJuYW1lcyI6WyJmb3JtYXRDb2RlQXNJbmRlbnRlZCIsIm5vZGUiLCJzdGF0ZSIsIkJvb2xlYW4iLCJvcHRpb25zIiwiZmVuY2VzIiwidmFsdWUiLCJsYW5nIiwidGVzdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatHeadingAsSetext: () => (/* binding */ formatHeadingAsSetext)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */ \n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */ function formatHeadingAsSetext(node, state) {\n    let literalWithBreak = false;\n    // Look for literals with a line break.\n    // Note that this also\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(node, function(node) {\n        if (\"value\" in node && /\\r?\\n|\\r/.test(node.value) || node.type === \"break\") {\n            literalWithBreak = true;\n            return unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.EXIT;\n        }\n    });\n    return Boolean((!node.depth || node.depth < 3) && (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_2__.toString)(node) && (state.options.setext || literalWithBreak));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLinkAsAutolink: () => (/* binding */ formatLinkAsAutolink)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */ \n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */ function formatLinkAsAutolink(node, state) {\n    const raw = (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_0__.toString)(node);\n    return Boolean(!state.options.resourceLink && // If there’s a url…\n    node.url && // And there’s a no title…\n    !node.title && // And the content of `node` is a single text node…\n    node.children && node.children.length === 1 && node.children[0].type === \"text\" && // And if the url is the same as the content…\n    (raw === node.url || \"mailto:\" + raw === node.url) && // And that starts w/ a protocol…\n    /^[a-z][a-z+.-]+:/i.test(node.url) && // And that doesn’t contain ASCII control codes (character escapes and\n    // references don’t work), space, or angle brackets…\n    !/[\\0- <>\\u007F]/.test(node.url));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js":
/*!**************************************************************************!*\
  !*** ./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patternInScope: () => (/* binding */ patternInScope)\n/* harmony export */ });\n/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */ /**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */ function patternInScope(stack, pattern) {\n    return listInScope(stack, pattern.inConstruct, true) && !listInScope(stack, pattern.notInConstruct, false);\n}\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */ function listInScope(stack, list, none) {\n    if (typeof list === \"string\") {\n        list = [\n            list\n        ];\n    }\n    if (!list || list.length === 0) {\n        return none;\n    }\n    let index = -1;\n    while(++index < list.length){\n        if (stack.includes(list[index])) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js\n");

/***/ })

};
;