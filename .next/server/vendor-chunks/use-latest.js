"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-latest";
exports.ids = ["vendor-chunks/use-latest"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-latest/dist/use-latest.esm.js":
/*!********************************************************!*\
  !*** ./node_modules/use-latest/dist/use-latest.esm.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLatest)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-isomorphic-layout-effect */ \"(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\");\n\n\nvar useLatest = function useLatest(value) {\n    var ref = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(value);\n    (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function() {\n        ref.current = value;\n    });\n    return ref;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWxhdGVzdC9kaXN0L3VzZS1sYXRlc3QuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDMkM7QUFFckUsSUFBSUUsWUFBWSxTQUFTQSxVQUFVQyxLQUFLO0lBQ3RDLElBQUlDLE1BQU1KLG1EQUFZLENBQUNHO0lBQ3ZCRix3RUFBeUJBLENBQUM7UUFDeEJHLElBQUlFLE9BQU8sR0FBR0g7SUFDaEI7SUFDQSxPQUFPQztBQUNUO0FBRWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy91c2UtbGF0ZXN0L2Rpc3QvdXNlLWxhdGVzdC5lc20uanM/Nzc0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QgZnJvbSAndXNlLWlzb21vcnBoaWMtbGF5b3V0LWVmZmVjdCc7XG5cbnZhciB1c2VMYXRlc3QgPSBmdW5jdGlvbiB1c2VMYXRlc3QodmFsdWUpIHtcbiAgdmFyIHJlZiA9IFJlYWN0LnVzZVJlZih2YWx1ZSk7XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH0pO1xuICByZXR1cm4gcmVmO1xufTtcblxuZXhwb3J0IHsgdXNlTGF0ZXN0IGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJ1c2VMYXRlc3QiLCJ2YWx1ZSIsInJlZiIsInVzZVJlZiIsImN1cnJlbnQiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-latest/dist/use-latest.esm.js\n");

/***/ })

};
;