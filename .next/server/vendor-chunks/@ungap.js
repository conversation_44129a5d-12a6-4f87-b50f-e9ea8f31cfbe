"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ungap";
exports.ids = ["vendor-chunks/@ungap"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/deserialize.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserialize: () => (/* binding */ deserialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\nconst env = typeof self === \"object\" ? self : globalThis;\nconst deserializer = ($, _)=>{\n    const as = (out, index)=>{\n        $.set(index, out);\n        return out;\n    };\n    const unpair = (index)=>{\n        if ($.has(index)) return $.get(index);\n        const [type, value] = _[index];\n        switch(type){\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID:\n                return as(value, index);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY:\n                {\n                    const arr = as([], index);\n                    for (const index of value)arr.push(unpair(index));\n                    return arr;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT:\n                {\n                    const object = as({}, index);\n                    for (const [key, index] of value)object[unpair(key)] = unpair(index);\n                    return object;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n                return as(new Date(value), index);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP:\n                {\n                    const { source, flags } = value;\n                    return as(new RegExp(source, flags), index);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP:\n                {\n                    const map = as(new Map, index);\n                    for (const [key, index] of value)map.set(unpair(key), unpair(index));\n                    return map;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET:\n                {\n                    const set = as(new Set, index);\n                    for (const index of value)set.add(unpair(index));\n                    return set;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR:\n                {\n                    const { name, message } = value;\n                    return as(new env[name](message), index);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT:\n                return as(BigInt(value), index);\n            case \"BigInt\":\n                return as(Object(BigInt(value)), index);\n            case \"ArrayBuffer\":\n                return as(new Uint8Array(value).buffer, value);\n            case \"DataView\":\n                {\n                    const { buffer } = new Uint8Array(value);\n                    return as(new DataView(buffer), value);\n                }\n        }\n        return as(new env[type](value), index);\n    };\n    return unpair;\n};\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */ const deserialize = (serialized)=>deserializer(new Map, serialized)(0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   deserialize: () => (/* reexport safe */ _deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize),\n/* harmony export */   serialize: () => (/* reexport safe */ _serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _deserialize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/deserialize.js\");\n/* harmony import */ var _serialize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serialize.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\");\n\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */ /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (typeof structuredClone === \"function\" ? /* c8 ignore start */ (any, options)=>options && (\"json\" in options || \"lossy\" in options) ? (0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)) : structuredClone(any) : (any, options)=>(0,_deserialize_js__WEBPACK_IMPORTED_MODULE_0__.deserialize)((0,_serialize_js__WEBPACK_IMPORTED_MODULE_1__.serialize)(any, options)));\n/* c8 ignore stop */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZDO0FBQ0o7QUFFekM7O0NBRUMsR0FFRDs7Ozs7OztDQU9DLEdBQ0QsaUVBQWUsT0FBT0Usb0JBQW9CLGFBQ3hDLG1CQUFtQixHQUNuQixDQUFDQyxLQUFLQyxVQUNKQSxXQUFZLFdBQVVBLFdBQVcsV0FBV0EsT0FBTSxJQUNoREosNERBQVdBLENBQUNDLHdEQUFTQSxDQUFDRSxLQUFLQyxZQUFZRixnQkFBZ0JDLE9BRTNELENBQUNBLEtBQUtDLFVBQVlKLDREQUFXQSxDQUFDQyx3REFBU0EsQ0FBQ0UsS0FBS0MsU0FBU0EsRUFBQztBQUN2RCxrQkFBa0IsR0FFWSIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL2luZGV4LmpzP2I3ZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtkZXNlcmlhbGl6ZX0gZnJvbSAnLi9kZXNlcmlhbGl6ZS5qcyc7XG5pbXBvcnQge3NlcmlhbGl6ZX0gZnJvbSAnLi9zZXJpYWxpemUuanMnO1xuXG4vKipcbiAqIEB0eXBlZGVmIHtBcnJheTxzdHJpbmcsYW55Pn0gUmVjb3JkIGEgdHlwZSByZXByZXNlbnRhdGlvblxuICovXG5cbi8qKlxuICogUmV0dXJucyBhbiBhcnJheSBvZiBzZXJpYWxpemVkIFJlY29yZHMuXG4gKiBAcGFyYW0ge2FueX0gYW55IGEgc2VyaWFsaXphYmxlIHZhbHVlLlxuICogQHBhcmFtIHt7dHJhbnNmZXI/OiBhbnlbXSwganNvbj86IGJvb2xlYW4sIGxvc3N5PzogYm9vbGVhbn0/fSBvcHRpb25zIGFuIG9iamVjdCB3aXRoXG4gKiBhIHRyYW5zZmVyIG9wdGlvbiAoaWdub3JlZCB3aGVuIHBvbHlmaWxsZWQpIGFuZC9vciBub24gc3RhbmRhcmQgZmllbGRzIHRoYXRcbiAqIGZhbGxiYWNrIHRvIHRoZSBwb2x5ZmlsbCBpZiBwcmVzZW50LlxuICogQHJldHVybnMge1JlY29yZFtdfVxuICovXG5leHBvcnQgZGVmYXVsdCB0eXBlb2Ygc3RydWN0dXJlZENsb25lID09PSBcImZ1bmN0aW9uXCIgP1xuICAvKiBjOCBpZ25vcmUgc3RhcnQgKi9cbiAgKGFueSwgb3B0aW9ucykgPT4gKFxuICAgIG9wdGlvbnMgJiYgKCdqc29uJyBpbiBvcHRpb25zIHx8ICdsb3NzeScgaW4gb3B0aW9ucykgP1xuICAgICAgZGVzZXJpYWxpemUoc2VyaWFsaXplKGFueSwgb3B0aW9ucykpIDogc3RydWN0dXJlZENsb25lKGFueSlcbiAgKSA6XG4gIChhbnksIG9wdGlvbnMpID0+IGRlc2VyaWFsaXplKHNlcmlhbGl6ZShhbnksIG9wdGlvbnMpKTtcbiAgLyogYzggaWdub3JlIHN0b3AgKi9cblxuZXhwb3J0IHtkZXNlcmlhbGl6ZSwgc2VyaWFsaXplfTtcbiJdLCJuYW1lcyI6WyJkZXNlcmlhbGl6ZSIsInNlcmlhbGl6ZSIsInN0cnVjdHVyZWRDbG9uZSIsImFueSIsIm9wdGlvbnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/serialize.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serialize: () => (/* binding */ serialize)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\");\n\nconst EMPTY = \"\";\nconst { toString } = {};\nconst { keys } = Object;\nconst typeOf = (value)=>{\n    const type = typeof value;\n    if (type !== \"object\" || !value) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE,\n        type\n    ];\n    const asString = toString.call(value).slice(8, -1);\n    switch(asString){\n        case \"Array\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n                EMPTY\n            ];\n        case \"Object\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT,\n                EMPTY\n            ];\n        case \"Date\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE,\n                EMPTY\n            ];\n        case \"RegExp\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP,\n                EMPTY\n            ];\n        case \"Map\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP,\n                EMPTY\n            ];\n        case \"Set\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.SET,\n                EMPTY\n            ];\n        case \"DataView\":\n            return [\n                _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n                asString\n            ];\n    }\n    if (asString.includes(\"Array\")) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY,\n        asString\n    ];\n    if (asString.includes(\"Error\")) return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.ERROR,\n        asString\n    ];\n    return [\n        _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT,\n        asString\n    ];\n};\nconst shouldSkip = ([TYPE, type])=>TYPE === _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE && (type === \"function\" || type === \"symbol\");\nconst serializer = (strict, json, $, _)=>{\n    const as = (out, value)=>{\n        const index = _.push(out) - 1;\n        $.set(value, index);\n        return index;\n    };\n    const pair = (value)=>{\n        if ($.has(value)) return $.get(value);\n        let [TYPE, type] = typeOf(value);\n        switch(TYPE){\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.PRIMITIVE:\n                {\n                    let entry = value;\n                    switch(type){\n                        case \"bigint\":\n                            TYPE = _types_js__WEBPACK_IMPORTED_MODULE_0__.BIGINT;\n                            entry = value.toString();\n                            break;\n                        case \"function\":\n                        case \"symbol\":\n                            if (strict) throw new TypeError(\"unable to serialize \" + type);\n                            entry = null;\n                            break;\n                        case \"undefined\":\n                            return as([\n                                _types_js__WEBPACK_IMPORTED_MODULE_0__.VOID\n                            ], value);\n                    }\n                    return as([\n                        TYPE,\n                        entry\n                    ], value);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.ARRAY:\n                {\n                    if (type) {\n                        let spread = value;\n                        if (type === \"DataView\") {\n                            spread = new Uint8Array(value.buffer);\n                        } else if (type === \"ArrayBuffer\") {\n                            spread = new Uint8Array(value);\n                        }\n                        return as([\n                            type,\n                            [\n                                ...spread\n                            ]\n                        ], value);\n                    }\n                    const arr = [];\n                    const index = as([\n                        TYPE,\n                        arr\n                    ], value);\n                    for (const entry of value)arr.push(pair(entry));\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.OBJECT:\n                {\n                    if (type) {\n                        switch(type){\n                            case \"BigInt\":\n                                return as([\n                                    type,\n                                    value.toString()\n                                ], value);\n                            case \"Boolean\":\n                            case \"Number\":\n                            case \"String\":\n                                return as([\n                                    type,\n                                    value.valueOf()\n                                ], value);\n                        }\n                    }\n                    if (json && \"toJSON\" in value) return pair(value.toJSON());\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const key of keys(value)){\n                        if (strict || !shouldSkip(typeOf(value[key]))) entries.push([\n                            pair(key),\n                            pair(value[key])\n                        ]);\n                    }\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.DATE:\n                return as([\n                    TYPE,\n                    value.toISOString()\n                ], value);\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.REGEXP:\n                {\n                    const { source, flags } = value;\n                    return as([\n                        TYPE,\n                        {\n                            source,\n                            flags\n                        }\n                    ], value);\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.MAP:\n                {\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const [key, entry] of value){\n                        if (strict || !(shouldSkip(typeOf(key)) || shouldSkip(typeOf(entry)))) entries.push([\n                            pair(key),\n                            pair(entry)\n                        ]);\n                    }\n                    return index;\n                }\n            case _types_js__WEBPACK_IMPORTED_MODULE_0__.SET:\n                {\n                    const entries = [];\n                    const index = as([\n                        TYPE,\n                        entries\n                    ], value);\n                    for (const entry of value){\n                        if (strict || !shouldSkip(typeOf(entry))) entries.push(pair(entry));\n                    }\n                    return index;\n                }\n        }\n        const { message } = value;\n        return as([\n            TYPE,\n            {\n                name: type,\n                message\n            }\n        ], value);\n    };\n    return pair;\n};\n/**\n * @typedef {Array<string,any>} Record a type representation\n */ /**\n * Returns an array of serialized Records.\n * @param {any} value a serializable value.\n * @param {{json?: boolean, lossy?: boolean}?} options an object with a `lossy` or `json` property that,\n *  if `true`, will not throw errors on incompatible types, and behave more\n *  like JSON stringify would behave. Symbol and Function will be discarded.\n * @returns {Record[]}\n */ const serialize = (value, { json, lossy } = {})=>{\n    const _ = [];\n    return serializer(!(json || lossy), !!json, new Map, _)(value), _;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/serialize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ungap/structured-clone/esm/types.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ungap/structured-clone/esm/types.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ARRAY: () => (/* binding */ ARRAY),\n/* harmony export */   BIGINT: () => (/* binding */ BIGINT),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   ERROR: () => (/* binding */ ERROR),\n/* harmony export */   MAP: () => (/* binding */ MAP),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   PRIMITIVE: () => (/* binding */ PRIMITIVE),\n/* harmony export */   REGEXP: () => (/* binding */ REGEXP),\n/* harmony export */   SET: () => (/* binding */ SET),\n/* harmony export */   VOID: () => (/* binding */ VOID)\n/* harmony export */ });\nconst VOID = -1;\nconst PRIMITIVE = 0;\nconst ARRAY = 1;\nconst OBJECT = 2;\nconst DATE = 3;\nconst REGEXP = 4;\nconst MAP = 5;\nconst SET = 6;\nconst ERROR = 7;\nconst BIGINT = 8; // export const SYMBOL = 9;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBTyxNQUFNQSxPQUFhLENBQUMsRUFBRTtBQUN0QixNQUFNQyxZQUFhLEVBQUU7QUFDckIsTUFBTUMsUUFBYSxFQUFFO0FBQ3JCLE1BQU1DLFNBQWEsRUFBRTtBQUNyQixNQUFNQyxPQUFhLEVBQUU7QUFDckIsTUFBTUMsU0FBYSxFQUFFO0FBQ3JCLE1BQU1DLE1BQWEsRUFBRTtBQUNyQixNQUFNQyxNQUFhLEVBQUU7QUFDckIsTUFBTUMsUUFBYSxFQUFFO0FBQ3JCLE1BQU1DLFNBQWEsRUFBRSxDQUM1QiwyQkFBMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL0B1bmdhcC9zdHJ1Y3R1cmVkLWNsb25lL2VzbS90eXBlcy5qcz81ZTllIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBWT0lEICAgICAgID0gLTE7XG5leHBvcnQgY29uc3QgUFJJTUlUSVZFICA9IDA7XG5leHBvcnQgY29uc3QgQVJSQVkgICAgICA9IDE7XG5leHBvcnQgY29uc3QgT0JKRUNUICAgICA9IDI7XG5leHBvcnQgY29uc3QgREFURSAgICAgICA9IDM7XG5leHBvcnQgY29uc3QgUkVHRVhQICAgICA9IDQ7XG5leHBvcnQgY29uc3QgTUFQICAgICAgICA9IDU7XG5leHBvcnQgY29uc3QgU0VUICAgICAgICA9IDY7XG5leHBvcnQgY29uc3QgRVJST1IgICAgICA9IDc7XG5leHBvcnQgY29uc3QgQklHSU5UICAgICA9IDg7XG4vLyBleHBvcnQgY29uc3QgU1lNQk9MID0gOTtcbiJdLCJuYW1lcyI6WyJWT0lEIiwiUFJJTUlUSVZFIiwiQVJSQVkiLCJPQkpFQ1QiLCJEQVRFIiwiUkVHRVhQIiwiTUFQIiwiU0VUIiwiRVJST1IiLCJCSUdJTlQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ungap/structured-clone/esm/types.js\n");

/***/ })

};
;