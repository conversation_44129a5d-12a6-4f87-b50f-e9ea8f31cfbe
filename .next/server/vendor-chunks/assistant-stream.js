"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/assistant-stream";
exports.ids = ["vendor-chunks/assistant-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/assistant-stream/node_modules/secure-json-parse/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/assistant-stream/node_modules/secure-json-parse/index.js ***!
  \*******************************************************************************/
/***/ ((module) => {

eval("\nconst hasBuffer = typeof Buffer !== \"undefined\";\nconst suspectProtoRx = /\"(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])\"\\s*:/;\nconst suspectConstructorRx = /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/;\nfunction _parse(text, reviver, options) {\n    // Normalize arguments\n    if (options == null) {\n        if (reviver !== null && typeof reviver === \"object\") {\n            options = reviver;\n            reviver = undefined;\n        }\n    }\n    if (hasBuffer && Buffer.isBuffer(text)) {\n        text = text.toString();\n    }\n    // BOM checker\n    if (text && text.charCodeAt(0) === 0xFEFF) {\n        text = text.slice(1);\n    }\n    // Parse normally, allowing exceptions\n    const obj = JSON.parse(text, reviver);\n    // Ignore null and non-objects\n    if (obj === null || typeof obj !== \"object\") {\n        return obj;\n    }\n    const protoAction = options && options.protoAction || \"error\";\n    const constructorAction = options && options.constructorAction || \"error\";\n    // options: 'error' (default) / 'remove' / 'ignore'\n    if (protoAction === \"ignore\" && constructorAction === \"ignore\") {\n        return obj;\n    }\n    if (protoAction !== \"ignore\" && constructorAction !== \"ignore\") {\n        if (suspectProtoRx.test(text) === false && suspectConstructorRx.test(text) === false) {\n            return obj;\n        }\n    } else if (protoAction !== \"ignore\" && constructorAction === \"ignore\") {\n        if (suspectProtoRx.test(text) === false) {\n            return obj;\n        }\n    } else {\n        if (suspectConstructorRx.test(text) === false) {\n            return obj;\n        }\n    }\n    // Scan result for proto keys\n    return filter(obj, {\n        protoAction,\n        constructorAction,\n        safe: options && options.safe\n    });\n}\nfunction filter(obj, { protoAction = \"error\", constructorAction = \"error\", safe } = {}) {\n    let next = [\n        obj\n    ];\n    while(next.length){\n        const nodes = next;\n        next = [];\n        for (const node of nodes){\n            if (protoAction !== \"ignore\" && Object.prototype.hasOwnProperty.call(node, \"__proto__\")) {\n                if (safe === true) {\n                    return null;\n                } else if (protoAction === \"error\") {\n                    throw new SyntaxError(\"Object contains forbidden prototype property\");\n                }\n                delete node.__proto__ // eslint-disable-line no-proto\n                ;\n            }\n            if (constructorAction !== \"ignore\" && Object.prototype.hasOwnProperty.call(node, \"constructor\") && Object.prototype.hasOwnProperty.call(node.constructor, \"prototype\")) {\n                if (safe === true) {\n                    return null;\n                } else if (constructorAction === \"error\") {\n                    throw new SyntaxError(\"Object contains forbidden prototype property\");\n                }\n                delete node.constructor;\n            }\n            for(const key in node){\n                const value = node[key];\n                if (value && typeof value === \"object\") {\n                    next.push(value);\n                }\n            }\n        }\n    }\n    return obj;\n}\nfunction parse(text, reviver, options) {\n    const { stackTraceLimit } = Error;\n    Error.stackTraceLimit = 0;\n    try {\n        return _parse(text, reviver, options);\n    } finally{\n        Error.stackTraceLimit = stackTraceLimit;\n    }\n}\nfunction safeParse(text, reviver) {\n    const { stackTraceLimit } = Error;\n    Error.stackTraceLimit = 0;\n    try {\n        return _parse(text, reviver, {\n            safe: true\n        });\n    } catch  {\n        return undefined;\n    } finally{\n        Error.stackTraceLimit = stackTraceLimit;\n    }\n}\nmodule.exports = parse;\nmodule.exports[\"default\"] = parse;\nmodule.exports.parse = parse;\nmodule.exports.safeParse = safeParse;\nmodule.exports.scan = filter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/node_modules/secure-json-parse/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/AssistantStream.js":
/*!********************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/AssistantStream.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantStream: () => (/* binding */ AssistantStream)\n/* harmony export */ });\n// src/core/AssistantStream.ts\nvar AssistantStream = {\n    toResponse (stream, transformer) {\n        return new Response(AssistantStream.toByteStream(stream, transformer), {\n            headers: transformer.headers ?? {}\n        });\n    },\n    fromResponse (response, transformer) {\n        return AssistantStream.fromByteStream(response.body, transformer);\n    },\n    toByteStream (stream, transformer) {\n        return stream.pipeThrough(transformer);\n    },\n    fromByteStream (readable, transformer) {\n        return readable.pipeThrough(transformer);\n    }\n};\n //# sourceMappingURL=AssistantStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvQXNzaXN0YW50U3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw4QkFBOEI7QUFDOUIsSUFBSUEsa0JBQWtCO0lBQ3BCQyxZQUFXQyxNQUFNLEVBQUVDLFdBQVc7UUFDNUIsT0FBTyxJQUFJQyxTQUFTSixnQkFBZ0JLLFlBQVksQ0FBQ0gsUUFBUUMsY0FBYztZQUNyRUcsU0FBU0gsWUFBWUcsT0FBTyxJQUFJLENBQUM7UUFDbkM7SUFDRjtJQUNBQyxjQUFhQyxRQUFRLEVBQUVMLFdBQVc7UUFDaEMsT0FBT0gsZ0JBQWdCUyxjQUFjLENBQUNELFNBQVNFLElBQUksRUFBRVA7SUFDdkQ7SUFDQUUsY0FBYUgsTUFBTSxFQUFFQyxXQUFXO1FBQzlCLE9BQU9ELE9BQU9TLFdBQVcsQ0FBQ1I7SUFDNUI7SUFDQU0sZ0JBQWVHLFFBQVEsRUFBRVQsV0FBVztRQUNsQyxPQUFPUyxTQUFTRCxXQUFXLENBQUNSO0lBQzlCO0FBQ0Y7QUFHRSxDQUNGLDJDQUEyQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvQXNzaXN0YW50U3RyZWFtLmpzPzJmZTIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvcmUvQXNzaXN0YW50U3RyZWFtLnRzXG52YXIgQXNzaXN0YW50U3RyZWFtID0ge1xuICB0b1Jlc3BvbnNlKHN0cmVhbSwgdHJhbnNmb3JtZXIpIHtcbiAgICByZXR1cm4gbmV3IFJlc3BvbnNlKEFzc2lzdGFudFN0cmVhbS50b0J5dGVTdHJlYW0oc3RyZWFtLCB0cmFuc2Zvcm1lciksIHtcbiAgICAgIGhlYWRlcnM6IHRyYW5zZm9ybWVyLmhlYWRlcnMgPz8ge31cbiAgICB9KTtcbiAgfSxcbiAgZnJvbVJlc3BvbnNlKHJlc3BvbnNlLCB0cmFuc2Zvcm1lcikge1xuICAgIHJldHVybiBBc3Npc3RhbnRTdHJlYW0uZnJvbUJ5dGVTdHJlYW0ocmVzcG9uc2UuYm9keSwgdHJhbnNmb3JtZXIpO1xuICB9LFxuICB0b0J5dGVTdHJlYW0oc3RyZWFtLCB0cmFuc2Zvcm1lcikge1xuICAgIHJldHVybiBzdHJlYW0ucGlwZVRocm91Z2godHJhbnNmb3JtZXIpO1xuICB9LFxuICBmcm9tQnl0ZVN0cmVhbShyZWFkYWJsZSwgdHJhbnNmb3JtZXIpIHtcbiAgICByZXR1cm4gcmVhZGFibGUucGlwZVRocm91Z2godHJhbnNmb3JtZXIpO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgQXNzaXN0YW50U3RyZWFtXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QXNzaXN0YW50U3RyZWFtLmpzLm1hcCJdLCJuYW1lcyI6WyJBc3Npc3RhbnRTdHJlYW0iLCJ0b1Jlc3BvbnNlIiwic3RyZWFtIiwidHJhbnNmb3JtZXIiLCJSZXNwb25zZSIsInRvQnl0ZVN0cmVhbSIsImhlYWRlcnMiLCJmcm9tUmVzcG9uc2UiLCJyZXNwb25zZSIsImZyb21CeXRlU3RyZWFtIiwiYm9keSIsInBpcGVUaHJvdWdoIiwicmVhZGFibGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/AssistantStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/accumulators/AssistantMessageStream.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/accumulators/AssistantMessageStream.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantMessageStream: () => (/* binding */ AssistantMessageStream)\n/* harmony export */ });\n/* harmony import */ var _assistant_message_accumulator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./assistant-message-accumulator.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/accumulators/assistant-message-accumulator.js\");\n// src/core/accumulators/AssistantMessageStream.ts\n\nvar AssistantMessageStream = class _AssistantMessageStream {\n    constructor(readable){\n        this.readable = readable;\n        this.readable = readable;\n    }\n    static fromAssistantStream(stream) {\n        return new _AssistantMessageStream(stream.pipeThrough(new _assistant_message_accumulator_js__WEBPACK_IMPORTED_MODULE_0__.AssistantMessageAccumulator()));\n    }\n    async unstable_result() {\n        let last;\n        for await (const chunk of this){\n            last = chunk;\n        }\n        if (!last) {\n            return {\n                role: \"assistant\",\n                status: {\n                    type: \"complete\",\n                    reason: \"unknown\"\n                },\n                parts: [],\n                content: [],\n                metadata: {\n                    unstable_data: [],\n                    unstable_annotations: [],\n                    steps: [],\n                    custom: {}\n                }\n            };\n        }\n        return last;\n    }\n    [Symbol.asyncIterator]() {\n        const reader = this.readable.getReader();\n        return {\n            async next () {\n                const { done, value } = await reader.read();\n                return done ? {\n                    done: true,\n                    value: void 0\n                } : {\n                    done: false,\n                    value\n                };\n            }\n        };\n    }\n    tee() {\n        const [readable1, readable2] = this.readable.tee();\n        return [\n            new _AssistantMessageStream(readable1),\n            new _AssistantMessageStream(readable2)\n        ];\n    }\n};\n //# sourceMappingURL=AssistantMessageStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/accumulators/AssistantMessageStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/accumulators/assistant-message-accumulator.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/accumulators/assistant-message-accumulator.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantMessageAccumulator: () => (/* binding */ AssistantMessageAccumulator)\n/* harmony export */ });\n/* harmony import */ var _utils_generateId_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/generateId.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/generateId.js\");\n/* harmony import */ var _utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/json/parse-partial-json-object.js */ \"(ssr)/./node_modules/assistant-stream/dist/utils/json/parse-partial-json-object.js\");\n// src/core/accumulators/assistant-message-accumulator.ts\n\n\nvar createInitialMessage = ()=>({\n        role: \"assistant\",\n        status: {\n            type: \"running\"\n        },\n        parts: [],\n        get content () {\n            return this.parts;\n        },\n        metadata: {\n            unstable_data: [],\n            unstable_annotations: [],\n            steps: [],\n            custom: {}\n        }\n    });\nvar updatePartForPath = (message, chunk, updater)=>{\n    if (message.parts.length === 0) {\n        throw new Error(\"No parts available to update.\");\n    }\n    if (chunk.path.length !== 1) throw new Error(\"Nested paths are not supported yet.\");\n    const partIndex = chunk.path[0];\n    const updatedPart = updater(message.parts[partIndex]);\n    return {\n        ...message,\n        parts: [\n            ...message.parts.slice(0, partIndex),\n            updatedPart,\n            ...message.parts.slice(partIndex + 1)\n        ],\n        get content () {\n            return this.parts;\n        }\n    };\n};\nvar handlePartStart = (message, chunk)=>{\n    const partInit = chunk.part;\n    if (partInit.type === \"text\" || partInit.type === \"reasoning\") {\n        const newTextPart = {\n            type: partInit.type,\n            text: \"\",\n            status: {\n                type: \"running\"\n            }\n        };\n        return {\n            ...message,\n            parts: [\n                ...message.parts,\n                newTextPart\n            ],\n            get content () {\n                return this.parts;\n            }\n        };\n    } else if (partInit.type === \"tool-call\") {\n        const newToolCallPart = {\n            type: \"tool-call\",\n            state: \"partial-call\",\n            status: {\n                type: \"running\",\n                isArgsComplete: false\n            },\n            toolCallId: partInit.toolCallId,\n            toolName: partInit.toolName,\n            argsText: \"\",\n            args: {}\n        };\n        return {\n            ...message,\n            parts: [\n                ...message.parts,\n                newToolCallPart\n            ],\n            get content () {\n                return this.parts;\n            }\n        };\n    } else if (partInit.type === \"source\") {\n        const newSourcePart = {\n            type: \"source\",\n            sourceType: partInit.sourceType,\n            id: partInit.id,\n            url: partInit.url,\n            ...partInit.title ? {\n                title: partInit.title\n            } : void 0\n        };\n        return {\n            ...message,\n            parts: [\n                ...message.parts,\n                newSourcePart\n            ],\n            get content () {\n                return this.parts;\n            }\n        };\n    } else if (partInit.type === \"file\") {\n        const newFilePart = {\n            type: \"file\",\n            mimeType: partInit.mimeType,\n            data: partInit.data\n        };\n        return {\n            ...message,\n            parts: [\n                ...message.parts,\n                newFilePart\n            ],\n            get content () {\n                return this.parts;\n            }\n        };\n    } else {\n        throw new Error(`Unsupported part type: ${partInit.type}`);\n    }\n};\nvar handleToolCallArgsTextFinish = (message, chunk)=>{\n    return updatePartForPath(message, chunk, (part)=>{\n        if (part.type !== \"tool-call\") {\n            throw new Error(\"Last is not a tool call\");\n        }\n        return {\n            ...part,\n            state: \"call\"\n        };\n    });\n};\nvar handlePartFinish = (message, chunk)=>{\n    return updatePartForPath(message, chunk, (part)=>({\n            ...part,\n            status: {\n                type: \"complete\",\n                reason: \"unknown\"\n            }\n        }));\n};\nvar handleTextDelta = (message, chunk)=>{\n    return updatePartForPath(message, chunk, (part)=>{\n        if (part.type === \"text\" || part.type === \"reasoning\") {\n            return {\n                ...part,\n                text: part.text + chunk.textDelta\n            };\n        } else if (part.type === \"tool-call\") {\n            const newArgsText = part.argsText + chunk.textDelta;\n            const newArgs = (0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.parsePartialJsonObject)(newArgsText) ?? part.args;\n            return {\n                ...part,\n                argsText: newArgsText,\n                args: newArgs\n            };\n        } else {\n            throw new Error(\"text-delta received but part is neither text nor tool-call\");\n        }\n    });\n};\nvar handleResult = (message, chunk)=>{\n    return updatePartForPath(message, chunk, (part)=>{\n        if (part.type === \"tool-call\") {\n            return {\n                ...part,\n                state: \"result\",\n                artifact: chunk.artifact,\n                result: chunk.result,\n                isError: chunk.isError ?? false,\n                status: {\n                    type: \"complete\",\n                    reason: \"stop\"\n                }\n            };\n        } else {\n            throw new Error(\"Result chunk received but part is not a tool-call\");\n        }\n    });\n};\nvar handleMessageFinish = (message, chunk)=>{\n    const newStatus = getStatus(chunk);\n    return {\n        ...message,\n        status: newStatus\n    };\n};\nvar getStatus = (chunk)=>{\n    if (chunk.finishReason === \"tool-calls\") {\n        return {\n            type: \"requires-action\",\n            reason: \"tool-calls\"\n        };\n    } else if (chunk.finishReason === \"stop\" || chunk.finishReason === \"unknown\") {\n        return {\n            type: \"complete\",\n            reason: chunk.finishReason\n        };\n    } else {\n        return {\n            type: \"incomplete\",\n            reason: chunk.finishReason\n        };\n    }\n};\nvar handleAnnotations = (message, chunk)=>{\n    return {\n        ...message,\n        metadata: {\n            ...message.metadata,\n            unstable_annotations: [\n                ...message.metadata.unstable_annotations,\n                ...chunk.annotations\n            ]\n        }\n    };\n};\nvar handleData = (message, chunk)=>{\n    return {\n        ...message,\n        metadata: {\n            ...message.metadata,\n            unstable_data: [\n                ...message.metadata.unstable_data,\n                ...chunk.data\n            ]\n        }\n    };\n};\nvar handleStepStart = (message, chunk)=>{\n    return {\n        ...message,\n        metadata: {\n            ...message.metadata,\n            steps: [\n                ...message.metadata.steps,\n                {\n                    state: \"started\",\n                    messageId: chunk.messageId\n                }\n            ]\n        }\n    };\n};\nvar handleStepFinish = (message, chunk)=>{\n    const steps = message.metadata.steps.slice();\n    const lastIndex = steps.length - 1;\n    if (steps.length > 0 && steps[lastIndex]?.state === \"started\") {\n        steps[lastIndex] = {\n            ...steps[lastIndex],\n            state: \"finished\",\n            finishReason: chunk.finishReason,\n            usage: chunk.usage,\n            isContinued: chunk.isContinued\n        };\n    } else {\n        steps.push({\n            state: \"finished\",\n            messageId: (0,_utils_generateId_js__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n            finishReason: chunk.finishReason,\n            usage: chunk.usage,\n            isContinued: chunk.isContinued\n        });\n    }\n    return {\n        ...message,\n        metadata: {\n            ...message.metadata,\n            steps\n        }\n    };\n};\nvar handleErrorChunk = (message, chunk)=>{\n    return {\n        ...message,\n        status: {\n            type: \"incomplete\",\n            reason: \"error\",\n            error: chunk.error\n        }\n    };\n};\nvar AssistantMessageAccumulator = class extends TransformStream {\n    constructor({ initialMessage } = {}){\n        let message = initialMessage ?? createInitialMessage();\n        super({\n            transform (chunk, controller) {\n                const type = chunk.type;\n                switch(type){\n                    case \"part-start\":\n                        message = handlePartStart(message, chunk);\n                        break;\n                    case \"tool-call-args-text-finish\":\n                        message = handleToolCallArgsTextFinish(message, chunk);\n                        break;\n                    case \"part-finish\":\n                        message = handlePartFinish(message, chunk);\n                        break;\n                    case \"text-delta\":\n                        message = handleTextDelta(message, chunk);\n                        break;\n                    case \"result\":\n                        message = handleResult(message, chunk);\n                        break;\n                    case \"message-finish\":\n                        message = handleMessageFinish(message, chunk);\n                        break;\n                    case \"annotations\":\n                        message = handleAnnotations(message, chunk);\n                        break;\n                    case \"data\":\n                        message = handleData(message, chunk);\n                        break;\n                    case \"step-start\":\n                        message = handleStepStart(message, chunk);\n                        break;\n                    case \"step-finish\":\n                        message = handleStepFinish(message, chunk);\n                        break;\n                    case \"error\":\n                        message = handleErrorChunk(message, chunk);\n                        break;\n                    default:\n                        {\n                            const unhandledType = type;\n                            throw new Error(`Unsupported chunk type: ${unhandledType}`);\n                        }\n                }\n                controller.enqueue(message);\n            },\n            flush (controller) {\n                if (message.status?.type === \"running\") {\n                    const requiresAction = message.parts?.at(-1)?.type === \"tool-call\";\n                    message = handleMessageFinish(message, {\n                        type: \"message-finish\",\n                        path: [],\n                        finishReason: requiresAction ? \"tool-calls\" : \"unknown\",\n                        usage: {\n                            promptTokens: 0,\n                            completionTokens: 0\n                        }\n                    });\n                    controller.enqueue(message);\n                }\n            }\n        });\n    }\n};\n //# sourceMappingURL=assistant-message-accumulator.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/accumulators/assistant-message-accumulator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/modules/assistant-stream.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/modules/assistant-stream.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAssistantStream: () => (/* binding */ createAssistantStream),\n/* harmony export */   createAssistantStreamController: () => (/* binding */ createAssistantStreamController),\n/* harmony export */   createAssistantStreamResponse: () => (/* binding */ createAssistantStreamResponse)\n/* harmony export */ });\n/* harmony import */ var _AssistantStream_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../AssistantStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/AssistantStream.js\");\n/* harmony import */ var _utils_stream_merge_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/stream/merge.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/merge.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/modules/text.js\");\n/* harmony import */ var _tool_call_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tool-call.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/modules/tool-call.js\");\n/* harmony import */ var _utils_Counter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/Counter.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/Counter.js\");\n/* harmony import */ var _utils_stream_path_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/stream/path-utils.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/path-utils.js\");\n/* harmony import */ var _serialization_data_stream_DataStream_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../serialization/data-stream/DataStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/DataStream.js\");\n/* harmony import */ var _utils_generateId_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/generateId.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/generateId.js\");\n/* harmony import */ var _utils_promiseWithResolvers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/promiseWithResolvers.js */ \"(ssr)/./node_modules/assistant-stream/dist/utils/promiseWithResolvers.js\");\n// src/core/modules/assistant-stream.ts\n\n\n\n\n\n\n\n\n\nvar AssistantStreamControllerImpl = class {\n    get __internal_isClosed() {\n        return this._merger.isSealed();\n    }\n    __internal_getReadable() {\n        return this._merger.readable;\n    }\n    __internal_subscribeToClose(callback) {\n        this._closeSubscriber = callback;\n    }\n    _addPart(part, stream) {\n        if (this._append) {\n            this._append.controller.close();\n            this._append = void 0;\n        }\n        this.enqueue({\n            type: \"part-start\",\n            part,\n            path: []\n        });\n        this._merger.addStream(stream.pipeThrough(new _utils_stream_path_utils_js__WEBPACK_IMPORTED_MODULE_0__.PathAppendEncoder(this._contentCounter.value)));\n    }\n    merge(stream) {\n        this._merger.addStream(stream.pipeThrough(new _utils_stream_path_utils_js__WEBPACK_IMPORTED_MODULE_0__.PathMergeEncoder(this._contentCounter)));\n    }\n    appendText(textDelta) {\n        if (this._append?.kind !== \"text\") {\n            this._append = {\n                kind: \"text\",\n                controller: this.addTextPart()\n            };\n        }\n        this._append.controller.append(textDelta);\n    }\n    appendReasoning(textDelta) {\n        if (this._append?.kind !== \"reasoning\") {\n            this._append = {\n                kind: \"reasoning\",\n                controller: this.addReasoningPart()\n            };\n        }\n        this._append.controller.append(textDelta);\n    }\n    addTextPart() {\n        const [stream, controller] = (0,_text_js__WEBPACK_IMPORTED_MODULE_1__.createTextStreamController)();\n        this._addPart({\n            type: \"text\"\n        }, stream);\n        return controller;\n    }\n    addReasoningPart() {\n        const [stream, controller] = (0,_text_js__WEBPACK_IMPORTED_MODULE_1__.createTextStreamController)();\n        this._addPart({\n            type: \"reasoning\"\n        }, stream);\n        return controller;\n    }\n    addToolCallPart(options) {\n        const opt = typeof options === \"string\" ? {\n            toolName: options\n        } : options;\n        const toolName = opt.toolName;\n        const toolCallId = opt.toolCallId ?? (0,_utils_generateId_js__WEBPACK_IMPORTED_MODULE_2__.generateId)();\n        const [stream, controller] = (0,_tool_call_js__WEBPACK_IMPORTED_MODULE_3__.createToolCallStreamController)();\n        this._addPart({\n            type: \"tool-call\",\n            toolName,\n            toolCallId\n        }, stream);\n        if (opt.argsText !== void 0) {\n            controller.argsText.append(opt.argsText);\n            controller.argsText.close();\n        }\n        if (opt.args !== void 0) {\n            controller.argsText.append(JSON.stringify(opt.args));\n            controller.argsText.close();\n        }\n        if (opt.response !== void 0) {\n            controller.setResponse(opt.response);\n        }\n        return controller;\n    }\n    appendSource(options) {\n        this._addPart(options, new ReadableStream({\n            start (controller) {\n                controller.enqueue({\n                    type: \"part-finish\",\n                    path: []\n                });\n                controller.close();\n            }\n        }));\n    }\n    appendFile(options) {\n        this._addPart(options, new ReadableStream({\n            start (controller) {\n                controller.enqueue({\n                    type: \"part-finish\",\n                    path: []\n                });\n                controller.close();\n            }\n        }));\n    }\n    enqueue(chunk) {\n        this._merger.enqueue(chunk);\n        if (chunk.type === \"part-start\" && chunk.path.length === 0) {\n            this._contentCounter.up();\n        }\n    }\n    close() {\n        this._merger.seal();\n        this._append?.controller?.close();\n        this._closeSubscriber?.();\n    }\n    constructor(){\n        this._merger = (0,_utils_stream_merge_js__WEBPACK_IMPORTED_MODULE_4__.createMergeStream)();\n        this._contentCounter = new _utils_Counter_js__WEBPACK_IMPORTED_MODULE_5__.Counter();\n    }\n};\nfunction createAssistantStream(callback) {\n    const controller = new AssistantStreamControllerImpl();\n    let promiseOrVoid;\n    try {\n        promiseOrVoid = callback(controller);\n    } catch (e) {\n        if (!controller.__internal_isClosed) {\n            controller.enqueue({\n                type: \"error\",\n                path: [],\n                error: String(e)\n            });\n            controller.close();\n        }\n        throw e;\n    }\n    if (promiseOrVoid instanceof Promise) {\n        const runTask = async ()=>{\n            try {\n                await promiseOrVoid;\n            } catch (e) {\n                if (!controller.__internal_isClosed) {\n                    controller.enqueue({\n                        type: \"error\",\n                        path: [],\n                        error: String(e)\n                    });\n                }\n                throw e;\n            } finally{\n                if (!controller.__internal_isClosed) {\n                    controller.close();\n                }\n            }\n        };\n        runTask();\n    } else {\n        if (!controller.__internal_isClosed) {\n            controller.close();\n        }\n    }\n    return controller.__internal_getReadable();\n}\nfunction createAssistantStreamController() {\n    const { resolve, promise } = (0,_utils_promiseWithResolvers_js__WEBPACK_IMPORTED_MODULE_6__.promiseWithResolvers)();\n    let controller;\n    const stream = createAssistantStream((c)=>{\n        controller = c;\n        controller.__internal_subscribeToClose(resolve);\n        return promise;\n    });\n    return [\n        stream,\n        controller\n    ];\n}\nfunction createAssistantStreamResponse(callback) {\n    return _AssistantStream_js__WEBPACK_IMPORTED_MODULE_7__.AssistantStream.toResponse(createAssistantStream(callback), new _serialization_data_stream_DataStream_js__WEBPACK_IMPORTED_MODULE_8__.DataStreamEncoder());\n}\n //# sourceMappingURL=assistant-stream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/modules/assistant-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/modules/text.js":
/*!*****************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/modules/text.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTextStream: () => (/* binding */ createTextStream),\n/* harmony export */   createTextStreamController: () => (/* binding */ createTextStreamController)\n/* harmony export */ });\n// src/core/modules/text.ts\nvar TextStreamControllerImpl = class {\n    constructor(controller){\n        this._isClosed = false;\n        this._controller = controller;\n    }\n    append(textDelta) {\n        this._controller.enqueue({\n            type: \"text-delta\",\n            path: [],\n            textDelta\n        });\n        return this;\n    }\n    close() {\n        if (this._isClosed) return;\n        this._isClosed = true;\n        this._controller.enqueue({\n            type: \"part-finish\",\n            path: []\n        });\n        this._controller.close();\n    }\n};\nvar createTextStream = (readable)=>{\n    return new ReadableStream({\n        start (c) {\n            return readable.start?.(new TextStreamControllerImpl(c));\n        },\n        pull (c) {\n            return readable.pull?.(new TextStreamControllerImpl(c));\n        },\n        cancel (c) {\n            return readable.cancel?.(c);\n        }\n    });\n};\nvar createTextStreamController = ()=>{\n    let controller;\n    const stream = createTextStream({\n        start (c) {\n            controller = c;\n        }\n    });\n    return [\n        stream,\n        controller\n    ];\n};\n //# sourceMappingURL=text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/modules/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/modules/tool-call.js":
/*!**********************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/modules/tool-call.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToolCallStream: () => (/* binding */ createToolCallStream),\n/* harmony export */   createToolCallStreamController: () => (/* binding */ createToolCallStreamController)\n/* harmony export */ });\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/modules/text.js\");\n// src/core/modules/tool-call.ts\n\nvar ToolCallStreamControllerImpl = class {\n    constructor(_controller){\n        this._isClosed = false;\n        this._controller = _controller;\n        const stream = (0,_text_js__WEBPACK_IMPORTED_MODULE_0__.createTextStream)({\n            start: (c)=>{\n                this._argsTextController = c;\n            }\n        });\n        this._mergeTask = stream.pipeTo(new WritableStream({\n            write: (chunk)=>{\n                switch(chunk.type){\n                    case \"text-delta\":\n                        this._controller.enqueue(chunk);\n                        break;\n                    case \"part-finish\":\n                        this._controller.enqueue({\n                            type: \"tool-call-args-text-finish\",\n                            path: []\n                        });\n                        break;\n                    default:\n                        throw new Error(`Unexpected chunk type: ${chunk.type}`);\n                }\n            }\n        }));\n    }\n    get argsText() {\n        return this._argsTextController;\n    }\n    setResponse(response) {\n        this._controller.enqueue({\n            type: \"result\",\n            path: [],\n            artifact: response.artifact,\n            result: response.result,\n            isError: response.isError ?? false\n        });\n    }\n    async close() {\n        if (this._isClosed) return;\n        this._isClosed = true;\n        this._argsTextController.close();\n        await this._mergeTask;\n        this._controller.enqueue({\n            type: \"part-finish\",\n            path: []\n        });\n        this._controller.close();\n    }\n};\nvar createToolCallStream = (readable)=>{\n    return new ReadableStream({\n        start (c) {\n            return readable.start?.(new ToolCallStreamControllerImpl(c));\n        },\n        pull (c) {\n            return readable.pull?.(new ToolCallStreamControllerImpl(c));\n        },\n        cancel (c) {\n            return readable.cancel?.(c);\n        }\n    });\n};\nvar createToolCallStreamController = ()=>{\n    let controller;\n    const stream = createToolCallStream({\n        start (c) {\n            controller = c;\n        }\n    });\n    return [\n        stream,\n        controller\n    ];\n};\n //# sourceMappingURL=tool-call.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/modules/tool-call.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/serialization/PlainText.js":
/*!****************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/serialization/PlainText.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PlainTextDecoder: () => (/* binding */ PlainTextDecoder),\n/* harmony export */   PlainTextEncoder: () => (/* binding */ PlainTextEncoder)\n/* harmony export */ });\n/* harmony import */ var _utils_stream_AssistantTransformStream_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/stream/AssistantTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantTransformStream.js\");\n/* harmony import */ var _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/stream/PipeableTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/PipeableTransformStream.js\");\n// src/core/serialization/PlainText.ts\n\n\nvar PlainTextEncoder = class extends _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_0__.PipeableTransformStream {\n    constructor(){\n        super((readable)=>{\n            const transform = new TransformStream({\n                transform (chunk, controller) {\n                    const type = chunk.type;\n                    switch(type){\n                        case \"text-delta\":\n                            controller.enqueue(chunk.textDelta);\n                            break;\n                        default:\n                            const unsupportedType = type;\n                            throw new Error(`unsupported chunk type: ${unsupportedType}`);\n                    }\n                }\n            });\n            return readable.pipeThrough(transform).pipeThrough(new TextEncoderStream());\n        });\n        this.headers = new Headers({\n            \"Content-Type\": \"text/plain; charset=utf-8\",\n            \"x-vercel-ai-data-stream\": \"v1\"\n        });\n    }\n};\nvar PlainTextDecoder = class extends _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_0__.PipeableTransformStream {\n    constructor(){\n        super((readable)=>{\n            const transform = new _utils_stream_AssistantTransformStream_js__WEBPACK_IMPORTED_MODULE_1__.AssistantTransformStream({\n                transform (chunk, controller) {\n                    controller.appendText(chunk);\n                }\n            });\n            return readable.pipeThrough(new TextDecoderStream()).pipeThrough(transform);\n        });\n    }\n};\n //# sourceMappingURL=PlainText.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/serialization/PlainText.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/DataStream.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/serialization/data-stream/DataStream.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataStreamDecoder: () => (/* binding */ DataStreamDecoder),\n/* harmony export */   DataStreamEncoder: () => (/* binding */ DataStreamEncoder)\n/* harmony export */ });\n/* harmony import */ var _utils_stream_AssistantTransformStream_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/stream/AssistantTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantTransformStream.js\");\n/* harmony import */ var _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/stream/PipeableTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/PipeableTransformStream.js\");\n/* harmony import */ var _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-types.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/chunk-types.js\");\n/* harmony import */ var _utils_stream_LineDecoderStream_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/stream/LineDecoderStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/LineDecoderStream.js\");\n/* harmony import */ var _serialization_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./serialization.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/serialization.js\");\n/* harmony import */ var _utils_stream_AssistantMetaTransformStream_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/stream/AssistantMetaTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantMetaTransformStream.js\");\n// src/core/serialization/data-stream/DataStream.ts\n\n\n\n\n\n\nvar DataStreamEncoder = class extends _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_0__.PipeableTransformStream {\n    constructor(){\n        super((readable)=>{\n            const transform = new TransformStream({\n                transform (chunk, controller) {\n                    const type = chunk.type;\n                    switch(type){\n                        case \"part-start\":\n                            {\n                                const part = chunk.part;\n                                if (part.type === \"tool-call\") {\n                                    const { type: type2, ...value } = part;\n                                    controller.enqueue({\n                                        type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.StartToolCall,\n                                        value\n                                    });\n                                }\n                                if (part.type === \"source\") {\n                                    const { type: type2, ...value } = part;\n                                    controller.enqueue({\n                                        type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Source,\n                                        value\n                                    });\n                                }\n                                break;\n                            }\n                        case \"text-delta\":\n                            {\n                                const part = chunk.meta;\n                                switch(part.type){\n                                    case \"text\":\n                                        {\n                                            controller.enqueue({\n                                                type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.TextDelta,\n                                                value: chunk.textDelta\n                                            });\n                                            break;\n                                        }\n                                    case \"reasoning\":\n                                        {\n                                            controller.enqueue({\n                                                type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ReasoningDelta,\n                                                value: chunk.textDelta\n                                            });\n                                            break;\n                                        }\n                                    case \"tool-call\":\n                                        {\n                                            controller.enqueue({\n                                                type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ToolCallArgsTextDelta,\n                                                value: {\n                                                    toolCallId: part.toolCallId,\n                                                    argsTextDelta: chunk.textDelta\n                                                }\n                                            });\n                                            break;\n                                        }\n                                    default:\n                                        throw new Error(`Unsupported part type for text-delta: ${part.type}`);\n                                }\n                                break;\n                            }\n                        case \"result\":\n                            {\n                                const part = chunk.meta;\n                                if (part.type !== \"tool-call\") {\n                                    throw new Error(`Result chunk on non-tool-call part not supported: ${part.type}`);\n                                }\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ToolCallResult,\n                                    value: {\n                                        toolCallId: part.toolCallId,\n                                        result: chunk.result,\n                                        artifact: chunk.artifact,\n                                        ...chunk.isError ? {\n                                            isError: chunk.isError\n                                        } : {}\n                                    }\n                                });\n                                break;\n                            }\n                        case \"step-start\":\n                            {\n                                const { type: type2, ...value } = chunk;\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.StartStep,\n                                    value\n                                });\n                                break;\n                            }\n                        case \"step-finish\":\n                            {\n                                const { type: type2, ...value } = chunk;\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.FinishStep,\n                                    value\n                                });\n                                break;\n                            }\n                        case \"message-finish\":\n                            {\n                                const { type: type2, ...value } = chunk;\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.FinishMessage,\n                                    value\n                                });\n                                break;\n                            }\n                        case \"error\":\n                            {\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Error,\n                                    value: chunk.error\n                                });\n                                break;\n                            }\n                        case \"annotations\":\n                            {\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Annotation,\n                                    value: chunk.annotations\n                                });\n                                break;\n                            }\n                        case \"data\":\n                            {\n                                controller.enqueue({\n                                    type: _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Data,\n                                    value: chunk.data\n                                });\n                                break;\n                            }\n                        // TODO ignore for now\n                        // in the future, we should create a handler that waits for text parts to finish before continuing\n                        case \"tool-call-args-text-finish\":\n                        case \"part-finish\":\n                            break;\n                        default:\n                            {\n                                const exhaustiveCheck = type;\n                                throw new Error(`Unsupported chunk type: ${exhaustiveCheck}`);\n                            }\n                    }\n                }\n            });\n            return readable.pipeThrough(new _utils_stream_AssistantMetaTransformStream_js__WEBPACK_IMPORTED_MODULE_2__.AssistantMetaTransformStream()).pipeThrough(transform).pipeThrough(new _serialization_js__WEBPACK_IMPORTED_MODULE_3__.DataStreamChunkEncoder()).pipeThrough(new TextEncoderStream());\n        });\n        this.headers = new Headers({\n            \"Content-Type\": \"text/plain; charset=utf-8\",\n            \"x-vercel-ai-data-stream\": \"v1\"\n        });\n    }\n};\nvar TOOL_CALL_ARGS_CLOSING_CHUNKS = [\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.StartToolCall,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ToolCall,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.TextDelta,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ReasoningDelta,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Source,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Error,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.FinishStep,\n    _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.FinishMessage\n];\nvar DataStreamDecoder = class extends _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_0__.PipeableTransformStream {\n    constructor(){\n        super((readable)=>{\n            const toolCallControllers = /* @__PURE__ */ new Map();\n            let activeToolCallArgsText;\n            const transform = new _utils_stream_AssistantTransformStream_js__WEBPACK_IMPORTED_MODULE_4__.AssistantTransformStream({\n                transform (chunk, controller) {\n                    const { type, value } = chunk;\n                    if (TOOL_CALL_ARGS_CLOSING_CHUNKS.includes(type)) {\n                        activeToolCallArgsText?.close();\n                        activeToolCallArgsText = void 0;\n                    }\n                    switch(type){\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ReasoningDelta:\n                            controller.appendReasoning(value);\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.TextDelta:\n                            controller.appendText(value);\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.StartToolCall:\n                            {\n                                const { toolCallId, toolName } = value;\n                                const toolCallController = controller.addToolCallPart({\n                                    toolCallId,\n                                    toolName\n                                });\n                                toolCallControllers.set(toolCallId, toolCallController);\n                                activeToolCallArgsText = toolCallController.argsText;\n                                break;\n                            }\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ToolCallArgsTextDelta:\n                            {\n                                const { toolCallId, argsTextDelta } = value;\n                                const toolCallController = toolCallControllers.get(toolCallId);\n                                if (!toolCallController) throw new Error(\"Encountered tool call with unknown id: \" + toolCallId);\n                                toolCallController.argsText.append(argsTextDelta);\n                                break;\n                            }\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ToolCallResult:\n                            {\n                                const { toolCallId, artifact, result, isError } = value;\n                                const toolCallController = toolCallControllers.get(toolCallId);\n                                if (!toolCallController) throw new Error(\"Encountered tool call result with unknown id: \" + toolCallId);\n                                toolCallController.setResponse({\n                                    artifact,\n                                    result,\n                                    isError\n                                });\n                                break;\n                            }\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ToolCall:\n                            {\n                                const { toolCallId, toolName, args } = value;\n                                let toolCallController = toolCallControllers.get(toolCallId);\n                                if (toolCallController) {\n                                    toolCallController.argsText.close();\n                                } else {\n                                    toolCallController = controller.addToolCallPart({\n                                        toolCallId,\n                                        toolName,\n                                        args\n                                    });\n                                    toolCallControllers.set(toolCallId, toolCallController);\n                                }\n                                break;\n                            }\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.FinishMessage:\n                            controller.enqueue({\n                                type: \"message-finish\",\n                                path: [],\n                                ...value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.StartStep:\n                            controller.enqueue({\n                                type: \"step-start\",\n                                path: [],\n                                ...value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.FinishStep:\n                            controller.enqueue({\n                                type: \"step-finish\",\n                                path: [],\n                                ...value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Data:\n                            controller.enqueue({\n                                type: \"data\",\n                                path: [],\n                                data: value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Annotation:\n                            controller.enqueue({\n                                type: \"annotations\",\n                                path: [],\n                                annotations: value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Source:\n                            controller.appendSource({\n                                type: \"source\",\n                                ...value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.Error:\n                            controller.enqueue({\n                                type: \"error\",\n                                path: [],\n                                error: value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.File:\n                            controller.appendFile({\n                                type: \"file\",\n                                ...value\n                            });\n                            break;\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.ReasoningSignature:\n                        case _chunk_types_js__WEBPACK_IMPORTED_MODULE_1__.DataStreamStreamChunkType.RedactedReasoning:\n                            break;\n                        default:\n                            {\n                                const exhaustiveCheck = type;\n                                throw new Error(`unsupported chunk type: ${exhaustiveCheck}`);\n                            }\n                    }\n                },\n                flush () {\n                    activeToolCallArgsText?.close();\n                    activeToolCallArgsText = void 0;\n                    toolCallControllers.forEach((controller)=>controller.close());\n                    toolCallControllers.clear();\n                }\n            });\n            return readable.pipeThrough(new TextDecoderStream()).pipeThrough(new _utils_stream_LineDecoderStream_js__WEBPACK_IMPORTED_MODULE_5__.LineDecoderStream()).pipeThrough(new _serialization_js__WEBPACK_IMPORTED_MODULE_3__.DataStreamChunkDecoder()).pipeThrough(transform);\n        });\n    }\n};\n //# sourceMappingURL=DataStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/DataStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/chunk-types.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/serialization/data-stream/chunk-types.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataStreamStreamChunkType: () => (/* binding */ DataStreamStreamChunkType)\n/* harmony export */ });\n// src/core/serialization/data-stream/chunk-types.ts\nvar DataStreamStreamChunkType = /* @__PURE__ */ ((DataStreamStreamChunkType2)=>{\n    DataStreamStreamChunkType2[\"TextDelta\"] = \"0\";\n    DataStreamStreamChunkType2[\"Data\"] = \"2\";\n    DataStreamStreamChunkType2[\"Error\"] = \"3\";\n    DataStreamStreamChunkType2[\"Annotation\"] = \"8\";\n    DataStreamStreamChunkType2[\"ToolCall\"] = \"9\";\n    DataStreamStreamChunkType2[\"ToolCallResult\"] = \"a\";\n    DataStreamStreamChunkType2[\"StartToolCall\"] = \"b\";\n    DataStreamStreamChunkType2[\"ToolCallArgsTextDelta\"] = \"c\";\n    DataStreamStreamChunkType2[\"FinishMessage\"] = \"d\";\n    DataStreamStreamChunkType2[\"FinishStep\"] = \"e\";\n    DataStreamStreamChunkType2[\"StartStep\"] = \"f\";\n    DataStreamStreamChunkType2[\"ReasoningDelta\"] = \"g\";\n    DataStreamStreamChunkType2[\"Source\"] = \"h\";\n    DataStreamStreamChunkType2[\"RedactedReasoning\"] = \"i\";\n    DataStreamStreamChunkType2[\"ReasoningSignature\"] = \"j\";\n    DataStreamStreamChunkType2[\"File\"] = \"k\";\n    return DataStreamStreamChunkType2;\n})(DataStreamStreamChunkType || {});\n //# sourceMappingURL=chunk-types.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvc2VyaWFsaXphdGlvbi9kYXRhLXN0cmVhbS9jaHVuay10eXBlcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsb0RBQW9EO0FBQ3BELElBQUlBLDRCQUE0QyxhQUFILEdBQUksRUFBQ0M7SUFDaERBLDBCQUEwQixDQUFDLFlBQVksR0FBRztJQUMxQ0EsMEJBQTBCLENBQUMsT0FBTyxHQUFHO0lBQ3JDQSwwQkFBMEIsQ0FBQyxRQUFRLEdBQUc7SUFDdENBLDBCQUEwQixDQUFDLGFBQWEsR0FBRztJQUMzQ0EsMEJBQTBCLENBQUMsV0FBVyxHQUFHO0lBQ3pDQSwwQkFBMEIsQ0FBQyxpQkFBaUIsR0FBRztJQUMvQ0EsMEJBQTBCLENBQUMsZ0JBQWdCLEdBQUc7SUFDOUNBLDBCQUEwQixDQUFDLHdCQUF3QixHQUFHO0lBQ3REQSwwQkFBMEIsQ0FBQyxnQkFBZ0IsR0FBRztJQUM5Q0EsMEJBQTBCLENBQUMsYUFBYSxHQUFHO0lBQzNDQSwwQkFBMEIsQ0FBQyxZQUFZLEdBQUc7SUFDMUNBLDBCQUEwQixDQUFDLGlCQUFpQixHQUFHO0lBQy9DQSwwQkFBMEIsQ0FBQyxTQUFTLEdBQUc7SUFDdkNBLDBCQUEwQixDQUFDLG9CQUFvQixHQUFHO0lBQ2xEQSwwQkFBMEIsQ0FBQyxxQkFBcUIsR0FBRztJQUNuREEsMEJBQTBCLENBQUMsT0FBTyxHQUFHO0lBQ3JDLE9BQU9BO0FBQ1QsR0FBR0QsNkJBQTZCLENBQUM7QUFHL0IsQ0FDRix1Q0FBdUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL2Fzc2lzdGFudC1zdHJlYW0vZGlzdC9jb3JlL3NlcmlhbGl6YXRpb24vZGF0YS1zdHJlYW0vY2h1bmstdHlwZXMuanM/NTNjYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvY29yZS9zZXJpYWxpemF0aW9uL2RhdGEtc3RyZWFtL2NodW5rLXR5cGVzLnRzXG52YXIgRGF0YVN0cmVhbVN0cmVhbUNodW5rVHlwZSA9IC8qIEBfX1BVUkVfXyAqLyAoKERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyKSA9PiB7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiVGV4dERlbHRhXCJdID0gXCIwXCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiRGF0YVwiXSA9IFwiMlwiO1xuICBEYXRhU3RyZWFtU3RyZWFtQ2h1bmtUeXBlMltcIkVycm9yXCJdID0gXCIzXCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiQW5ub3RhdGlvblwiXSA9IFwiOFwiO1xuICBEYXRhU3RyZWFtU3RyZWFtQ2h1bmtUeXBlMltcIlRvb2xDYWxsXCJdID0gXCI5XCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiVG9vbENhbGxSZXN1bHRcIl0gPSBcImFcIjtcbiAgRGF0YVN0cmVhbVN0cmVhbUNodW5rVHlwZTJbXCJTdGFydFRvb2xDYWxsXCJdID0gXCJiXCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiVG9vbENhbGxBcmdzVGV4dERlbHRhXCJdID0gXCJjXCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiRmluaXNoTWVzc2FnZVwiXSA9IFwiZFwiO1xuICBEYXRhU3RyZWFtU3RyZWFtQ2h1bmtUeXBlMltcIkZpbmlzaFN0ZXBcIl0gPSBcImVcIjtcbiAgRGF0YVN0cmVhbVN0cmVhbUNodW5rVHlwZTJbXCJTdGFydFN0ZXBcIl0gPSBcImZcIjtcbiAgRGF0YVN0cmVhbVN0cmVhbUNodW5rVHlwZTJbXCJSZWFzb25pbmdEZWx0YVwiXSA9IFwiZ1wiO1xuICBEYXRhU3RyZWFtU3RyZWFtQ2h1bmtUeXBlMltcIlNvdXJjZVwiXSA9IFwiaFwiO1xuICBEYXRhU3RyZWFtU3RyZWFtQ2h1bmtUeXBlMltcIlJlZGFjdGVkUmVhc29uaW5nXCJdID0gXCJpXCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiUmVhc29uaW5nU2lnbmF0dXJlXCJdID0gXCJqXCI7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyW1wiRmlsZVwiXSA9IFwia1wiO1xuICByZXR1cm4gRGF0YVN0cmVhbVN0cmVhbUNodW5rVHlwZTI7XG59KShEYXRhU3RyZWFtU3RyZWFtQ2h1bmtUeXBlIHx8IHt9KTtcbmV4cG9ydCB7XG4gIERhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaHVuay10eXBlcy5qcy5tYXAiXSwibmFtZXMiOlsiRGF0YVN0cmVhbVN0cmVhbUNodW5rVHlwZSIsIkRhdGFTdHJlYW1TdHJlYW1DaHVua1R5cGUyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/chunk-types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/serialization.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/serialization/data-stream/serialization.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataStreamChunkDecoder: () => (/* binding */ DataStreamChunkDecoder),\n/* harmony export */   DataStreamChunkEncoder: () => (/* binding */ DataStreamChunkEncoder)\n/* harmony export */ });\n// src/core/serialization/data-stream/serialization.ts\nvar DataStreamChunkEncoder = class extends TransformStream {\n    constructor(){\n        super({\n            transform: (chunk, controller)=>{\n                controller.enqueue(`${chunk.type}:${JSON.stringify(chunk.value)}\n`);\n            }\n        });\n    }\n};\nvar DataStreamChunkDecoder = class extends TransformStream {\n    constructor(){\n        super({\n            transform: (chunk, controller)=>{\n                const index = chunk.indexOf(\":\");\n                if (index === -1) throw new Error(\"Invalid stream part\");\n                controller.enqueue({\n                    type: chunk.slice(0, index),\n                    value: JSON.parse(chunk.slice(index + 1))\n                });\n            }\n        });\n    }\n};\n //# sourceMappingURL=serialization.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvc2VyaWFsaXphdGlvbi9kYXRhLXN0cmVhbS9zZXJpYWxpemF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsc0RBQXNEO0FBQ3RELElBQUlBLHlCQUF5QixjQUFjQztJQUN6Q0MsYUFBYztRQUNaLEtBQUssQ0FBQztZQUNKQyxXQUFXLENBQUNDLE9BQU9DO2dCQUNqQkEsV0FBV0MsT0FBTyxDQUFDLENBQUMsRUFBRUYsTUFBTUcsSUFBSSxDQUFDLENBQUMsRUFBRUMsS0FBS0MsU0FBUyxDQUFDTCxNQUFNTSxLQUFLLEVBQUU7QUFDeEUsQ0FBQztZQUNLO1FBQ0Y7SUFDRjtBQUNGO0FBQ0EsSUFBSUMseUJBQXlCLGNBQWNWO0lBQ3pDQyxhQUFjO1FBQ1osS0FBSyxDQUFDO1lBQ0pDLFdBQVcsQ0FBQ0MsT0FBT0M7Z0JBQ2pCLE1BQU1PLFFBQVFSLE1BQU1TLE9BQU8sQ0FBQztnQkFDNUIsSUFBSUQsVUFBVSxDQUFDLEdBQUcsTUFBTSxJQUFJRSxNQUFNO2dCQUNsQ1QsV0FBV0MsT0FBTyxDQUFDO29CQUNqQkMsTUFBTUgsTUFBTVcsS0FBSyxDQUFDLEdBQUdIO29CQUNyQkYsT0FBT0YsS0FBS1EsS0FBSyxDQUFDWixNQUFNVyxLQUFLLENBQUNILFFBQVE7Z0JBQ3hDO1lBQ0Y7UUFDRjtJQUNGO0FBQ0Y7QUFJRSxDQUNGLHlDQUF5QyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvc2VyaWFsaXphdGlvbi9kYXRhLXN0cmVhbS9zZXJpYWxpemF0aW9uLmpzPzQ0MTIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvcmUvc2VyaWFsaXphdGlvbi9kYXRhLXN0cmVhbS9zZXJpYWxpemF0aW9uLnRzXG52YXIgRGF0YVN0cmVhbUNodW5rRW5jb2RlciA9IGNsYXNzIGV4dGVuZHMgVHJhbnNmb3JtU3RyZWFtIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoe1xuICAgICAgdHJhbnNmb3JtOiAoY2h1bmssIGNvbnRyb2xsZXIpID0+IHtcbiAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKGAke2NodW5rLnR5cGV9OiR7SlNPTi5zdHJpbmdpZnkoY2h1bmsudmFsdWUpfVxuYCk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cbn07XG52YXIgRGF0YVN0cmVhbUNodW5rRGVjb2RlciA9IGNsYXNzIGV4dGVuZHMgVHJhbnNmb3JtU3RyZWFtIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoe1xuICAgICAgdHJhbnNmb3JtOiAoY2h1bmssIGNvbnRyb2xsZXIpID0+IHtcbiAgICAgICAgY29uc3QgaW5kZXggPSBjaHVuay5pbmRleE9mKFwiOlwiKTtcbiAgICAgICAgaWYgKGluZGV4ID09PSAtMSkgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBzdHJlYW0gcGFydFwiKTtcbiAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKHtcbiAgICAgICAgICB0eXBlOiBjaHVuay5zbGljZSgwLCBpbmRleCksXG4gICAgICAgICAgdmFsdWU6IEpTT04ucGFyc2UoY2h1bmsuc2xpY2UoaW5kZXggKyAxKSlcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cbn07XG5leHBvcnQge1xuICBEYXRhU3RyZWFtQ2h1bmtEZWNvZGVyLFxuICBEYXRhU3RyZWFtQ2h1bmtFbmNvZGVyXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2VyaWFsaXphdGlvbi5qcy5tYXAiXSwibmFtZXMiOlsiRGF0YVN0cmVhbUNodW5rRW5jb2RlciIsIlRyYW5zZm9ybVN0cmVhbSIsImNvbnN0cnVjdG9yIiwidHJhbnNmb3JtIiwiY2h1bmsiLCJjb250cm9sbGVyIiwiZW5xdWV1ZSIsInR5cGUiLCJKU09OIiwic3RyaW5naWZ5IiwidmFsdWUiLCJEYXRhU3RyZWFtQ2h1bmtEZWNvZGVyIiwiaW5kZXgiLCJpbmRleE9mIiwiRXJyb3IiLCJzbGljZSIsInBhcnNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/serialization/data-stream/serialization.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolCallReader.js":
/*!************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/tool/ToolCallReader.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolCallArgsReaderImpl: () => (/* binding */ ToolCallArgsReaderImpl),\n/* harmony export */   ToolCallReaderImpl: () => (/* binding */ ToolCallReaderImpl),\n/* harmony export */   ToolCallResponseReaderImpl: () => (/* binding */ ToolCallResponseReaderImpl)\n/* harmony export */ });\n/* harmony import */ var _utils_promiseWithResolvers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/promiseWithResolvers.js */ \"(ssr)/./node_modules/assistant-stream/dist/utils/promiseWithResolvers.js\");\n/* harmony import */ var _utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/json/parse-partial-json-object.js */ \"(ssr)/./node_modules/assistant-stream/dist/utils/json/parse-partial-json-object.js\");\n// src/core/tool/ToolCallReader.ts\n\n\nfunction getField(obj, fieldPath) {\n    let current = obj;\n    for (const key of fieldPath){\n        if (current === void 0 || current === null) {\n            return void 0;\n        }\n        current = current[key];\n    }\n    return current;\n}\nvar GetHandle = class {\n    constructor(resolve, reject, fieldPath){\n        this.disposed = false;\n        this.resolve = resolve;\n        this.reject = reject;\n        this.fieldPath = fieldPath;\n    }\n    update(args) {\n        if (this.disposed) return;\n        try {\n            if ((0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.getPartialJsonObjectFieldState)(args, this.fieldPath) === \"complete\") {\n                const value = getField(args, this.fieldPath);\n                if (value !== void 0) {\n                    this.resolve(value);\n                    this.dispose();\n                }\n            }\n        } catch (e) {\n            this.reject(e);\n            this.dispose();\n        }\n    }\n    dispose() {\n        this.disposed = true;\n    }\n};\nvar StreamValuesHandle = class {\n    constructor(controller, fieldPath){\n        this.disposed = false;\n        this.controller = controller;\n        this.fieldPath = fieldPath;\n    }\n    update(args) {\n        if (this.disposed) return;\n        try {\n            const value = getField(args, this.fieldPath);\n            if (value !== void 0) {\n                this.controller.enqueue(value);\n            }\n            if ((0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.getPartialJsonObjectFieldState)(args, this.fieldPath) === \"complete\") {\n                this.controller.close();\n                this.dispose();\n            }\n        } catch (e) {\n            this.controller.error(e);\n            this.dispose();\n        }\n    }\n    dispose() {\n        this.disposed = true;\n    }\n};\nvar StreamTextHandle = class {\n    constructor(controller, fieldPath){\n        this.disposed = false;\n        this.lastValue = void 0;\n        this.controller = controller;\n        this.fieldPath = fieldPath;\n    }\n    update(args) {\n        if (this.disposed) return;\n        try {\n            const value = getField(args, this.fieldPath);\n            if (value !== void 0 && typeof value === \"string\") {\n                const delta = value.substring(this.lastValue?.length || 0);\n                this.lastValue = value;\n                this.controller.enqueue(delta);\n            }\n            if ((0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.getPartialJsonObjectFieldState)(args, this.fieldPath) === \"complete\") {\n                this.controller.close();\n                this.dispose();\n            }\n        } catch (e) {\n            this.controller.error(e);\n            this.dispose();\n        }\n    }\n    dispose() {\n        this.disposed = true;\n    }\n};\nvar ForEachHandle = class {\n    constructor(controller, fieldPath){\n        this.disposed = false;\n        this.processedIndexes = /* @__PURE__ */ new Set();\n        this.controller = controller;\n        this.fieldPath = fieldPath;\n    }\n    update(args) {\n        if (this.disposed) return;\n        try {\n            const array = getField(args, this.fieldPath);\n            if (!Array.isArray(array)) {\n                return;\n            }\n            for(let i = 0; i < array.length; i++){\n                if (!this.processedIndexes.has(i)) {\n                    const elementPath = [\n                        ...this.fieldPath,\n                        i\n                    ];\n                    if ((0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.getPartialJsonObjectFieldState)(args, elementPath) === \"complete\") {\n                        this.controller.enqueue(array[i]);\n                        this.processedIndexes.add(i);\n                    }\n                }\n            }\n            if ((0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.getPartialJsonObjectFieldState)(args, this.fieldPath) === \"complete\") {\n                this.controller.close();\n                this.dispose();\n            }\n        } catch (e) {\n            this.controller.error(e);\n            this.dispose();\n        }\n    }\n    dispose() {\n        this.disposed = true;\n    }\n};\nvar ToolCallArgsReaderImpl = class {\n    constructor(argTextDeltas){\n        this.handles = /* @__PURE__ */ new Set();\n        this.args = (0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.parsePartialJsonObject)(\"\");\n        this.argTextDeltas = argTextDeltas;\n        this.processStream();\n    }\n    async processStream() {\n        try {\n            let accumulatedText = \"\";\n            const reader = this.argTextDeltas.getReader();\n            while(true){\n                const { value, done } = await reader.read();\n                if (done) break;\n                accumulatedText += value;\n                const parsedArgs = (0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.parsePartialJsonObject)(accumulatedText);\n                if (parsedArgs !== void 0) {\n                    this.args = parsedArgs;\n                    for (const handle of this.handles){\n                        handle.update(parsedArgs);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error processing argument stream:\", error);\n            for (const handle of this.handles){\n                handle.dispose();\n            }\n        }\n    }\n    get(...fieldPath) {\n        return new Promise((resolve, reject)=>{\n            const handle = new GetHandle(resolve, reject, fieldPath);\n            if (this.args && (0,_utils_json_parse_partial_json_object_js__WEBPACK_IMPORTED_MODULE_0__.getPartialJsonObjectFieldState)(this.args, fieldPath) === \"complete\") {\n                const value = getField(this.args, fieldPath);\n                if (value !== void 0) {\n                    resolve(value);\n                    return;\n                }\n            }\n            this.handles.add(handle);\n            handle.update(this.args);\n        });\n    }\n    streamValues(...fieldPath) {\n        const simplePath = fieldPath;\n        const stream = new ReadableStream({\n            start: (controller)=>{\n                const handle = new StreamValuesHandle(controller, simplePath);\n                this.handles.add(handle);\n                handle.update(this.args);\n            },\n            cancel: ()=>{\n                for (const handle of this.handles){\n                    if (handle instanceof StreamValuesHandle) {\n                        handle.dispose();\n                        this.handles.delete(handle);\n                        break;\n                    }\n                }\n            }\n        });\n        return stream;\n    }\n    streamText(...fieldPath) {\n        const simplePath = fieldPath;\n        const stream = new ReadableStream({\n            start: (controller)=>{\n                const handle = new StreamTextHandle(controller, simplePath);\n                this.handles.add(handle);\n                handle.update(this.args);\n            },\n            cancel: ()=>{\n                for (const handle of this.handles){\n                    if (handle instanceof StreamTextHandle) {\n                        handle.dispose();\n                        this.handles.delete(handle);\n                        break;\n                    }\n                }\n            }\n        });\n        return stream;\n    }\n    forEach(...fieldPath) {\n        const simplePath = fieldPath;\n        const stream = new ReadableStream({\n            start: (controller)=>{\n                const handle = new ForEachHandle(controller, simplePath);\n                this.handles.add(handle);\n                handle.update(this.args);\n            },\n            cancel: ()=>{\n                for (const handle of this.handles){\n                    if (handle instanceof ForEachHandle) {\n                        handle.dispose();\n                        this.handles.delete(handle);\n                        break;\n                    }\n                }\n            }\n        });\n        return stream;\n    }\n};\nvar ToolCallResponseReaderImpl = class {\n    constructor(promise){\n        this.promise = promise;\n    }\n    get() {\n        return this.promise;\n    }\n};\nvar ToolCallReaderImpl = class {\n    constructor(){\n        this.argsText = \"\";\n        this.result = {\n            get: async ()=>{\n                const response = await this.response.get();\n                return response.result;\n            }\n        };\n        const stream = new TransformStream();\n        this.writable = stream.writable;\n        this.args = new ToolCallArgsReaderImpl(stream.readable);\n        const { promise, resolve } = (0,_utils_promiseWithResolvers_js__WEBPACK_IMPORTED_MODULE_1__.promiseWithResolvers)();\n        this.resolve = resolve;\n        this.response = new ToolCallResponseReaderImpl(promise);\n    }\n    async appendArgsTextDelta(text) {\n        const writer = this.writable.getWriter();\n        try {\n            await writer.write(text);\n        } catch (err) {\n            console.warn(err);\n        } finally{\n            writer.releaseLock();\n        }\n        this.argsText += text;\n    }\n    setResponse(value) {\n        this.resolve(value);\n    }\n};\n //# sourceMappingURL=ToolCallReader.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolCallReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolExecutionStream.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/tool/ToolExecutionStream.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolExecutionStream: () => (/* binding */ ToolExecutionStream)\n/* harmony export */ });\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/assistant-stream/node_modules/secure-json-parse/index.js\");\n/* harmony import */ var _utils_stream_AssistantMetaTransformStream_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/stream/AssistantMetaTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantMetaTransformStream.js\");\n/* harmony import */ var _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/stream/PipeableTransformStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/PipeableTransformStream.js\");\n/* harmony import */ var _ToolResponse_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToolResponse.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolResponse.js\");\n/* harmony import */ var _utils_withPromiseOrValue_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/withPromiseOrValue.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/withPromiseOrValue.js\");\n/* harmony import */ var _ToolCallReader_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ToolCallReader.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolCallReader.js\");\n// src/core/tool/ToolExecutionStream.ts\n\n\n\n\n\n\nvar ToolExecutionStream = class extends _utils_stream_PipeableTransformStream_js__WEBPACK_IMPORTED_MODULE_1__.PipeableTransformStream {\n    constructor(options){\n        const toolCallPromises = /* @__PURE__ */ new Map();\n        const toolCallControllers = /* @__PURE__ */ new Map();\n        super((readable)=>{\n            const transform = new TransformStream({\n                transform (chunk, controller) {\n                    if (chunk.type !== \"part-finish\" || chunk.meta.type !== \"tool-call\") {\n                        controller.enqueue(chunk);\n                    }\n                    const type = chunk.type;\n                    switch(type){\n                        case \"part-start\":\n                            if (chunk.part.type === \"tool-call\") {\n                                const reader = new _ToolCallReader_js__WEBPACK_IMPORTED_MODULE_2__.ToolCallReaderImpl();\n                                toolCallControllers.set(chunk.part.toolCallId, reader);\n                                options.streamCall({\n                                    reader,\n                                    toolCallId: chunk.part.toolCallId,\n                                    toolName: chunk.part.toolName\n                                });\n                            }\n                            break;\n                        case \"text-delta\":\n                            {\n                                if (chunk.meta.type === \"tool-call\") {\n                                    const toolCallId = chunk.meta.toolCallId;\n                                    const controller2 = toolCallControllers.get(toolCallId);\n                                    if (!controller2) throw new Error(\"No controller found for tool call\");\n                                    controller2.appendArgsTextDelta(chunk.textDelta);\n                                }\n                                break;\n                            }\n                        case \"result\":\n                            {\n                                if (chunk.meta.type !== \"tool-call\") break;\n                                const { toolCallId } = chunk.meta;\n                                const controller2 = toolCallControllers.get(toolCallId);\n                                if (!controller2) throw new Error(\"No controller found for tool call\");\n                                controller2.setResponse(new _ToolResponse_js__WEBPACK_IMPORTED_MODULE_3__.ToolResponse({\n                                    result: chunk.result,\n                                    artifact: chunk.artifact,\n                                    isError: chunk.isError\n                                }));\n                                break;\n                            }\n                        case \"tool-call-args-text-finish\":\n                            {\n                                if (chunk.meta.type !== \"tool-call\") break;\n                                const { toolCallId, toolName } = chunk.meta;\n                                const streamController = toolCallControllers.get(toolCallId);\n                                if (!streamController) throw new Error(\"No controller found for tool call\");\n                                const promise = (0,_utils_withPromiseOrValue_js__WEBPACK_IMPORTED_MODULE_4__.withPromiseOrValue)(()=>{\n                                    if (!streamController.argsText) {\n                                        console.log(\"Encountered tool call without args, this should never happen\");\n                                        throw new Error(\"Encountered tool call without args, this is unexpected.\");\n                                    }\n                                    let args;\n                                    try {\n                                        args = secure_json_parse__WEBPACK_IMPORTED_MODULE_0__.parse(streamController.argsText);\n                                    } catch (e) {\n                                        throw new Error(`Function parameter parsing failed. ${JSON.stringify(e.message)}`);\n                                    }\n                                    return options.execute({\n                                        toolCallId,\n                                        toolName,\n                                        args\n                                    });\n                                }, (c)=>{\n                                    if (c === void 0) return;\n                                    const result = new _ToolResponse_js__WEBPACK_IMPORTED_MODULE_3__.ToolResponse({\n                                        artifact: c.artifact,\n                                        result: c.result,\n                                        isError: c.isError\n                                    });\n                                    streamController.setResponse(result);\n                                    controller.enqueue({\n                                        type: \"result\",\n                                        path: chunk.path,\n                                        ...result\n                                    });\n                                }, (e)=>{\n                                    const result = new _ToolResponse_js__WEBPACK_IMPORTED_MODULE_3__.ToolResponse({\n                                        result: String(e),\n                                        isError: true\n                                    });\n                                    streamController.setResponse(result);\n                                    controller.enqueue({\n                                        type: \"result\",\n                                        path: chunk.path,\n                                        ...result\n                                    });\n                                });\n                                if (promise) {\n                                    toolCallPromises.set(toolCallId, promise);\n                                }\n                                break;\n                            }\n                        case \"part-finish\":\n                            {\n                                if (chunk.meta.type !== \"tool-call\") break;\n                                const { toolCallId } = chunk.meta;\n                                const toolCallPromise = toolCallPromises.get(toolCallId);\n                                if (toolCallPromise) {\n                                    toolCallPromise.then(()=>{\n                                        toolCallPromises.delete(toolCallId);\n                                        toolCallControllers.delete(toolCallId);\n                                        controller.enqueue(chunk);\n                                    });\n                                } else {\n                                    controller.enqueue(chunk);\n                                }\n                            }\n                    }\n                },\n                async flush () {\n                    await Promise.all(toolCallPromises.values());\n                }\n            });\n            return readable.pipeThrough(new _utils_stream_AssistantMetaTransformStream_js__WEBPACK_IMPORTED_MODULE_5__.AssistantMetaTransformStream()).pipeThrough(transform);\n        });\n    }\n};\n //# sourceMappingURL=ToolExecutionStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolExecutionStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolResponse.js":
/*!**********************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/tool/ToolResponse.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolResponse: () => (/* binding */ ToolResponse)\n/* harmony export */ });\n// src/core/tool/ToolResponse.ts\nvar TOOL_RESPONSE_SYMBOL = Symbol.for(\"aui.tool-response\");\nvar ToolResponse = class {\n    get [TOOL_RESPONSE_SYMBOL]() {\n        return true;\n    }\n    constructor(options){\n        this.artifact = options.artifact;\n        this.result = options.result;\n        this.isError = options.isError ?? false;\n    }\n    static [Symbol.hasInstance](obj) {\n        return typeof obj === \"object\" && obj !== null && TOOL_RESPONSE_SYMBOL in obj;\n    }\n};\n //# sourceMappingURL=ToolResponse.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvdG9vbC9Ub29sUmVzcG9uc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGdDQUFnQztBQUNoQyxJQUFJQSx1QkFBdUJDLE9BQU9DLEdBQUcsQ0FBQztBQUN0QyxJQUFJQyxlQUFlO0lBQ2pCLElBQUksQ0FBQ0gscUJBQXFCLEdBQUc7UUFDM0IsT0FBTztJQUNUO0lBSUFJLFlBQVlDLE9BQU8sQ0FBRTtRQUNuQixJQUFJLENBQUNDLFFBQVEsR0FBR0QsUUFBUUMsUUFBUTtRQUNoQyxJQUFJLENBQUNDLE1BQU0sR0FBR0YsUUFBUUUsTUFBTTtRQUM1QixJQUFJLENBQUNDLE9BQU8sR0FBR0gsUUFBUUcsT0FBTyxJQUFJO0lBQ3BDO0lBQ0EsT0FBTyxDQUFDUCxPQUFPUSxXQUFXLENBQUMsQ0FBQ0MsR0FBRyxFQUFFO1FBQy9CLE9BQU8sT0FBT0EsUUFBUSxZQUFZQSxRQUFRLFFBQVFWLHdCQUF3QlU7SUFDNUU7QUFDRjtBQUdFLENBQ0Ysd0NBQXdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9hc3Npc3RhbnQtc3RyZWFtL2Rpc3QvY29yZS90b29sL1Rvb2xSZXNwb25zZS5qcz8yYWUxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb3JlL3Rvb2wvVG9vbFJlc3BvbnNlLnRzXG52YXIgVE9PTF9SRVNQT05TRV9TWU1CT0wgPSBTeW1ib2wuZm9yKFwiYXVpLnRvb2wtcmVzcG9uc2VcIik7XG52YXIgVG9vbFJlc3BvbnNlID0gY2xhc3Mge1xuICBnZXQgW1RPT0xfUkVTUE9OU0VfU1lNQk9MXSgpIHtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfVxuICBhcnRpZmFjdDtcbiAgcmVzdWx0O1xuICBpc0Vycm9yO1xuICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgdGhpcy5hcnRpZmFjdCA9IG9wdGlvbnMuYXJ0aWZhY3Q7XG4gICAgdGhpcy5yZXN1bHQgPSBvcHRpb25zLnJlc3VsdDtcbiAgICB0aGlzLmlzRXJyb3IgPSBvcHRpb25zLmlzRXJyb3IgPz8gZmFsc2U7XG4gIH1cbiAgc3RhdGljIFtTeW1ib2wuaGFzSW5zdGFuY2VdKG9iaikge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSBcIm9iamVjdFwiICYmIG9iaiAhPT0gbnVsbCAmJiBUT09MX1JFU1BPTlNFX1NZTUJPTCBpbiBvYmo7XG4gIH1cbn07XG5leHBvcnQge1xuICBUb29sUmVzcG9uc2Vcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Ub29sUmVzcG9uc2UuanMubWFwIl0sIm5hbWVzIjpbIlRPT0xfUkVTUE9OU0VfU1lNQk9MIiwiU3ltYm9sIiwiZm9yIiwiVG9vbFJlc3BvbnNlIiwiY29uc3RydWN0b3IiLCJvcHRpb25zIiwiYXJ0aWZhY3QiLCJyZXN1bHQiLCJpc0Vycm9yIiwiaGFzSW5zdGFuY2UiLCJvYmoiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolResponse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/tool/toolResultStream.js":
/*!**************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/tool/toolResultStream.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toolResultStream: () => (/* binding */ toolResultStream),\n/* harmony export */   unstable_runPendingTools: () => (/* binding */ unstable_runPendingTools)\n/* harmony export */ });\n/* harmony import */ var _ToolResponse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ToolResponse.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolResponse.js\");\n/* harmony import */ var _ToolExecutionStream_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ToolExecutionStream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/tool/ToolExecutionStream.js\");\n// src/core/tool/toolResultStream.ts\n\n\nvar isStandardSchemaV1 = (schema)=>{\n    return typeof schema === \"object\" && schema !== null && \"~standard\" in schema && schema[\"~standard\"].version === 1;\n};\nfunction getToolResponse(tools, abortSignal, toolCall) {\n    const tool = tools?.[toolCall.toolName];\n    if (!tool || !tool.execute) return void 0;\n    const getResult = async (toolExecute)=>{\n        let executeFn = toolExecute;\n        if (isStandardSchemaV1(tool.parameters)) {\n            let result2 = tool.parameters[\"~standard\"].validate(toolCall.args);\n            if (result2 instanceof Promise) result2 = await result2;\n            if (result2.issues) {\n                executeFn = tool.experimental_onSchemaValidationError ?? (()=>{\n                    throw new Error(`Function parameter validation failed. ${JSON.stringify(result2.issues)}`);\n                });\n            }\n        }\n        const result = await executeFn(toolCall.args, {\n            toolCallId: toolCall.toolCallId,\n            abortSignal\n        });\n        if (result instanceof _ToolResponse_js__WEBPACK_IMPORTED_MODULE_0__.ToolResponse) return result;\n        return new _ToolResponse_js__WEBPACK_IMPORTED_MODULE_0__.ToolResponse({\n            result: result === void 0 ? \"<no result>\" : result\n        });\n    };\n    return getResult(tool.execute);\n}\nfunction getToolStreamResponse(tools, abortSignal, reader, context) {\n    tools?.[context.toolName]?.streamCall?.(reader, {\n        toolCallId: context.toolCallId,\n        abortSignal\n    });\n}\nasync function unstable_runPendingTools(message, tools, abortSignal) {\n    for (const part of message.parts){\n        if (part.type === \"tool-call\") {\n            const promiseOrUndefined = getToolResponse(tools, abortSignal, part);\n            if (promiseOrUndefined) {\n                const result = await promiseOrUndefined;\n                const updatedParts = message.parts.map((p)=>{\n                    if (p.type === \"tool-call\" && p.toolCallId === part.toolCallId) {\n                        return {\n                            ...p,\n                            state: \"result\",\n                            artifact: result.artifact,\n                            result: result.result,\n                            isError: result.isError\n                        };\n                    }\n                    return p;\n                });\n                message = {\n                    ...message,\n                    parts: updatedParts,\n                    content: updatedParts\n                };\n            }\n        }\n    }\n    return message;\n}\nfunction toolResultStream(tools, abortSignal) {\n    return new _ToolExecutionStream_js__WEBPACK_IMPORTED_MODULE_1__.ToolExecutionStream({\n        execute: (toolCall)=>getToolResponse(tools, abortSignal, toolCall),\n        streamCall: ({ reader, ...context })=>getToolStreamResponse(tools, abortSignal, reader, context)\n    });\n}\n //# sourceMappingURL=toolResultStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/tool/toolResultStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/Counter.js":
/*!******************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/Counter.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Counter: () => (/* binding */ Counter)\n/* harmony export */ });\n// src/core/utils/Counter.ts\nvar Counter = class {\n    up() {\n        return ++this.value;\n    }\n    constructor(){\n        this.value = -1;\n    }\n};\n //# sourceMappingURL=Counter.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvdXRpbHMvQ291bnRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNEJBQTRCO0FBQzVCLElBQUlBLFVBQVU7SUFFWkMsS0FBSztRQUNILE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUs7SUFDckI7O2FBSEFBLFFBQVEsQ0FBQzs7QUFJWDtBQUdFLENBQ0YsbUNBQW1DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9hc3Npc3RhbnQtc3RyZWFtL2Rpc3QvY29yZS91dGlscy9Db3VudGVyLmpzPzlhZDEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvcmUvdXRpbHMvQ291bnRlci50c1xudmFyIENvdW50ZXIgPSBjbGFzcyB7XG4gIHZhbHVlID0gLTE7XG4gIHVwKCkge1xuICAgIHJldHVybiArK3RoaXMudmFsdWU7XG4gIH1cbn07XG5leHBvcnQge1xuICBDb3VudGVyXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Q291bnRlci5qcy5tYXAiXSwibmFtZXMiOlsiQ291bnRlciIsInVwIiwidmFsdWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/Counter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/generateId.js":
/*!*********************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/generateId.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateId: () => (/* binding */ generateId)\n/* harmony export */ });\n/* harmony import */ var nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid/non-secure */ \"(ssr)/./node_modules/assistant-stream/node_modules/nanoid/non-secure/index.js\");\n// src/core/utils/generateId.tsx\n\nvar generateId = (0,nanoid_non_secure__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz\", 7);\n //# sourceMappingURL=generateId.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvdXRpbHMvZ2VuZXJhdGVJZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLGdDQUFnQztBQUNtQjtBQUNuRCxJQUFJQyxhQUFhRCxpRUFBY0EsQ0FDN0Isa0VBQ0E7QUFJQSxDQUNGLHNDQUFzQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvdXRpbHMvZ2VuZXJhdGVJZC5qcz9jOWEzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb3JlL3V0aWxzL2dlbmVyYXRlSWQudHN4XG5pbXBvcnQgeyBjdXN0b21BbHBoYWJldCB9IGZyb20gXCJuYW5vaWQvbm9uLXNlY3VyZVwiO1xudmFyIGdlbmVyYXRlSWQgPSBjdXN0b21BbHBoYWJldChcbiAgXCIwMTIzNDU2Nzg5QUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5elwiLFxuICA3XG4pO1xuZXhwb3J0IHtcbiAgZ2VuZXJhdGVJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdlbmVyYXRlSWQuanMubWFwIl0sIm5hbWVzIjpbImN1c3RvbUFscGhhYmV0IiwiZ2VuZXJhdGVJZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/generateId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantMetaTransformStream.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/stream/AssistantMetaTransformStream.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantMetaTransformStream: () => (/* binding */ AssistantMetaTransformStream)\n/* harmony export */ });\n// src/core/utils/stream/AssistantMetaTransformStream.ts\nvar AssistantMetaTransformStream = class extends TransformStream {\n    constructor(){\n        const parts = [];\n        super({\n            transform (chunk, controller) {\n                if (chunk.type === \"part-start\") {\n                    if (chunk.path.length !== 0) {\n                        controller.error(new Error(\"Nested parts are not supported\"));\n                        return;\n                    }\n                    parts.push(chunk.part);\n                    controller.enqueue(chunk);\n                    return;\n                }\n                if (chunk.type === \"text-delta\" || chunk.type === \"result\" || chunk.type === \"part-finish\" || chunk.type === \"tool-call-args-text-finish\") {\n                    if (chunk.path.length !== 1) {\n                        controller.error(new Error(`${chunk.type} chunks must have a path of length 1`));\n                        return;\n                    }\n                    const idx = chunk.path[0];\n                    if (idx < 0 || idx >= parts.length) {\n                        controller.error(new Error(`Invalid path index: ${idx}`));\n                        return;\n                    }\n                    const part = parts[idx];\n                    controller.enqueue({\n                        ...chunk,\n                        meta: part\n                    });\n                    return;\n                }\n                controller.enqueue(chunk);\n            }\n        });\n    }\n};\n //# sourceMappingURL=AssistantMetaTransformStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantMetaTransformStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantTransformStream.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/stream/AssistantTransformStream.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantTransformStream: () => (/* binding */ AssistantTransformStream)\n/* harmony export */ });\n/* harmony import */ var _modules_assistant_stream_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../modules/assistant-stream.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/modules/assistant-stream.js\");\n// src/core/utils/stream/AssistantTransformStream.ts\n\nvar AssistantTransformStream = class extends TransformStream {\n    constructor(transformer, writableStrategy, readableStrategy){\n        const [stream, runController] = (0,_modules_assistant_stream_js__WEBPACK_IMPORTED_MODULE_0__.createAssistantStreamController)();\n        let runPipeTask;\n        super({\n            start (controller) {\n                runPipeTask = stream.pipeTo(new WritableStream({\n                    write (chunk) {\n                        controller.enqueue(chunk);\n                    },\n                    abort (reason) {\n                        controller.error(reason);\n                    },\n                    close () {\n                        controller.terminate();\n                    }\n                })).catch((error)=>{\n                    controller.error(error);\n                });\n                return transformer.start?.(runController);\n            },\n            transform (chunk) {\n                return transformer.transform?.(chunk, runController);\n            },\n            async flush () {\n                await transformer.flush?.(runController);\n                runController.close();\n                await runPipeTask;\n            }\n        }, writableStrategy, readableStrategy);\n    }\n};\n //# sourceMappingURL=AssistantTransformStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/AssistantTransformStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/LineDecoderStream.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/stream/LineDecoderStream.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LineDecoderStream: () => (/* binding */ LineDecoderStream)\n/* harmony export */ });\n// src/core/utils/stream/LineDecoderStream.ts\nvar LineDecoderStream = class extends TransformStream {\n    constructor(){\n        super({\n            transform: (chunk, controller)=>{\n                this.buffer += chunk;\n                const lines = this.buffer.split(\"\\n\");\n                for(let i = 0; i < lines.length - 1; i++){\n                    controller.enqueue(lines[i]);\n                }\n                this.buffer = lines[lines.length - 1] || \"\";\n            },\n            flush: ()=>{\n                if (this.buffer) {\n                    throw new Error(`Stream ended with an incomplete line: \"${this.buffer}\"`);\n                }\n            }\n        });\n        this.buffer = \"\";\n    }\n};\n //# sourceMappingURL=LineDecoderStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/LineDecoderStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/PipeableTransformStream.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/stream/PipeableTransformStream.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PipeableTransformStream: () => (/* binding */ PipeableTransformStream)\n/* harmony export */ });\n// src/core/utils/stream/PipeableTransformStream.ts\nvar PipeableTransformStream = class extends TransformStream {\n    constructor(transform){\n        super();\n        const readable = transform(super.readable);\n        Object.defineProperty(this, \"readable\", {\n            value: readable,\n            writable: false\n        });\n    }\n};\n //# sourceMappingURL=PipeableTransformStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvdXRpbHMvc3RyZWFtL1BpcGVhYmxlVHJhbnNmb3JtU3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxtREFBbUQ7QUFDbkQsSUFBSUEsMEJBQTBCLGNBQWNDO0lBQzFDQyxZQUFZQyxTQUFTLENBQUU7UUFDckIsS0FBSztRQUNMLE1BQU1DLFdBQVdELFVBQVUsS0FBSyxDQUFDQztRQUNqQ0MsT0FBT0MsY0FBYyxDQUFDLElBQUksRUFBRSxZQUFZO1lBQ3RDQyxPQUFPSDtZQUNQSSxVQUFVO1FBQ1o7SUFDRjtBQUNGO0FBR0UsQ0FDRixtREFBbUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL2Fzc2lzdGFudC1zdHJlYW0vZGlzdC9jb3JlL3V0aWxzL3N0cmVhbS9QaXBlYWJsZVRyYW5zZm9ybVN0cmVhbS5qcz80NmYxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb3JlL3V0aWxzL3N0cmVhbS9QaXBlYWJsZVRyYW5zZm9ybVN0cmVhbS50c1xudmFyIFBpcGVhYmxlVHJhbnNmb3JtU3RyZWFtID0gY2xhc3MgZXh0ZW5kcyBUcmFuc2Zvcm1TdHJlYW0ge1xuICBjb25zdHJ1Y3Rvcih0cmFuc2Zvcm0pIHtcbiAgICBzdXBlcigpO1xuICAgIGNvbnN0IHJlYWRhYmxlID0gdHJhbnNmb3JtKHN1cGVyLnJlYWRhYmxlKTtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJyZWFkYWJsZVwiLCB7XG4gICAgICB2YWx1ZTogcmVhZGFibGUsXG4gICAgICB3cml0YWJsZTogZmFsc2VcbiAgICB9KTtcbiAgfVxufTtcbmV4cG9ydCB7XG4gIFBpcGVhYmxlVHJhbnNmb3JtU3RyZWFtXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UGlwZWFibGVUcmFuc2Zvcm1TdHJlYW0uanMubWFwIl0sIm5hbWVzIjpbIlBpcGVhYmxlVHJhbnNmb3JtU3RyZWFtIiwiVHJhbnNmb3JtU3RyZWFtIiwiY29uc3RydWN0b3IiLCJ0cmFuc2Zvcm0iLCJyZWFkYWJsZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwidmFsdWUiLCJ3cml0YWJsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/PipeableTransformStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/merge.js":
/*!***********************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/stream/merge.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMergeStream: () => (/* binding */ createMergeStream)\n/* harmony export */ });\n/* harmony import */ var _utils_promiseWithResolvers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/promiseWithResolvers.js */ \"(ssr)/./node_modules/assistant-stream/dist/utils/promiseWithResolvers.js\");\n// src/core/utils/stream/merge.ts\n\nvar createMergeStream = ()=>{\n    const list = [];\n    let sealed = false;\n    let controller;\n    let currentPull;\n    const handlePull = (item)=>{\n        if (!item.promise) {\n            item.promise = item.reader.read().then(({ done, value })=>{\n                item.promise = void 0;\n                if (done) {\n                    list.splice(list.indexOf(item), 1);\n                    if (sealed && list.length === 0) {\n                        controller.close();\n                    }\n                } else {\n                    controller.enqueue(value);\n                }\n                currentPull?.resolve();\n                currentPull = void 0;\n            }).catch((e)=>{\n                console.error(e);\n                list.forEach((item2)=>{\n                    item2.reader.cancel();\n                });\n                list.length = 0;\n                controller.error(e);\n                currentPull?.reject(e);\n                currentPull = void 0;\n            });\n        }\n    };\n    const readable = new ReadableStream({\n        start (c) {\n            controller = c;\n        },\n        pull () {\n            currentPull = (0,_utils_promiseWithResolvers_js__WEBPACK_IMPORTED_MODULE_0__.promiseWithResolvers)();\n            list.forEach((item)=>{\n                handlePull(item);\n            });\n            return currentPull.promise;\n        },\n        cancel () {\n            list.forEach((item)=>{\n                item.reader.cancel();\n            });\n            list.length = 0;\n        }\n    });\n    return {\n        readable,\n        isSealed () {\n            return sealed;\n        },\n        seal () {\n            sealed = true;\n            if (list.length === 0) controller.close();\n        },\n        addStream (stream) {\n            if (sealed) throw new Error(\"Cannot add streams after the run callback has settled.\");\n            const item = {\n                reader: stream.getReader()\n            };\n            list.push(item);\n            handlePull(item);\n        },\n        enqueue (chunk) {\n            this.addStream(new ReadableStream({\n                start (c) {\n                    c.enqueue(chunk);\n                    c.close();\n                }\n            }));\n        }\n    };\n};\n //# sourceMappingURL=merge.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/path-utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/stream/path-utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PathAppendDecoder: () => (/* binding */ PathAppendDecoder),\n/* harmony export */   PathAppendEncoder: () => (/* binding */ PathAppendEncoder),\n/* harmony export */   PathMergeEncoder: () => (/* binding */ PathMergeEncoder)\n/* harmony export */ });\n/* harmony import */ var _Counter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../Counter.js */ \"(ssr)/./node_modules/assistant-stream/dist/core/utils/Counter.js\");\n// src/core/utils/stream/path-utils.ts\n\nvar PathAppendEncoder = class extends TransformStream {\n    constructor(idx){\n        super({\n            transform (chunk, controller) {\n                controller.enqueue({\n                    ...chunk,\n                    path: [\n                        idx,\n                        ...chunk.path\n                    ]\n                });\n            }\n        });\n    }\n};\nvar PathAppendDecoder = class extends TransformStream {\n    constructor(idx){\n        super({\n            transform (chunk, controller) {\n                const { path: [idx2, ...path] } = chunk;\n                if (idx !== idx2) throw new Error(`Path mismatch: expected ${idx}, got ${idx2}`);\n                controller.enqueue({\n                    ...chunk,\n                    path\n                });\n            }\n        });\n    }\n};\nvar PathMergeEncoder = class extends TransformStream {\n    constructor(counter){\n        const innerCounter = new _Counter_js__WEBPACK_IMPORTED_MODULE_0__.Counter();\n        const mapping = /* @__PURE__ */ new Map();\n        super({\n            transform (chunk, controller) {\n                if (chunk.type === \"part-start\" && chunk.path.length === 0) {\n                    mapping.set(innerCounter.up(), counter.up());\n                }\n                const [idx, ...path] = chunk.path;\n                if (idx === void 0) {\n                    controller.enqueue(chunk);\n                    return;\n                }\n                const mappedIdx = mapping.get(idx);\n                if (mappedIdx === void 0) throw new Error(\"Path not found\");\n                controller.enqueue({\n                    ...chunk,\n                    path: [\n                        mappedIdx,\n                        ...path\n                    ]\n                });\n            }\n        });\n    }\n};\n //# sourceMappingURL=path-utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/stream/path-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/core/utils/withPromiseOrValue.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/core/utils/withPromiseOrValue.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withPromiseOrValue: () => (/* binding */ withPromiseOrValue)\n/* harmony export */ });\n// src/core/utils/withPromiseOrValue.ts\nfunction withPromiseOrValue(callback, thenHandler, catchHandler) {\n    try {\n        const promiseOrValue = callback();\n        if (typeof promiseOrValue === \"object\" && promiseOrValue !== null && \"then\" in promiseOrValue) {\n            return promiseOrValue.then(thenHandler, catchHandler);\n        } else {\n            thenHandler(promiseOrValue);\n        }\n    } catch (e) {\n        catchHandler(e);\n    }\n}\n //# sourceMappingURL=withPromiseOrValue.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L2NvcmUvdXRpbHMvd2l0aFByb21pc2VPclZhbHVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1Q0FBdUM7QUFDdkMsU0FBU0EsbUJBQW1CQyxRQUFRLEVBQUVDLFdBQVcsRUFBRUMsWUFBWTtJQUM3RCxJQUFJO1FBQ0YsTUFBTUMsaUJBQWlCSDtRQUN2QixJQUFJLE9BQU9HLG1CQUFtQixZQUFZQSxtQkFBbUIsUUFBUSxVQUFVQSxnQkFBZ0I7WUFDN0YsT0FBT0EsZUFBZUMsSUFBSSxDQUFDSCxhQUFhQztRQUMxQyxPQUFPO1lBQ0xELFlBQVlFO1FBQ2Q7SUFDRixFQUFFLE9BQU9FLEdBQUc7UUFDVkgsYUFBYUc7SUFDZjtBQUNGO0FBR0UsQ0FDRiw4Q0FBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL2Fzc2lzdGFudC1zdHJlYW0vZGlzdC9jb3JlL3V0aWxzL3dpdGhQcm9taXNlT3JWYWx1ZS5qcz9lY2NhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb3JlL3V0aWxzL3dpdGhQcm9taXNlT3JWYWx1ZS50c1xuZnVuY3Rpb24gd2l0aFByb21pc2VPclZhbHVlKGNhbGxiYWNrLCB0aGVuSGFuZGxlciwgY2F0Y2hIYW5kbGVyKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcHJvbWlzZU9yVmFsdWUgPSBjYWxsYmFjaygpO1xuICAgIGlmICh0eXBlb2YgcHJvbWlzZU9yVmFsdWUgPT09IFwib2JqZWN0XCIgJiYgcHJvbWlzZU9yVmFsdWUgIT09IG51bGwgJiYgXCJ0aGVuXCIgaW4gcHJvbWlzZU9yVmFsdWUpIHtcbiAgICAgIHJldHVybiBwcm9taXNlT3JWYWx1ZS50aGVuKHRoZW5IYW5kbGVyLCBjYXRjaEhhbmRsZXIpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGVuSGFuZGxlcihwcm9taXNlT3JWYWx1ZSk7XG4gICAgfVxuICB9IGNhdGNoIChlKSB7XG4gICAgY2F0Y2hIYW5kbGVyKGUpO1xuICB9XG59XG5leHBvcnQge1xuICB3aXRoUHJvbWlzZU9yVmFsdWVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD13aXRoUHJvbWlzZU9yVmFsdWUuanMubWFwIl0sIm5hbWVzIjpbIndpdGhQcm9taXNlT3JWYWx1ZSIsImNhbGxiYWNrIiwidGhlbkhhbmRsZXIiLCJjYXRjaEhhbmRsZXIiLCJwcm9taXNlT3JWYWx1ZSIsInRoZW4iLCJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/core/utils/withPromiseOrValue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/utils/AsyncIterableStream.js":
/*!*************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/utils/AsyncIterableStream.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asAsyncIterableStream: () => (/* binding */ asAsyncIterableStream)\n/* harmony export */ });\n// src/utils/AsyncIterableStream.ts\nasync function* streamGeneratorPolyfill() {\n    const reader = this.getReader();\n    try {\n        while(true){\n            const { done, value } = await reader.read();\n            if (done) break;\n            yield value;\n        }\n    } finally{\n        reader.releaseLock();\n    }\n}\nfunction asAsyncIterableStream(source) {\n    source[Symbol.asyncIterator] ??= streamGeneratorPolyfill;\n    return source;\n}\n //# sourceMappingURL=AsyncIterableStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L3V0aWxzL0FzeW5jSXRlcmFibGVTdHJlYW0uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLG1DQUFtQztBQUNuQyxnQkFBZ0JBO0lBQ2QsTUFBTUMsU0FBUyxJQUFJLENBQUNDLFNBQVM7SUFDN0IsSUFBSTtRQUNGLE1BQU8sS0FBTTtZQUNYLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNSCxPQUFPSSxJQUFJO1lBQ3pDLElBQUlGLE1BQU07WUFDVixNQUFNQztRQUNSO0lBQ0YsU0FBVTtRQUNSSCxPQUFPSyxXQUFXO0lBQ3BCO0FBQ0Y7QUFDQSxTQUFTQyxzQkFBc0JDLE1BQU07SUFDbkNBLE1BQU0sQ0FBQ0MsT0FBT0MsYUFBYSxDQUFDLEtBQUtWO0lBQ2pDLE9BQU9RO0FBQ1Q7QUFHRSxDQUNGLCtDQUErQyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L3V0aWxzL0FzeW5jSXRlcmFibGVTdHJlYW0uanM/ZDM4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvQXN5bmNJdGVyYWJsZVN0cmVhbS50c1xuYXN5bmMgZnVuY3Rpb24qIHN0cmVhbUdlbmVyYXRvclBvbHlmaWxsKCkge1xuICBjb25zdCByZWFkZXIgPSB0aGlzLmdldFJlYWRlcigpO1xuICB0cnkge1xuICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuICAgICAgaWYgKGRvbmUpIGJyZWFrO1xuICAgICAgeWllbGQgdmFsdWU7XG4gICAgfVxuICB9IGZpbmFsbHkge1xuICAgIHJlYWRlci5yZWxlYXNlTG9jaygpO1xuICB9XG59XG5mdW5jdGlvbiBhc0FzeW5jSXRlcmFibGVTdHJlYW0oc291cmNlKSB7XG4gIHNvdXJjZVtTeW1ib2wuYXN5bmNJdGVyYXRvcl0gPz89IHN0cmVhbUdlbmVyYXRvclBvbHlmaWxsO1xuICByZXR1cm4gc291cmNlO1xufVxuZXhwb3J0IHtcbiAgYXNBc3luY0l0ZXJhYmxlU3RyZWFtXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QXN5bmNJdGVyYWJsZVN0cmVhbS5qcy5tYXAiXSwibmFtZXMiOlsic3RyZWFtR2VuZXJhdG9yUG9seWZpbGwiLCJyZWFkZXIiLCJnZXRSZWFkZXIiLCJkb25lIiwidmFsdWUiLCJyZWFkIiwicmVsZWFzZUxvY2siLCJhc0FzeW5jSXRlcmFibGVTdHJlYW0iLCJzb3VyY2UiLCJTeW1ib2wiLCJhc3luY0l0ZXJhdG9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/utils/AsyncIterableStream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/utils/json/fix-json.js":
/*!*******************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/utils/json/fix-json.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixJson: () => (/* binding */ fixJson)\n/* harmony export */ });\n// src/utils/json/fix-json.ts\nfunction fixJson(input) {\n    const stack = [\n        \"ROOT\"\n    ];\n    let lastValidIndex = -1;\n    let literalStart = null;\n    const path = [];\n    let currentKey;\n    function pushCurrentKeyToPath() {\n        if (currentKey !== void 0) {\n            path.push(JSON.parse('\"' + currentKey + '\"'));\n            currentKey = void 0;\n        }\n    }\n    function processValueStart(char, i, swapState) {\n        {\n            switch(char){\n                case '\"':\n                    {\n                        lastValidIndex = i;\n                        stack.pop();\n                        stack.push(swapState);\n                        stack.push(\"INSIDE_STRING\");\n                        pushCurrentKeyToPath();\n                        break;\n                    }\n                case \"f\":\n                case \"t\":\n                case \"n\":\n                    {\n                        lastValidIndex = i;\n                        literalStart = i;\n                        stack.pop();\n                        stack.push(swapState);\n                        stack.push(\"INSIDE_LITERAL\");\n                        break;\n                    }\n                case \"-\":\n                    {\n                        stack.pop();\n                        stack.push(swapState);\n                        stack.push(\"INSIDE_NUMBER\");\n                        pushCurrentKeyToPath();\n                        break;\n                    }\n                case \"0\":\n                case \"1\":\n                case \"2\":\n                case \"3\":\n                case \"4\":\n                case \"5\":\n                case \"6\":\n                case \"7\":\n                case \"8\":\n                case \"9\":\n                    {\n                        lastValidIndex = i;\n                        stack.pop();\n                        stack.push(swapState);\n                        stack.push(\"INSIDE_NUMBER\");\n                        pushCurrentKeyToPath();\n                        break;\n                    }\n                case \"{\":\n                    {\n                        lastValidIndex = i;\n                        stack.pop();\n                        stack.push(swapState);\n                        stack.push(\"INSIDE_OBJECT_START\");\n                        pushCurrentKeyToPath();\n                        break;\n                    }\n                case \"[\":\n                    {\n                        lastValidIndex = i;\n                        stack.pop();\n                        stack.push(swapState);\n                        stack.push(\"INSIDE_ARRAY_START\");\n                        pushCurrentKeyToPath();\n                        break;\n                    }\n            }\n        }\n    }\n    function processAfterObjectValue(char, i) {\n        switch(char){\n            case \",\":\n                {\n                    stack.pop();\n                    stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n                    break;\n                }\n            case \"}\":\n                {\n                    lastValidIndex = i;\n                    stack.pop();\n                    currentKey = path.pop();\n                    break;\n                }\n        }\n    }\n    function processAfterArrayValue(char, i) {\n        switch(char){\n            case \",\":\n                {\n                    stack.pop();\n                    stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n                    currentKey = (Number(currentKey) + 1).toString();\n                    break;\n                }\n            case \"]\":\n                {\n                    lastValidIndex = i;\n                    stack.pop();\n                    currentKey = path.pop();\n                    break;\n                }\n        }\n    }\n    for(let i = 0; i < input.length; i++){\n        const char = input[i];\n        const currentState = stack[stack.length - 1];\n        switch(currentState){\n            case \"ROOT\":\n                processValueStart(char, i, \"FINISH\");\n                break;\n            case \"INSIDE_OBJECT_START\":\n                {\n                    switch(char){\n                        case '\"':\n                            {\n                                stack.pop();\n                                stack.push(\"INSIDE_OBJECT_KEY\");\n                                currentKey = \"\";\n                                break;\n                            }\n                        case \"}\":\n                            {\n                                lastValidIndex = i;\n                                stack.pop();\n                                currentKey = path.pop();\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_OBJECT_AFTER_COMMA\":\n                {\n                    switch(char){\n                        case '\"':\n                            {\n                                stack.pop();\n                                stack.push(\"INSIDE_OBJECT_KEY\");\n                                currentKey = \"\";\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_OBJECT_KEY\":\n                {\n                    switch(char){\n                        case '\"':\n                            {\n                                stack.pop();\n                                stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n                                break;\n                            }\n                        case \"\\\\\":\n                            {\n                                stack.push(\"INSIDE_STRING_ESCAPE\");\n                                currentKey += char;\n                                break;\n                            }\n                        default:\n                            {\n                                currentKey += char;\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_OBJECT_AFTER_KEY\":\n                {\n                    switch(char){\n                        case \":\":\n                            {\n                                stack.pop();\n                                stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_OBJECT_BEFORE_VALUE\":\n                {\n                    processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n                    break;\n                }\n            case \"INSIDE_OBJECT_AFTER_VALUE\":\n                {\n                    processAfterObjectValue(char, i);\n                    break;\n                }\n            case \"INSIDE_STRING\":\n                {\n                    switch(char){\n                        case '\"':\n                            {\n                                stack.pop();\n                                lastValidIndex = i;\n                                currentKey = path.pop();\n                                break;\n                            }\n                        case \"\\\\\":\n                            {\n                                stack.push(\"INSIDE_STRING_ESCAPE\");\n                                break;\n                            }\n                        default:\n                            {\n                                lastValidIndex = i;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_ARRAY_START\":\n                {\n                    switch(char){\n                        case \"]\":\n                            {\n                                lastValidIndex = i;\n                                stack.pop();\n                                currentKey = path.pop();\n                                break;\n                            }\n                        default:\n                            {\n                                lastValidIndex = i;\n                                currentKey = \"0\";\n                                processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_ARRAY_AFTER_VALUE\":\n                {\n                    switch(char){\n                        case \",\":\n                            {\n                                stack.pop();\n                                stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n                                currentKey = (Number(currentKey) + 1).toString();\n                                break;\n                            }\n                        case \"]\":\n                            {\n                                lastValidIndex = i;\n                                stack.pop();\n                                currentKey = path.pop();\n                                break;\n                            }\n                        default:\n                            {\n                                lastValidIndex = i;\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_ARRAY_AFTER_COMMA\":\n                {\n                    processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n                    break;\n                }\n            case \"INSIDE_STRING_ESCAPE\":\n                {\n                    stack.pop();\n                    if (stack[stack.length - 1] === \"INSIDE_STRING\") {\n                        lastValidIndex = i;\n                    } else if (stack[stack.length - 1] === \"INSIDE_OBJECT_KEY\") {\n                        currentKey += char;\n                    }\n                    break;\n                }\n            case \"INSIDE_NUMBER\":\n                {\n                    switch(char){\n                        case \"0\":\n                        case \"1\":\n                        case \"2\":\n                        case \"3\":\n                        case \"4\":\n                        case \"5\":\n                        case \"6\":\n                        case \"7\":\n                        case \"8\":\n                        case \"9\":\n                            {\n                                lastValidIndex = i;\n                                break;\n                            }\n                        case \"e\":\n                        case \"E\":\n                        case \"-\":\n                        case \".\":\n                            {\n                                break;\n                            }\n                        case \",\":\n                            {\n                                stack.pop();\n                                currentKey = path.pop();\n                                if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n                                    processAfterArrayValue(char, i);\n                                }\n                                if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n                                    processAfterObjectValue(char, i);\n                                }\n                                break;\n                            }\n                        case \"}\":\n                            {\n                                stack.pop();\n                                currentKey = path.pop();\n                                if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n                                    processAfterObjectValue(char, i);\n                                }\n                                break;\n                            }\n                        case \"]\":\n                            {\n                                stack.pop();\n                                currentKey = path.pop();\n                                if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n                                    processAfterArrayValue(char, i);\n                                }\n                                break;\n                            }\n                        default:\n                            {\n                                stack.pop();\n                                currentKey = path.pop();\n                                break;\n                            }\n                    }\n                    break;\n                }\n            case \"INSIDE_LITERAL\":\n                {\n                    const partialLiteral = input.substring(literalStart, i + 1);\n                    if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n                        stack.pop();\n                        if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n                            processAfterObjectValue(char, i);\n                        } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n                            processAfterArrayValue(char, i);\n                        }\n                    } else {\n                        lastValidIndex = i;\n                    }\n                    break;\n                }\n        }\n    }\n    let result = input.slice(0, lastValidIndex + 1);\n    for(let i = stack.length - 1; i >= 0; i--){\n        const state = stack[i];\n        switch(state){\n            case \"INSIDE_STRING\":\n                {\n                    result += '\"';\n                    break;\n                }\n            case \"INSIDE_OBJECT_KEY\":\n            case \"INSIDE_OBJECT_AFTER_KEY\":\n            case \"INSIDE_OBJECT_AFTER_COMMA\":\n            case \"INSIDE_OBJECT_START\":\n            case \"INSIDE_OBJECT_BEFORE_VALUE\":\n            case \"INSIDE_OBJECT_AFTER_VALUE\":\n                {\n                    result += \"}\";\n                    break;\n                }\n            case \"INSIDE_ARRAY_START\":\n            case \"INSIDE_ARRAY_AFTER_COMMA\":\n            case \"INSIDE_ARRAY_AFTER_VALUE\":\n                {\n                    result += \"]\";\n                    break;\n                }\n            case \"INSIDE_LITERAL\":\n                {\n                    const partialLiteral = input.substring(literalStart, input.length);\n                    if (\"true\".startsWith(partialLiteral)) {\n                        result += \"true\".slice(partialLiteral.length);\n                    } else if (\"false\".startsWith(partialLiteral)) {\n                        result += \"false\".slice(partialLiteral.length);\n                    } else if (\"null\".startsWith(partialLiteral)) {\n                        result += \"null\".slice(partialLiteral.length);\n                    }\n                }\n        }\n    }\n    return [\n        result,\n        path\n    ];\n}\n //# sourceMappingURL=fix-json.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/utils/json/fix-json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/utils/json/parse-partial-json-object.js":
/*!************************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/utils/json/parse-partial-json-object.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPartialJsonObjectFieldState: () => (/* binding */ getPartialJsonObjectFieldState),\n/* harmony export */   getPartialJsonObjectMeta: () => (/* binding */ getPartialJsonObjectMeta),\n/* harmony export */   parsePartialJsonObject: () => (/* binding */ parsePartialJsonObject)\n/* harmony export */ });\n/* harmony import */ var secure_json_parse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! secure-json-parse */ \"(ssr)/./node_modules/assistant-stream/node_modules/secure-json-parse/index.js\");\n/* harmony import */ var _fix_json_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fix-json.js */ \"(ssr)/./node_modules/assistant-stream/dist/utils/json/fix-json.js\");\n// src/utils/json/parse-partial-json-object.ts\n\n\nvar PARTIAL_JSON_OBJECT_META_SYMBOL = Symbol(\"aui.parse-partial-json-object.meta\");\nvar getPartialJsonObjectMeta = (obj)=>{\n    return obj?.[PARTIAL_JSON_OBJECT_META_SYMBOL];\n};\nvar parsePartialJsonObject = (json)=>{\n    if (json.length === 0) return {\n        [PARTIAL_JSON_OBJECT_META_SYMBOL]: {\n            state: \"partial\",\n            partialPath: []\n        }\n    };\n    try {\n        const res = secure_json_parse__WEBPACK_IMPORTED_MODULE_0__.parse(json);\n        if (typeof res !== \"object\" || res === null) throw new Error(\"argsText is expected to be an object\");\n        res[PARTIAL_JSON_OBJECT_META_SYMBOL] = {\n            state: \"complete\",\n            partialPath: []\n        };\n        return res;\n    } catch  {\n        try {\n            const [fixedJson, partialPath] = (0,_fix_json_js__WEBPACK_IMPORTED_MODULE_1__.fixJson)(json);\n            const res = secure_json_parse__WEBPACK_IMPORTED_MODULE_0__.parse(fixedJson);\n            if (typeof res !== \"object\" || res === null) throw new Error(\"argsText is expected to be an object\");\n            res[PARTIAL_JSON_OBJECT_META_SYMBOL] = {\n                state: \"partial\",\n                partialPath\n            };\n            return res;\n        } catch  {\n            return void 0;\n        }\n    }\n};\nvar getFieldState = (parent, parentMeta, fieldPath)=>{\n    if (typeof parent !== \"object\" || parent === null) return parentMeta.state;\n    if (parentMeta.state === \"complete\") return \"complete\";\n    if (fieldPath.length === 0) return parentMeta.state;\n    const [field, ...restPath] = fieldPath;\n    if (!Object.prototype.hasOwnProperty.call(parent, field)) return \"partial\";\n    const [partialField, ...restPartialPath] = parentMeta.partialPath;\n    if (field !== partialField) return \"complete\";\n    const child = parent[field];\n    const childMeta = {\n        state: \"partial\",\n        partialPath: restPartialPath\n    };\n    return getFieldState(child, childMeta, restPath);\n};\nvar getPartialJsonObjectFieldState = (obj, fieldPath)=>{\n    const meta = getPartialJsonObjectMeta(obj);\n    if (!meta) throw new Error(\"unable to determine object state\");\n    return getFieldState(obj, meta, fieldPath.map(String));\n};\n //# sourceMappingURL=parse-partial-json-object.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/utils/json/parse-partial-json-object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/dist/utils/promiseWithResolvers.js":
/*!**************************************************************************!*\
  !*** ./node_modules/assistant-stream/dist/utils/promiseWithResolvers.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   promiseWithResolvers: () => (/* binding */ promiseWithResolvers)\n/* harmony export */ });\n// src/utils/promiseWithResolvers.ts\nvar promiseWithResolvers = function() {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    if (!resolve || !reject) throw new Error(\"Failed to create promise\");\n    return {\n        promise,\n        resolve,\n        reject\n    };\n};\n //# sourceMappingURL=promiseWithResolvers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9kaXN0L3V0aWxzL3Byb21pc2VXaXRoUmVzb2x2ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxvQ0FBb0M7QUFDcEMsSUFBSUEsdUJBQXVCO0lBQ3pCLElBQUlDO0lBQ0osSUFBSUM7SUFDSixNQUFNQyxVQUFVLElBQUlDLFFBQVEsQ0FBQ0MsS0FBS0M7UUFDaENMLFVBQVVJO1FBQ1ZILFNBQVNJO0lBQ1g7SUFDQSxJQUFJLENBQUNMLFdBQVcsQ0FBQ0MsUUFBUSxNQUFNLElBQUlLLE1BQU07SUFDekMsT0FBTztRQUFFSjtRQUFTRjtRQUFTQztJQUFPO0FBQ3BDO0FBR0UsQ0FDRixnREFBZ0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL2Fzc2lzdGFudC1zdHJlYW0vZGlzdC91dGlscy9wcm9taXNlV2l0aFJlc29sdmVycy5qcz82NWE5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9wcm9taXNlV2l0aFJlc29sdmVycy50c1xudmFyIHByb21pc2VXaXRoUmVzb2x2ZXJzID0gZnVuY3Rpb24oKSB7XG4gIGxldCByZXNvbHZlO1xuICBsZXQgcmVqZWN0O1xuICBjb25zdCBwcm9taXNlID0gbmV3IFByb21pc2UoKHJlcywgcmVqKSA9PiB7XG4gICAgcmVzb2x2ZSA9IHJlcztcbiAgICByZWplY3QgPSByZWo7XG4gIH0pO1xuICBpZiAoIXJlc29sdmUgfHwgIXJlamVjdCkgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGNyZWF0ZSBwcm9taXNlXCIpO1xuICByZXR1cm4geyBwcm9taXNlLCByZXNvbHZlLCByZWplY3QgfTtcbn07XG5leHBvcnQge1xuICBwcm9taXNlV2l0aFJlc29sdmVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByb21pc2VXaXRoUmVzb2x2ZXJzLmpzLm1hcCJdLCJuYW1lcyI6WyJwcm9taXNlV2l0aFJlc29sdmVycyIsInJlc29sdmUiLCJyZWplY3QiLCJwcm9taXNlIiwiUHJvbWlzZSIsInJlcyIsInJlaiIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/dist/utils/promiseWithResolvers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/assistant-stream/node_modules/nanoid/non-secure/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/assistant-stream/node_modules/nanoid/non-secure/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customAlphabet: () => (/* binding */ customAlphabet),\n/* harmony export */   nanoid: () => (/* binding */ nanoid)\n/* harmony export */ });\n/* @ts-self-types=\"./index.d.ts\" */ let urlAlphabet = \"useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict\";\nlet customAlphabet = (alphabet, defaultSize = 21)=>{\n    return (size = defaultSize)=>{\n        let id = \"\";\n        let i = size | 0;\n        while(i--){\n            id += alphabet[Math.random() * alphabet.length | 0];\n        }\n        return id;\n    };\n};\nlet nanoid = (size = 21)=>{\n    let id = \"\";\n    let i = size | 0;\n    while(i--){\n        id += urlAlphabet[Math.random() * 64 | 0];\n    }\n    return id;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXNzaXN0YW50LXN0cmVhbS9ub2RlX21vZHVsZXMvbmFub2lkL25vbi1zZWN1cmUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxpQ0FBaUMsR0FDakMsSUFBSUEsY0FDRjtBQUNLLElBQUlDLGlCQUFpQixDQUFDQyxVQUFVQyxjQUFjLEVBQUU7SUFDckQsT0FBTyxDQUFDQyxPQUFPRCxXQUFXO1FBQ3hCLElBQUlFLEtBQUs7UUFDVCxJQUFJQyxJQUFJRixPQUFPO1FBQ2YsTUFBT0UsSUFBSztZQUNWRCxNQUFNSCxRQUFRLENBQUMsS0FBTU0sTUFBTSxLQUFLTixTQUFTTyxNQUFNLEdBQUksRUFBRTtRQUN2RDtRQUNBLE9BQU9KO0lBQ1Q7QUFDRixFQUFDO0FBQ00sSUFBSUssU0FBUyxDQUFDTixPQUFPLEVBQUU7SUFDNUIsSUFBSUMsS0FBSztJQUNULElBQUlDLElBQUlGLE9BQU87SUFDZixNQUFPRSxJQUFLO1FBQ1ZELE1BQU1MLFdBQVcsQ0FBQyxLQUFNUSxNQUFNLEtBQUssS0FBTSxFQUFFO0lBQzdDO0lBQ0EsT0FBT0g7QUFDVCxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9hc3Npc3RhbnQtc3RyZWFtL25vZGVfbW9kdWxlcy9uYW5vaWQvbm9uLXNlY3VyZS9pbmRleC5qcz82MjFlIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIEB0cy1zZWxmLXR5cGVzPVwiLi9pbmRleC5kLnRzXCIgKi9cbmxldCB1cmxBbHBoYWJldCA9XG4gICd1c2VhbmRvbS0yNlQxOTgzNDBQWDc1cHhKQUNLVkVSWU1JTkRCVVNIV09MRl9HUVpiZmdoamtscXZ3eXpyaWN0J1xuZXhwb3J0IGxldCBjdXN0b21BbHBoYWJldCA9IChhbHBoYWJldCwgZGVmYXVsdFNpemUgPSAyMSkgPT4ge1xuICByZXR1cm4gKHNpemUgPSBkZWZhdWx0U2l6ZSkgPT4ge1xuICAgIGxldCBpZCA9ICcnXG4gICAgbGV0IGkgPSBzaXplIHwgMFxuICAgIHdoaWxlIChpLS0pIHtcbiAgICAgIGlkICs9IGFscGhhYmV0WyhNYXRoLnJhbmRvbSgpICogYWxwaGFiZXQubGVuZ3RoKSB8IDBdXG4gICAgfVxuICAgIHJldHVybiBpZFxuICB9XG59XG5leHBvcnQgbGV0IG5hbm9pZCA9IChzaXplID0gMjEpID0+IHtcbiAgbGV0IGlkID0gJydcbiAgbGV0IGkgPSBzaXplIHwgMFxuICB3aGlsZSAoaS0tKSB7XG4gICAgaWQgKz0gdXJsQWxwaGFiZXRbKE1hdGgucmFuZG9tKCkgKiA2NCkgfCAwXVxuICB9XG4gIHJldHVybiBpZFxufVxuIl0sIm5hbWVzIjpbInVybEFscGhhYmV0IiwiY3VzdG9tQWxwaGFiZXQiLCJhbHBoYWJldCIsImRlZmF1bHRTaXplIiwic2l6ZSIsImlkIiwiaSIsIk1hdGgiLCJyYW5kb20iLCJsZW5ndGgiLCJuYW5vaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/assistant-stream/node_modules/nanoid/non-secure/index.js\n");

/***/ })

};
;