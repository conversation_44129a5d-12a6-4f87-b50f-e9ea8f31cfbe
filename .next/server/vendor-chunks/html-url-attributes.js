"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-url-attributes";
exports.ids = ["vendor-chunks/html-url-attributes"];
exports.modules = {

/***/ "(ssr)/./node_modules/html-url-attributes/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/html-url-attributes/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   urlAttributes: () => (/* binding */ urlAttributes)\n/* harmony export */ });\n/**\n * HTML URL properties.\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n *\n * @type {Record<string, Array<string> | null>}\n */ const urlAttributes = {\n    action: [\n        \"form\"\n    ],\n    cite: [\n        \"blockquote\",\n        \"del\",\n        \"ins\",\n        \"q\"\n    ],\n    data: [\n        \"object\"\n    ],\n    formAction: [\n        \"button\",\n        \"input\"\n    ],\n    href: [\n        \"a\",\n        \"area\",\n        \"base\",\n        \"link\"\n    ],\n    icon: [\n        \"menuitem\"\n    ],\n    itemId: null,\n    manifest: [\n        \"html\"\n    ],\n    ping: [\n        \"a\",\n        \"area\"\n    ],\n    poster: [\n        \"video\"\n    ],\n    src: [\n        \"audio\",\n        \"embed\",\n        \"iframe\",\n        \"img\",\n        \"input\",\n        \"script\",\n        \"source\",\n        \"track\",\n        \"video\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC11cmwtYXR0cmlidXRlcy9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7Ozs7O0NBT0MsR0FDTSxNQUFNQSxnQkFBZ0I7SUFDM0JDLFFBQVE7UUFBQztLQUFPO0lBQ2hCQyxNQUFNO1FBQUM7UUFBYztRQUFPO1FBQU87S0FBSTtJQUN2Q0MsTUFBTTtRQUFDO0tBQVM7SUFDaEJDLFlBQVk7UUFBQztRQUFVO0tBQVE7SUFDL0JDLE1BQU07UUFBQztRQUFLO1FBQVE7UUFBUTtLQUFPO0lBQ25DQyxNQUFNO1FBQUM7S0FBVztJQUNsQkMsUUFBUTtJQUNSQyxVQUFVO1FBQUM7S0FBTztJQUNsQkMsTUFBTTtRQUFDO1FBQUs7S0FBTztJQUNuQkMsUUFBUTtRQUFDO0tBQVE7SUFDakJDLEtBQUs7UUFDSDtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNILEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vbm9kZV9tb2R1bGVzL2h0bWwtdXJsLWF0dHJpYnV0ZXMvbGliL2luZGV4LmpzP2QyZGMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBIVE1MIFVSTCBwcm9wZXJ0aWVzLlxuICpcbiAqIEVhY2gga2V5IGlzIGEgcHJvcGVydHkgbmFtZSBhbmQgZWFjaCB2YWx1ZSBpcyBhIGxpc3Qgb2YgdGFnIG5hbWVzIGl0IGFwcGxpZXNcbiAqIHRvIG9yIGBudWxsYCBpZiBpdCBhcHBsaWVzIHRvIGFsbCBlbGVtZW50cy5cbiAqXG4gKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgQXJyYXk8c3RyaW5nPiB8IG51bGw+fVxuICovXG5leHBvcnQgY29uc3QgdXJsQXR0cmlidXRlcyA9IHtcbiAgYWN0aW9uOiBbJ2Zvcm0nXSxcbiAgY2l0ZTogWydibG9ja3F1b3RlJywgJ2RlbCcsICdpbnMnLCAncSddLFxuICBkYXRhOiBbJ29iamVjdCddLFxuICBmb3JtQWN0aW9uOiBbJ2J1dHRvbicsICdpbnB1dCddLFxuICBocmVmOiBbJ2EnLCAnYXJlYScsICdiYXNlJywgJ2xpbmsnXSxcbiAgaWNvbjogWydtZW51aXRlbSddLFxuICBpdGVtSWQ6IG51bGwsXG4gIG1hbmlmZXN0OiBbJ2h0bWwnXSxcbiAgcGluZzogWydhJywgJ2FyZWEnXSxcbiAgcG9zdGVyOiBbJ3ZpZGVvJ10sXG4gIHNyYzogW1xuICAgICdhdWRpbycsXG4gICAgJ2VtYmVkJyxcbiAgICAnaWZyYW1lJyxcbiAgICAnaW1nJyxcbiAgICAnaW5wdXQnLFxuICAgICdzY3JpcHQnLFxuICAgICdzb3VyY2UnLFxuICAgICd0cmFjaycsXG4gICAgJ3ZpZGVvJ1xuICBdXG59XG4iXSwibmFtZXMiOlsidXJsQXR0cmlidXRlcyIsImFjdGlvbiIsImNpdGUiLCJkYXRhIiwiZm9ybUFjdGlvbiIsImhyZWYiLCJpY29uIiwiaXRlbUlkIiwibWFuaWZlc3QiLCJwaW5nIiwicG9zdGVyIiwic3JjIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-url-attributes/lib/index.js\n");

/***/ })

};
;