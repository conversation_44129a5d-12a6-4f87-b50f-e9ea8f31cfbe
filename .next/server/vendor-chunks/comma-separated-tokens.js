"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/comma-separated-tokens";
exports.ids = ["vendor-chunks/comma-separated-tokens"];
exports.modules = {

/***/ "(ssr)/./node_modules/comma-separated-tokens/index.js":
/*!******************************************************!*\
  !*** ./node_modules/comma-separated-tokens/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * @typedef Options\n *   Configuration for `stringify`.\n * @property {boolean} [padLeft=true]\n *   Whether to pad a space before a token.\n * @property {boolean} [padRight=false]\n *   Whether to pad a space after a token.\n */ /**\n * @typedef {Options} StringifyOptions\n *   Please use `StringifyOptions` instead.\n */ /**\n * Parse comma-separated tokens to an array.\n *\n * @param {string} value\n *   Comma-separated tokens.\n * @returns {Array<string>}\n *   List of tokens.\n */ function parse(value) {\n    /** @type {Array<string>} */ const tokens = [];\n    const input = String(value || \"\");\n    let index = input.indexOf(\",\");\n    let start = 0;\n    /** @type {boolean} */ let end = false;\n    while(!end){\n        if (index === -1) {\n            index = input.length;\n            end = true;\n        }\n        const token = input.slice(start, index).trim();\n        if (token || !end) {\n            tokens.push(token);\n        }\n        start = index + 1;\n        index = input.indexOf(\",\", start);\n    }\n    return tokens;\n}\n/**\n * Serialize an array of strings or numbers to comma-separated tokens.\n *\n * @param {Array<string|number>} values\n *   List of tokens.\n * @param {Options} [options]\n *   Configuration for `stringify` (optional).\n * @returns {string}\n *   Comma-separated tokens.\n */ function stringify(values, options) {\n    const settings = options || {};\n    // Ensure the last empty entry is seen.\n    const input = values[values.length - 1] === \"\" ? [\n        ...values,\n        \"\"\n    ] : values;\n    return input.join((settings.padRight ? \" \" : \"\") + \",\" + (settings.padLeft === false ? \"\" : \" \")).trim();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/comma-separated-tokens/index.js\n");

/***/ })

};
;