"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark";
exports.ids = ["vendor-chunks/micromark"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark/dev/lib/constructs.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/constructs.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attentionMarkers: () => (/* binding */ attentionMarkers),\n/* harmony export */   contentInitial: () => (/* binding */ contentInitial),\n/* harmony export */   disable: () => (/* binding */ disable),\n/* harmony export */   document: () => (/* binding */ document),\n/* harmony export */   flow: () => (/* binding */ flow),\n/* harmony export */   flowInitial: () => (/* binding */ flowInitial),\n/* harmony export */   insideSpan: () => (/* binding */ insideSpan),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/list.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/definition.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/attention.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/**\n * @import {Extension} from 'micromark-util-types'\n */ \n\n\n/** @satisfies {Extension['document']} */ const document = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.plusSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit0]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit1]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit2]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit3]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit4]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit5]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit6]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit7]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit8]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.digit9]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_1__.list,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.greaterThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.blockQuote\n};\n/** @satisfies {Extension['contentInitial']} */ const contentInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_3__.definition\n};\n/** @satisfies {Extension['flowInitial']} */ const flowInitial = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_4__.codeIndented\n};\n/** @satisfies {Extension['flow']} */ const flow = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.numberSign]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_5__.headingAtx,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_8__.htmlFlow,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.equalsTo]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.setextUnderline,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_6__.thematicBreak,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.tilde]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_9__.codeFenced\n};\n/** @satisfies {Extension['string']} */ const string = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n};\n/** @satisfies {Extension['text']} */ const text = {\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_12__.lineEnding,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.exclamationMark]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_13__.labelStartImage,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ampersand]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_10__.characterReference,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lessThan]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_15__.autolink,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_16__.htmlText\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_17__.labelStartLink,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash]: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_18__.hardBreakEscape,\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_11__.characterEscape\n    ],\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_19__.labelEnd,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n    [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.graveAccent]: micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_20__.codeText\n};\n/** @satisfies {Extension['insideSpan']} */ const insideSpan = {\n    null: [\n        micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_14__.attention,\n        _initialize_text_js__WEBPACK_IMPORTED_MODULE_21__.resolver\n    ]\n};\n/** @satisfies {Extension['attentionMarkers']} */ const attentionMarkers = {\n    null: [\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.asterisk,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.underscore\n    ]\n};\n/** @satisfies {Extension['disable']} */ const disable = {\n    null: []\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/constructs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js":
/*!************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/create-tokenizer.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTokenizer: () => (/* binding */ createTokenizer)\n/* harmony export */ });\n/* harmony import */ var debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/values.js\");\n/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */ \n\n\n\n\n\nconst debug = debug__WEBPACK_IMPORTED_MODULE_0__(\"micromark\");\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */ function createTokenizer(parser, initialize, from) {\n    /** @type {Point} */ let point = {\n        _bufferIndex: -1,\n        _index: 0,\n        line: from && from.line || 1,\n        column: from && from.column || 1,\n        offset: from && from.offset || 0\n    };\n    /** @type {Record<string, number>} */ const columnStart = {};\n    /** @type {Array<Construct>} */ const resolveAllConstructs = [];\n    /** @type {Array<Chunk>} */ let chunks = [];\n    /** @type {Array<Token>} */ let stack = [];\n    /** @type {boolean | undefined} */ let consumed = true;\n    /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */ const effects = {\n        attempt: constructFactory(onsuccessfulconstruct),\n        check: constructFactory(onsuccessfulcheck),\n        consume,\n        enter,\n        exit,\n        interrupt: constructFactory(onsuccessfulcheck, {\n            interrupt: true\n        })\n    };\n    /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */ const context = {\n        code: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        containerState: {},\n        defineSkip,\n        events: [],\n        now,\n        parser,\n        previous: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof,\n        sliceSerialize,\n        sliceStream,\n        write\n    };\n    /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */ let state = initialize.tokenize.call(context, effects);\n    /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */ let expectedCode;\n    if (initialize.resolveAll) {\n        resolveAllConstructs.push(initialize);\n    }\n    return context;\n    /** @type {TokenizeContext['write']} */ function write(slice) {\n        chunks = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(chunks, slice);\n        main();\n        // Exit if we’re not done, resolve might change stuff.\n        if (chunks[chunks.length - 1] !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            return [];\n        }\n        addResult(initialize, 0);\n        // Otherwise, resolve, and exit.\n        context.events = (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(resolveAllConstructs, context.events, context);\n        return context.events;\n    }\n    //\n    // Tools.\n    //\n    /** @type {TokenizeContext['sliceSerialize']} */ function sliceSerialize(token, expandTabs) {\n        return serializeChunks(sliceStream(token), expandTabs);\n    }\n    /** @type {TokenizeContext['sliceStream']} */ function sliceStream(token) {\n        return sliceChunks(chunks, token);\n    }\n    /** @type {TokenizeContext['now']} */ function now() {\n        // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n        const { _bufferIndex, _index, line, column, offset } = point;\n        return {\n            _bufferIndex,\n            _index,\n            line,\n            column,\n            offset\n        };\n    }\n    /** @type {TokenizeContext['defineSkip']} */ function defineSkip(value) {\n        columnStart[value.line] = value.column;\n        accountForPotentialSkip();\n        debug(\"position: define skip: `%j`\", point);\n    }\n    //\n    // State management.\n    //\n    /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function main() {\n        /** @type {number} */ let chunkIndex;\n        while(point._index < chunks.length){\n            const chunk = chunks[point._index];\n            // If we’re in a buffer chunk, loop through it.\n            if (typeof chunk === \"string\") {\n                chunkIndex = point._index;\n                if (point._bufferIndex < 0) {\n                    point._bufferIndex = 0;\n                }\n                while(point._index === chunkIndex && point._bufferIndex < chunk.length){\n                    go(chunk.charCodeAt(point._bufferIndex));\n                }\n            } else {\n                go(chunk);\n            }\n        }\n    }\n    /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */ function go(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === true, \"expected character to be consumed\");\n        consumed = undefined;\n        debug(\"main: passing `%s` to %s\", code, state && state.name);\n        expectedCode = code;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof state === \"function\", \"expected state\");\n        state = state(code);\n    }\n    /** @type {Effects['consume']} */ function consume(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected given code to equal expected code\");\n        debug(\"consume: `%s`\", code);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(consumed === undefined, \"expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === null ? context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\" : context.events[context.events.length - 1][0] === \"enter\", \"expected last token to be open\");\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            point.line++;\n            point.column = 1;\n            point.offset += code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed ? 2 : 1;\n            accountForPotentialSkip();\n            debug(\"position: after eol: `%j`\", point);\n        } else if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace) {\n            point.column++;\n            point.offset++;\n        }\n        // Not in a string chunk.\n        if (point._bufferIndex < 0) {\n            point._index++;\n        } else {\n            point._bufferIndex++;\n            // At end of string chunk.\n            if (point._bufferIndex === // Points w/ non-negative `_bufferIndex` reference\n            // strings.\n            /** @type {string} */ chunks[point._index].length) {\n                point._bufferIndex = -1;\n                point._index++;\n            }\n        }\n        // Expose the previous character.\n        context.previous = code;\n        // Mark as consumed.\n        consumed = true;\n    }\n    /** @type {Effects['enter']} */ function enter(type, fields) {\n        /** @type {Token} */ // @ts-expect-error Patch instead of assign required fields to help GC.\n        const token = fields || {};\n        token.type = type;\n        token.start = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        debug(\"enter: `%s`\", type);\n        context.events.push([\n            \"enter\",\n            token,\n            context\n        ]);\n        stack.push(token);\n        return token;\n    }\n    /** @type {Effects['exit']} */ function exit(type) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof type === \"string\", \"expected string type\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type.length > 0, \"expected non-empty string\");\n        const token = stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token, \"cannot close w/o open tokens\");\n        token.end = now();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(type === token.type, \"expected exit token to match current token\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(!(token.start._index === token.end._index && token.start._bufferIndex === token.end._bufferIndex), \"expected non-empty token (`\" + type + \"`)\");\n        debug(\"exit: `%s`\", token.type);\n        context.events.push([\n            \"exit\",\n            token,\n            context\n        ]);\n        return token;\n    }\n    /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulconstruct(construct, info) {\n        addResult(construct, info.from);\n    }\n    /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */ function onsuccessfulcheck(_, info) {\n        info.restore();\n    }\n    /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */ function constructFactory(onreturn, fields) {\n        return hook;\n        /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */ function hook(constructs, returnState, bogusState) {\n            /** @type {ReadonlyArray<Construct>} */ let listOfConstructs;\n            /** @type {number} */ let constructIndex;\n            /** @type {Construct} */ let currentConstruct;\n            /** @type {Info} */ let info;\n            return Array.isArray(constructs) ? /* c8 ignore next 1 */ handleListOfConstructs(constructs) : \"tokenize\" in constructs ? handleListOfConstructs([\n                /** @type {Construct} */ constructs\n            ]) : handleMapOfConstructs(constructs);\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleMapOfConstructs(map) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    const left = code !== null && map[code];\n                    const all = code !== null && map.null;\n                    const list = [\n                        // To do: add more extension tests.\n                        /* c8 ignore next 2 */ ...Array.isArray(left) ? left : left ? [\n                            left\n                        ] : [],\n                        ...Array.isArray(all) ? all : all ? [\n                            all\n                        ] : []\n                    ];\n                    return handleListOfConstructs(list)(code);\n                }\n            }\n            /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */ function handleListOfConstructs(list) {\n                listOfConstructs = list;\n                constructIndex = 0;\n                if (list.length === 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(bogusState, \"expected `bogusState` to be given\");\n                    return bogusState;\n                }\n                return handleConstruct(list[constructIndex]);\n            }\n            /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */ function handleConstruct(construct) {\n                return start;\n                /** @type {State} */ function start(code) {\n                    // To do: not needed to store if there is no bogus state, probably?\n                    // Currently doesn’t work because `inspect` in document does a check\n                    // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n                    // by not storing.\n                    info = store();\n                    currentConstruct = construct;\n                    if (!construct.partial) {\n                        context.currentConstruct = construct;\n                    }\n                    // Always populated by defaults.\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n                    if (construct.name && context.parser.constructs.disable.null.includes(construct.name)) {\n                        return nok(code);\n                    }\n                    return construct.tokenize.call(// If we do have fields, create an object w/ `context` as its\n                    // prototype.\n                    // This allows a “live binding”, which is needed for `interrupt`.\n                    fields ? Object.assign(Object.create(context), fields) : context, effects, ok, nok)(code);\n                }\n            }\n            /** @type {State} */ function ok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                onreturn(currentConstruct, info);\n                return returnState;\n            }\n            /** @type {State} */ function nok(code) {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === expectedCode, \"expected code\");\n                consumed = true;\n                info.restore();\n                if (++constructIndex < listOfConstructs.length) {\n                    return handleConstruct(listOfConstructs[constructIndex]);\n                }\n                return bogusState;\n            }\n        }\n    }\n    /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */ function addResult(construct, from) {\n        if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n            resolveAllConstructs.push(construct);\n        }\n        if (construct.resolve) {\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(context.events, from, context.events.length - from, construct.resolve(context.events.slice(from), context));\n        }\n        if (construct.resolveTo) {\n            context.events = construct.resolveTo(context.events, context);\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(construct.partial || context.events.length === 0 || context.events[context.events.length - 1][0] === \"exit\", \"expected last token to end\");\n    }\n    /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */ function store() {\n        const startPoint = now();\n        const startPrevious = context.previous;\n        const startCurrentConstruct = context.currentConstruct;\n        const startEventsIndex = context.events.length;\n        const startStack = Array.from(stack);\n        return {\n            from: startEventsIndex,\n            restore\n        };\n        /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */ function restore() {\n            point = startPoint;\n            context.previous = startPrevious;\n            context.currentConstruct = startCurrentConstruct;\n            context.events.length = startEventsIndex;\n            stack = startStack;\n            accountForPotentialSkip();\n            debug(\"position: restore: `%j`\", point);\n        }\n    }\n    /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */ function accountForPotentialSkip() {\n        if (point.line in columnStart && point.column < 2) {\n            point.column = columnStart[point.line];\n            point.offset += columnStart[point.line] - 1;\n        }\n    }\n}\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */ function sliceChunks(chunks, token) {\n    const startIndex = token.start._index;\n    const startBufferIndex = token.start._bufferIndex;\n    const endIndex = token.end._index;\n    const endBufferIndex = token.end._bufferIndex;\n    /** @type {Array<Chunk>} */ let view;\n    if (startIndex === endIndex) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(endBufferIndex > -1, \"expected non-negative end buffer index\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex > -1, \"expected non-negative start buffer index\");\n        // @ts-expect-error `_bufferIndex` is used on string chunks.\n        view = [\n            chunks[startIndex].slice(startBufferIndex, endBufferIndex)\n        ];\n    } else {\n        view = chunks.slice(startIndex, endIndex);\n        if (startBufferIndex > -1) {\n            const head = view[0];\n            if (typeof head === \"string\") {\n                view[0] = head.slice(startBufferIndex);\n            /* c8 ignore next 4 -- used to be used, no longer */ } else {\n                (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(startBufferIndex === 0, \"expected `startBufferIndex` to be `0`\");\n                view.shift();\n            }\n        }\n        if (endBufferIndex > 0) {\n            // @ts-expect-error `_bufferIndex` is used on string chunks.\n            view.push(chunks[endIndex].slice(0, endBufferIndex));\n        }\n    }\n    return view;\n}\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */ function serializeChunks(chunks, expandTabs) {\n    let index = -1;\n    /** @type {Array<string>} */ const result = [];\n    /** @type {boolean | undefined} */ let atTab;\n    while(++index < chunks.length){\n        const chunk = chunks[index];\n        /** @type {string} */ let value;\n        if (typeof chunk === \"string\") {\n            value = chunk;\n        } else switch(chunk){\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturn:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.lineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.carriageReturnLineFeed:\n                {\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.cr + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.lf;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab:\n                {\n                    value = expandTabs ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.ht;\n                    break;\n                }\n            case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.virtualSpace:\n                {\n                    if (!expandTabs && atTab) continue;\n                    value = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_6__.values.space;\n                    break;\n                }\n            default:\n                {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof chunk === \"number\", \"expected number\");\n                    // Currently only replacement character.\n                    value = String.fromCharCode(chunk);\n                }\n        }\n        atTab = chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.horizontalTab;\n        result.push(value);\n    }\n    return result.join(\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/content.js":
/*!**************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/content.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */ \n\n\n\n/** @type {InitialConstruct} */ const content = {\n    tokenize: initializeContent\n};\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */ function initializeContent(effects) {\n    const contentStart = effects.attempt(this.parser.constructs.contentInitial, afterContentStartConstruct, paragraphInitial);\n    /** @type {Token} */ let previous;\n    return contentStart;\n    /** @type {State} */ function afterContentStartConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, contentStart, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix);\n    }\n    /** @type {State} */ function paragraphInitial(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code), \"expected anything other than a line ending or EOF\");\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n        return lineStart(code);\n    }\n    /** @type {State} */ function lineStart(code) {\n        const token = effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText, {\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText,\n            previous\n        });\n        if (previous) {\n            previous.next = token;\n        }\n        previous = token;\n        return data(code);\n    }\n    /** @type {State} */ function data(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.paragraph);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.chunkText);\n            return lineStart;\n        }\n        // Data.\n        effects.consume(code);\n        return data;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/document.js":
/*!***************************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   document: () => (/* binding */ document)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */ /**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const document = {\n    tokenize: initializeDocument\n};\n/** @type {Construct} */ const containerConstruct = {\n    tokenize: tokenizeContainer\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeDocument(effects) {\n    const self = this;\n    /** @type {Array<StackItem>} */ const stack = [];\n    let continued = 0;\n    /** @type {TokenizeContext | undefined} */ let childFlow;\n    /** @type {Token | undefined} */ let childToken;\n    /** @type {number} */ let lineStartOffset;\n    return start;\n    /** @type {State} */ function start(code) {\n        // First we iterate through the open blocks, starting with the root\n        // document, and descending through last children down to the last open\n        // block.\n        // Each block imposes a condition that the line must satisfy if the block is\n        // to remain open.\n        // For example, a block quote requires a `>` character.\n        // A paragraph requires a non-blank line.\n        // In this phase we may match all or just some of the open blocks.\n        // But we cannot close unmatched blocks yet, because we may have a lazy\n        // continuation line.\n        if (continued < stack.length) {\n            const item = stack[continued];\n            self.containerState = item[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(item[0].continuation, \"expected `continuation` to be defined on container construct\");\n            return effects.attempt(item[0].continuation, documentContinue, checkNewContainers)(code);\n        }\n        // Done.\n        return checkNewContainers(code);\n    }\n    /** @type {State} */ function documentContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined after continuation\");\n        continued++;\n        // Note: this field is called `_closeFlow` but it also closes containers.\n        // Perhaps a good idea to rename it but it’s already used in the wild by\n        // extensions.\n        if (self.containerState._closeFlow) {\n            self.containerState._closeFlow = undefined;\n            if (childFlow) {\n                closeFlow();\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when dealing with lazy lines in `writeToChild`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {Point | undefined} */ let point;\n            // Find the flow chunk.\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    point = self.events[indexBeforeFlow][1].end;\n                    break;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            let index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n            return checkNewContainers(code);\n        }\n        return start(code);\n    }\n    /** @type {State} */ function checkNewContainers(code) {\n        // Next, after consuming the continuation markers for existing blocks, we\n        // look for new block starts (e.g. `>` for a block quote).\n        // If we encounter a new block start, we close any blocks unmatched in\n        // step 1 before creating the new block as a child of the last matched\n        // block.\n        if (continued === stack.length) {\n            // No need to `check` whether there’s a container, of `exitContainers`\n            // would be moot.\n            // We can instead immediately `attempt` to parse one.\n            if (!childFlow) {\n                return documentContinued(code);\n            }\n            // If we have concrete content, such as block HTML or fenced code,\n            // we can’t have containers “pierce” into them, so we can immediately\n            // start.\n            if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n                return flowStart(code);\n            }\n            // If we do have flow, it could still be a blank line,\n            // but we’d be interrupting it w/ a new container if there’s a current\n            // construct.\n            // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n            // needed in micromark-extension-gfm-table@1.0.6).\n            self.interrupt = Boolean(childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack);\n        }\n        // Check if there is a new container.\n        self.containerState = {};\n        return effects.check(containerConstruct, thereIsANewContainer, thereIsNoNewContainer)(code);\n    }\n    /** @type {State} */ function thereIsANewContainer(code) {\n        if (childFlow) closeFlow();\n        exitContainers(continued);\n        return documentContinued(code);\n    }\n    /** @type {State} */ function thereIsNoNewContainer(code) {\n        self.parser.lazy[self.now().line] = continued !== stack.length;\n        lineStartOffset = self.now().offset;\n        return flowStart(code);\n    }\n    /** @type {State} */ function documentContinued(code) {\n        // Try new containers.\n        self.containerState = {};\n        return effects.attempt(containerConstruct, containerContinue, flowStart)(code);\n    }\n    /** @type {State} */ function containerContinue(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.currentConstruct, \"expected `currentConstruct` to be defined on tokenizer\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined on tokenizer\");\n        continued++;\n        stack.push([\n            self.currentConstruct,\n            self.containerState\n        ]);\n        // Try another.\n        return documentContinued(code);\n    }\n    /** @type {State} */ function flowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            if (childFlow) closeFlow();\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        childFlow = childFlow || self.parser.flow(self.now());\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow, {\n            _tokenizer: childFlow,\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.contentTypeFlow,\n            previous: childToken\n        });\n        return flowContinue(code);\n    }\n    /** @type {State} */ function flowContinue(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof) {\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow), true);\n            exitContainers(0);\n            effects.consume(code);\n            return;\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n            effects.consume(code);\n            writeToChild(effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow));\n            // Get ready for the next line.\n            continued = 0;\n            self.interrupt = undefined;\n            return start;\n        }\n        effects.consume(code);\n        return flowContinue;\n    }\n    /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */ function writeToChild(token, endOfFile) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when continuing\");\n        const stream = self.sliceStream(token);\n        if (endOfFile) stream.push(null);\n        token.previous = childToken;\n        if (childToken) childToken.next = token;\n        childToken = token;\n        childFlow.defineSkip(token.start);\n        childFlow.write(stream);\n        // Alright, so we just added a lazy line:\n        //\n        // ```markdown\n        // > a\n        // b.\n        //\n        // Or:\n        //\n        // > ~~~c\n        // d\n        //\n        // Or:\n        //\n        // > | e |\n        // f\n        // ```\n        //\n        // The construct in the second example (fenced code) does not accept lazy\n        // lines, so it marked itself as done at the end of its first line, and\n        // then the content construct parses `d`.\n        // Most constructs in markdown match on the first line: if the first line\n        // forms a construct, a non-lazy line can’t “unmake” it.\n        //\n        // The construct in the third example is potentially a GFM table, and\n        // those are *weird*.\n        // It *could* be a table, from the first line, if the following line\n        // matches a condition.\n        // In this case, that second line is lazy, which “unmakes” the first line\n        // and turns the whole into one content block.\n        //\n        // We’ve now parsed the non-lazy and the lazy line, and can figure out\n        // whether the lazy line started a new flow block.\n        // If it did, we exit the current containers between the two flow blocks.\n        if (self.parser.lazy[token.start.line]) {\n            let index = childFlow.events.length;\n            while(index--){\n                if (// The token starts before the line ending…\n                childFlow.events[index][1].start.offset < lineStartOffset && // …and either is not ended yet…\n                (!childFlow.events[index][1].end || // …or ends after it.\n                childFlow.events[index][1].end.offset > lineStartOffset)) {\n                    // Exit: there’s still something open, which means it’s a lazy line\n                    // part of something.\n                    return;\n                }\n            }\n            // Note: this algorithm for moving events around is similar to the\n            // algorithm when closing flow in `documentContinue`.\n            const indexBeforeExits = self.events.length;\n            let indexBeforeFlow = indexBeforeExits;\n            /** @type {boolean | undefined} */ let seen;\n            /** @type {Point | undefined} */ let point;\n            // Find the previous chunk (the one before the lazy line).\n            while(indexBeforeFlow--){\n                if (self.events[indexBeforeFlow][0] === \"exit\" && self.events[indexBeforeFlow][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow) {\n                    if (seen) {\n                        point = self.events[indexBeforeFlow][1].end;\n                        break;\n                    }\n                    seen = true;\n                }\n            }\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(point, \"could not find previous flow chunk\");\n            exitContainers(continued);\n            // Fix positions.\n            index = indexBeforeExits;\n            while(index < self.events.length){\n                self.events[index][1].end = {\n                    ...point\n                };\n                index++;\n            }\n            // Inject the exits earlier (they’re still also at the end).\n            (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n            // Discard the duplicate exits.\n            self.events.length = index;\n        }\n    }\n    /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */ function exitContainers(size) {\n        let index = stack.length;\n        // Exit open containers.\n        while(index-- > size){\n            const entry = stack[index];\n            self.containerState = entry[1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(entry[0].exit, \"expected `exit` to be defined on container construct\");\n            entry[0].exit.call(self, effects);\n        }\n        stack.length = size;\n    }\n    function closeFlow() {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, \"expected `containerState` to be defined when closing flow\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(childFlow, \"expected `childFlow` to be defined when closing it\");\n        childFlow.write([\n            micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.codes.eof\n        ]);\n        childToken = undefined;\n        childFlow = undefined;\n        self.containerState._closeFlow = undefined;\n    }\n}\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */ function tokenizeContainer(effects, ok, nok) {\n    // Always populated by defaults.\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(this.parser.constructs.disable.null, \"expected `disable.null` to be populated\");\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, effects.attempt(this.parser.constructs.document, ok, nok), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix, this.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/flow.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flow: () => (/* binding */ flow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/micromark-core-commonmark/dev/lib/content.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\n\n\n\n/** @type {InitialConstruct} */ const flow = {\n    tokenize: initializeFlow\n};\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */ function initializeFlow(effects) {\n    const self = this;\n    const initial = effects.attempt(// Try to parse a blank line.\n    micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_0__.blankLine, atBlankEnding, // Try to parse initial flow (essentially, only code).\n    effects.attempt(this.parser.constructs.flowInitial, afterConstruct, (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, effects.attempt(this.parser.constructs.flow, afterConstruct, effects.attempt(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_2__.content, afterConstruct)), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix)));\n    return initial;\n    /** @type {State} */ function atBlankEnding(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n    /** @type {State} */ function afterConstruct(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEnding)(code), \"expected eol or eof\");\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n            effects.consume(code);\n            return;\n        }\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n        self.currentConstruct = undefined;\n        return initial;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/initialize/text.js":
/*!***********************************************************!*\
  !*** ./node_modules/micromark/dev/lib/initialize/text.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolver: () => (/* binding */ resolver),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */ \n\nconst resolver = {\n    resolveAll: createResolver()\n};\nconst string = initializeFactory(\"string\");\nconst text = initializeFactory(\"text\");\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */ function initializeFactory(field) {\n    return {\n        resolveAll: createResolver(field === \"text\" ? resolveAllLineSuffixes : undefined),\n        tokenize: initializeText\n    };\n    /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */ function initializeText(effects) {\n        const self = this;\n        const constructs = this.parser.constructs[field];\n        const text = effects.attempt(constructs, start, notText);\n        return start;\n        /** @type {State} */ function start(code) {\n            return atBreak(code) ? text(code) : notText(code);\n        }\n        /** @type {State} */ function notText(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                effects.consume(code);\n                return;\n            }\n            effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n            effects.consume(code);\n            return data;\n        }\n        /** @type {State} */ function data(code) {\n            if (atBreak(code)) {\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data);\n                return text(code);\n            }\n            // Data.\n            effects.consume(code);\n            return data;\n        }\n        /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */ function atBreak(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof) {\n                return true;\n            }\n            const list = constructs[code];\n            let index = -1;\n            if (list) {\n                // Always populated by defaults.\n                (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(Array.isArray(list), \"expected `disable.null` to be populated\");\n                while(++index < list.length){\n                    const item = list[index];\n                    if (!item.previous || item.previous.call(self, self.previous)) {\n                        return true;\n                    }\n                }\n            }\n            return false;\n        }\n    }\n}\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */ function createResolver(extraResolver) {\n    return resolveAllText;\n    /** @type {Resolver} */ function resolveAllText(events, context) {\n        let index = -1;\n        /** @type {number | undefined} */ let enter;\n        // A rather boring computation (to merge adjacent `data` events) which\n        // improves mm performance by 29%.\n        while(++index <= events.length){\n            if (enter === undefined) {\n                if (events[index] && events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                    enter = index;\n                    index++;\n                }\n            } else if (!events[index] || events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n                // Don’t do anything if there is one data token.\n                if (index !== enter + 2) {\n                    events[enter][1].end = events[index - 1][1].end;\n                    events.splice(enter + 2, index - enter - 2);\n                    index = enter + 2;\n                }\n                enter = undefined;\n            }\n        }\n        return extraResolver ? extraResolver(events, context) : events;\n    }\n}\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */ function resolveAllLineSuffixes(events, context) {\n    let eventIndex = 0 // Skip first.\n    ;\n    while(++eventIndex <= events.length){\n        if ((eventIndex === events.length || events[eventIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding) && events[eventIndex - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data) {\n            const data = events[eventIndex - 1][1];\n            const chunks = context.sliceStream(data);\n            let index = chunks.length;\n            let bufferIndex = -1;\n            let size = 0;\n            /** @type {boolean | undefined} */ let tabs;\n            while(index--){\n                const chunk = chunks[index];\n                if (typeof chunk === \"string\") {\n                    bufferIndex = chunk.length;\n                    while(chunk.charCodeAt(bufferIndex - 1) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.space){\n                        size++;\n                        bufferIndex--;\n                    }\n                    if (bufferIndex) break;\n                    bufferIndex = -1;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab) {\n                    tabs = true;\n                    size++;\n                } else if (chunk === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace) {\n                // Empty\n                } else {\n                    // Replacement character, exit.\n                    index++;\n                    break;\n                }\n            }\n            // Allow final trailing whitespace.\n            if (context._contentTypeTextTrailing && eventIndex === events.length) {\n                size = 0;\n            }\n            if (size) {\n                const token = {\n                    type: eventIndex === events.length || tabs || size < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.constants.hardBreakPrefixSizeMin ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.hardBreakTrailing,\n                    start: {\n                        _bufferIndex: index ? bufferIndex : data.start._bufferIndex + bufferIndex,\n                        _index: data.start._index + index,\n                        line: data.end.line,\n                        column: data.end.column - size,\n                        offset: data.end.offset - size\n                    },\n                    end: {\n                        ...data.end\n                    }\n                };\n                data.end = {\n                    ...token.start\n                };\n                if (data.start.offset === data.end.offset) {\n                    Object.assign(data, token);\n                } else {\n                    events.splice(eventIndex, 0, [\n                        \"enter\",\n                        token,\n                        context\n                    ], [\n                        \"exit\",\n                        token,\n                        context\n                    ]);\n                    eventIndex += 2;\n                }\n            }\n            eventIndex++;\n        }\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvaW5pdGlhbGl6ZS90ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7Ozs7Ozs7O0NBU0MsR0FFa0M7QUFDMEI7QUFFdEQsTUFBTUssV0FBVztJQUFDQyxZQUFZQztBQUFnQixFQUFDO0FBQy9DLE1BQU1DLFNBQVNDLGtCQUFrQixVQUFTO0FBQzFDLE1BQU1DLE9BQU9ELGtCQUFrQixRQUFPO0FBRTdDOzs7OztDQUtDLEdBQ0QsU0FBU0Esa0JBQWtCRSxLQUFLO0lBQzlCLE9BQU87UUFDTEwsWUFBWUMsZUFDVkksVUFBVSxTQUFTQyx5QkFBeUJDO1FBRTlDQyxVQUFVQztJQUNaO0lBRUE7Ozs7R0FJQyxHQUNELFNBQVNBLGVBQWVDLE9BQU87UUFDN0IsTUFBTUMsT0FBTyxJQUFJO1FBQ2pCLE1BQU1DLGFBQWEsSUFBSSxDQUFDQyxNQUFNLENBQUNELFVBQVUsQ0FBQ1AsTUFBTTtRQUNoRCxNQUFNRCxPQUFPTSxRQUFRSSxPQUFPLENBQUNGLFlBQVlHLE9BQU9DO1FBRWhELE9BQU9EO1FBRVAsa0JBQWtCLEdBQ2xCLFNBQVNBLE1BQU1FLElBQUk7WUFDakIsT0FBT0MsUUFBUUQsUUFBUWIsS0FBS2EsUUFBUUQsUUFBUUM7UUFDOUM7UUFFQSxrQkFBa0IsR0FDbEIsU0FBU0QsUUFBUUMsSUFBSTtZQUNuQixJQUFJQSxTQUFTckIsd0RBQUtBLENBQUN1QixHQUFHLEVBQUU7Z0JBQ3RCVCxRQUFRVSxPQUFPLENBQUNIO2dCQUNoQjtZQUNGO1lBRUFQLFFBQVFXLEtBQUssQ0FBQ3ZCLHdEQUFLQSxDQUFDd0IsSUFBSTtZQUN4QlosUUFBUVUsT0FBTyxDQUFDSDtZQUNoQixPQUFPSztRQUNUO1FBRUEsa0JBQWtCLEdBQ2xCLFNBQVNBLEtBQUtMLElBQUk7WUFDaEIsSUFBSUMsUUFBUUQsT0FBTztnQkFDakJQLFFBQVFhLElBQUksQ0FBQ3pCLHdEQUFLQSxDQUFDd0IsSUFBSTtnQkFDdkIsT0FBT2xCLEtBQUthO1lBQ2Q7WUFFQSxRQUFRO1lBQ1JQLFFBQVFVLE9BQU8sQ0FBQ0g7WUFDaEIsT0FBT0s7UUFDVDtRQUVBOzs7OztLQUtDLEdBQ0QsU0FBU0osUUFBUUQsSUFBSTtZQUNuQixJQUFJQSxTQUFTckIsd0RBQUtBLENBQUN1QixHQUFHLEVBQUU7Z0JBQ3RCLE9BQU87WUFDVDtZQUVBLE1BQU1LLE9BQU9aLFVBQVUsQ0FBQ0ssS0FBSztZQUM3QixJQUFJUSxRQUFRLENBQUM7WUFFYixJQUFJRCxNQUFNO2dCQUNSLGdDQUFnQztnQkFDaEM3QiwwQ0FBTUEsQ0FBQytCLE1BQU1DLE9BQU8sQ0FBQ0gsT0FBTztnQkFFNUIsTUFBTyxFQUFFQyxRQUFRRCxLQUFLSSxNQUFNLENBQUU7b0JBQzVCLE1BQU1DLE9BQU9MLElBQUksQ0FBQ0MsTUFBTTtvQkFDeEIsSUFBSSxDQUFDSSxLQUFLQyxRQUFRLElBQUlELEtBQUtDLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDcEIsTUFBTUEsS0FBS21CLFFBQVEsR0FBRzt3QkFDN0QsT0FBTztvQkFDVDtnQkFDRjtZQUNGO1lBRUEsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQUVBOzs7OztDQUtDLEdBQ0QsU0FBUzdCLGVBQWUrQixhQUFhO0lBQ25DLE9BQU9DO0lBRVAscUJBQXFCLEdBQ3JCLFNBQVNBLGVBQWVDLE1BQU0sRUFBRUMsT0FBTztRQUNyQyxJQUFJVixRQUFRLENBQUM7UUFDYiwrQkFBK0IsR0FDL0IsSUFBSUo7UUFFSixzRUFBc0U7UUFDdEUsa0NBQWtDO1FBQ2xDLE1BQU8sRUFBRUksU0FBU1MsT0FBT04sTUFBTSxDQUFFO1lBQy9CLElBQUlQLFVBQVVkLFdBQVc7Z0JBQ3ZCLElBQUkyQixNQUFNLENBQUNULE1BQU0sSUFBSVMsTUFBTSxDQUFDVCxNQUFNLENBQUMsRUFBRSxDQUFDVyxJQUFJLEtBQUt0Qyx3REFBS0EsQ0FBQ3dCLElBQUksRUFBRTtvQkFDekRELFFBQVFJO29CQUNSQTtnQkFDRjtZQUNGLE9BQU8sSUFBSSxDQUFDUyxNQUFNLENBQUNULE1BQU0sSUFBSVMsTUFBTSxDQUFDVCxNQUFNLENBQUMsRUFBRSxDQUFDVyxJQUFJLEtBQUt0Qyx3REFBS0EsQ0FBQ3dCLElBQUksRUFBRTtnQkFDakUsZ0RBQWdEO2dCQUNoRCxJQUFJRyxVQUFVSixRQUFRLEdBQUc7b0JBQ3ZCYSxNQUFNLENBQUNiLE1BQU0sQ0FBQyxFQUFFLENBQUNnQixHQUFHLEdBQUdILE1BQU0sQ0FBQ1QsUUFBUSxFQUFFLENBQUMsRUFBRSxDQUFDWSxHQUFHO29CQUMvQ0gsT0FBT0ksTUFBTSxDQUFDakIsUUFBUSxHQUFHSSxRQUFRSixRQUFRO29CQUN6Q0ksUUFBUUosUUFBUTtnQkFDbEI7Z0JBRUFBLFFBQVFkO1lBQ1Y7UUFDRjtRQUVBLE9BQU95QixnQkFBZ0JBLGNBQWNFLFFBQVFDLFdBQVdEO0lBQzFEO0FBQ0Y7QUFFQTs7Ozs7Ozs7OztDQVVDLEdBQ0QsU0FBUzVCLHVCQUF1QjRCLE1BQU0sRUFBRUMsT0FBTztJQUM3QyxJQUFJSSxhQUFhLEVBQUUsY0FBYzs7SUFFakMsTUFBTyxFQUFFQSxjQUFjTCxPQUFPTixNQUFNLENBQUU7UUFDcEMsSUFDRSxDQUFDVyxlQUFlTCxPQUFPTixNQUFNLElBQzNCTSxNQUFNLENBQUNLLFdBQVcsQ0FBQyxFQUFFLENBQUNILElBQUksS0FBS3RDLHdEQUFLQSxDQUFDMEMsVUFBVSxLQUNqRE4sTUFBTSxDQUFDSyxhQUFhLEVBQUUsQ0FBQyxFQUFFLENBQUNILElBQUksS0FBS3RDLHdEQUFLQSxDQUFDd0IsSUFBSSxFQUM3QztZQUNBLE1BQU1BLE9BQU9ZLE1BQU0sQ0FBQ0ssYUFBYSxFQUFFLENBQUMsRUFBRTtZQUN0QyxNQUFNRSxTQUFTTixRQUFRTyxXQUFXLENBQUNwQjtZQUNuQyxJQUFJRyxRQUFRZ0IsT0FBT2IsTUFBTTtZQUN6QixJQUFJZSxjQUFjLENBQUM7WUFDbkIsSUFBSUMsT0FBTztZQUNYLGdDQUFnQyxHQUNoQyxJQUFJQztZQUVKLE1BQU9wQixRQUFTO2dCQUNkLE1BQU1xQixRQUFRTCxNQUFNLENBQUNoQixNQUFNO2dCQUUzQixJQUFJLE9BQU9xQixVQUFVLFVBQVU7b0JBQzdCSCxjQUFjRyxNQUFNbEIsTUFBTTtvQkFFMUIsTUFBT2tCLE1BQU1DLFVBQVUsQ0FBQ0osY0FBYyxPQUFPL0Msd0RBQUtBLENBQUNvRCxLQUFLLENBQUU7d0JBQ3hESjt3QkFDQUQ7b0JBQ0Y7b0JBRUEsSUFBSUEsYUFBYTtvQkFDakJBLGNBQWMsQ0FBQztnQkFDakIsT0FFSyxJQUFJRyxVQUFVbEQsd0RBQUtBLENBQUNxRCxhQUFhLEVBQUU7b0JBQ3RDSixPQUFPO29CQUNQRDtnQkFDRixPQUFPLElBQUlFLFVBQVVsRCx3REFBS0EsQ0FBQ3NELFlBQVksRUFBRTtnQkFDdkMsUUFBUTtnQkFDVixPQUFPO29CQUNMLCtCQUErQjtvQkFDL0J6QjtvQkFDQTtnQkFDRjtZQUNGO1lBRUEsbUNBQW1DO1lBQ25DLElBQUlVLFFBQVFnQix3QkFBd0IsSUFBSVosZUFBZUwsT0FBT04sTUFBTSxFQUFFO2dCQUNwRWdCLE9BQU87WUFDVDtZQUVBLElBQUlBLE1BQU07Z0JBQ1IsTUFBTVEsUUFBUTtvQkFDWmhCLE1BQ0VHLGVBQWVMLE9BQU9OLE1BQU0sSUFDNUJpQixRQUNBRCxPQUFPL0MsNERBQVNBLENBQUN3RCxzQkFBc0IsR0FDbkN2RCx3REFBS0EsQ0FBQ3dELFVBQVUsR0FDaEJ4RCx3REFBS0EsQ0FBQ3lELGlCQUFpQjtvQkFDN0J4QyxPQUFPO3dCQUNMeUMsY0FBYy9CLFFBQ1ZrQixjQUNBckIsS0FBS1AsS0FBSyxDQUFDeUMsWUFBWSxHQUFHYjt3QkFDOUJjLFFBQVFuQyxLQUFLUCxLQUFLLENBQUMwQyxNQUFNLEdBQUdoQzt3QkFDNUJpQyxNQUFNcEMsS0FBS2UsR0FBRyxDQUFDcUIsSUFBSTt3QkFDbkJDLFFBQVFyQyxLQUFLZSxHQUFHLENBQUNzQixNQUFNLEdBQUdmO3dCQUMxQmdCLFFBQVF0QyxLQUFLZSxHQUFHLENBQUN1QixNQUFNLEdBQUdoQjtvQkFDNUI7b0JBQ0FQLEtBQUs7d0JBQUMsR0FBR2YsS0FBS2UsR0FBRztvQkFBQTtnQkFDbkI7Z0JBRUFmLEtBQUtlLEdBQUcsR0FBRztvQkFBQyxHQUFHZSxNQUFNckMsS0FBSztnQkFBQTtnQkFFMUIsSUFBSU8sS0FBS1AsS0FBSyxDQUFDNkMsTUFBTSxLQUFLdEMsS0FBS2UsR0FBRyxDQUFDdUIsTUFBTSxFQUFFO29CQUN6Q0MsT0FBT0MsTUFBTSxDQUFDeEMsTUFBTThCO2dCQUN0QixPQUFPO29CQUNMbEIsT0FBT0ksTUFBTSxDQUNYQyxZQUNBLEdBQ0E7d0JBQUM7d0JBQVNhO3dCQUFPakI7cUJBQVEsRUFDekI7d0JBQUM7d0JBQVFpQjt3QkFBT2pCO3FCQUFRO29CQUUxQkksY0FBYztnQkFDaEI7WUFDRjtZQUVBQTtRQUNGO0lBQ0Y7SUFFQSxPQUFPTDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGV2L2xpYi9pbml0aWFsaXplL3RleHQuanM/MmRiZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge1xuICogICBDb2RlLFxuICogICBJbml0aWFsQ29uc3RydWN0LFxuICogICBJbml0aWFsaXplcixcbiAqICAgUmVzb2x2ZXIsXG4gKiAgIFN0YXRlLFxuICogICBUb2tlbml6ZUNvbnRleHRcbiAqIH0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtvayBhcyBhc3NlcnR9IGZyb20gJ2RldmxvcCdcbmltcG9ydCB7Y29kZXMsIGNvbnN0YW50cywgdHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbCdcblxuZXhwb3J0IGNvbnN0IHJlc29sdmVyID0ge3Jlc29sdmVBbGw6IGNyZWF0ZVJlc29sdmVyKCl9XG5leHBvcnQgY29uc3Qgc3RyaW5nID0gaW5pdGlhbGl6ZUZhY3RvcnkoJ3N0cmluZycpXG5leHBvcnQgY29uc3QgdGV4dCA9IGluaXRpYWxpemVGYWN0b3J5KCd0ZXh0JylcblxuLyoqXG4gKiBAcGFyYW0geydzdHJpbmcnIHwgJ3RleHQnfSBmaWVsZFxuICogICBGaWVsZC5cbiAqIEByZXR1cm5zIHtJbml0aWFsQ29uc3RydWN0fVxuICogICBDb25zdHJ1Y3QuXG4gKi9cbmZ1bmN0aW9uIGluaXRpYWxpemVGYWN0b3J5KGZpZWxkKSB7XG4gIHJldHVybiB7XG4gICAgcmVzb2x2ZUFsbDogY3JlYXRlUmVzb2x2ZXIoXG4gICAgICBmaWVsZCA9PT0gJ3RleHQnID8gcmVzb2x2ZUFsbExpbmVTdWZmaXhlcyA6IHVuZGVmaW5lZFxuICAgICksXG4gICAgdG9rZW5pemU6IGluaXRpYWxpemVUZXh0XG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAgICogICBDb250ZXh0LlxuICAgKiBAdHlwZSB7SW5pdGlhbGl6ZXJ9XG4gICAqL1xuICBmdW5jdGlvbiBpbml0aWFsaXplVGV4dChlZmZlY3RzKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgICBjb25zdCBjb25zdHJ1Y3RzID0gdGhpcy5wYXJzZXIuY29uc3RydWN0c1tmaWVsZF1cbiAgICBjb25zdCB0ZXh0ID0gZWZmZWN0cy5hdHRlbXB0KGNvbnN0cnVjdHMsIHN0YXJ0LCBub3RUZXh0KVxuXG4gICAgcmV0dXJuIHN0YXJ0XG5cbiAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICAgIHJldHVybiBhdEJyZWFrKGNvZGUpID8gdGV4dChjb2RlKSA6IG5vdFRleHQoY29kZSlcbiAgICB9XG5cbiAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgIGZ1bmN0aW9uIG5vdFRleHQoY29kZSkge1xuICAgICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZikge1xuICAgICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuZGF0YSlcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgcmV0dXJuIGRhdGFcbiAgICB9XG5cbiAgICAvKiogQHR5cGUge1N0YXRlfSAqL1xuICAgIGZ1bmN0aW9uIGRhdGEoY29kZSkge1xuICAgICAgaWYgKGF0QnJlYWsoY29kZSkpIHtcbiAgICAgICAgZWZmZWN0cy5leGl0KHR5cGVzLmRhdGEpXG4gICAgICAgIHJldHVybiB0ZXh0KGNvZGUpXG4gICAgICB9XG5cbiAgICAgIC8vIERhdGEuXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBkYXRhXG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQHBhcmFtIHtDb2RlfSBjb2RlXG4gICAgICogICBDb2RlLlxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgICAqICAgV2hldGhlciB0aGUgY29kZSBpcyBhIGJyZWFrLlxuICAgICAqL1xuICAgIGZ1bmN0aW9uIGF0QnJlYWsoY29kZSkge1xuICAgICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZikge1xuICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgfVxuXG4gICAgICBjb25zdCBsaXN0ID0gY29uc3RydWN0c1tjb2RlXVxuICAgICAgbGV0IGluZGV4ID0gLTFcblxuICAgICAgaWYgKGxpc3QpIHtcbiAgICAgICAgLy8gQWx3YXlzIHBvcHVsYXRlZCBieSBkZWZhdWx0cy5cbiAgICAgICAgYXNzZXJ0KEFycmF5LmlzQXJyYXkobGlzdCksICdleHBlY3RlZCBgZGlzYWJsZS5udWxsYCB0byBiZSBwb3B1bGF0ZWQnKVxuXG4gICAgICAgIHdoaWxlICgrK2luZGV4IDwgbGlzdC5sZW5ndGgpIHtcbiAgICAgICAgICBjb25zdCBpdGVtID0gbGlzdFtpbmRleF1cbiAgICAgICAgICBpZiAoIWl0ZW0ucHJldmlvdXMgfHwgaXRlbS5wcmV2aW91cy5jYWxsKHNlbGYsIHNlbGYucHJldmlvdXMpKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBAcGFyYW0ge1Jlc29sdmVyIHwgdW5kZWZpbmVkfSBbZXh0cmFSZXNvbHZlcl1cbiAqICAgUmVzb2x2ZXIuXG4gKiBAcmV0dXJucyB7UmVzb2x2ZXJ9XG4gKiAgIFJlc29sdmVyLlxuICovXG5mdW5jdGlvbiBjcmVhdGVSZXNvbHZlcihleHRyYVJlc29sdmVyKSB7XG4gIHJldHVybiByZXNvbHZlQWxsVGV4dFxuXG4gIC8qKiBAdHlwZSB7UmVzb2x2ZXJ9ICovXG4gIGZ1bmN0aW9uIHJlc29sdmVBbGxUZXh0KGV2ZW50cywgY29udGV4dCkge1xuICAgIGxldCBpbmRleCA9IC0xXG4gICAgLyoqIEB0eXBlIHtudW1iZXIgfCB1bmRlZmluZWR9ICovXG4gICAgbGV0IGVudGVyXG5cbiAgICAvLyBBIHJhdGhlciBib3JpbmcgY29tcHV0YXRpb24gKHRvIG1lcmdlIGFkamFjZW50IGBkYXRhYCBldmVudHMpIHdoaWNoXG4gICAgLy8gaW1wcm92ZXMgbW0gcGVyZm9ybWFuY2UgYnkgMjklLlxuICAgIHdoaWxlICgrK2luZGV4IDw9IGV2ZW50cy5sZW5ndGgpIHtcbiAgICAgIGlmIChlbnRlciA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGlmIChldmVudHNbaW5kZXhdICYmIGV2ZW50c1tpbmRleF1bMV0udHlwZSA9PT0gdHlwZXMuZGF0YSkge1xuICAgICAgICAgIGVudGVyID0gaW5kZXhcbiAgICAgICAgICBpbmRleCsrXG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoIWV2ZW50c1tpbmRleF0gfHwgZXZlbnRzW2luZGV4XVsxXS50eXBlICE9PSB0eXBlcy5kYXRhKSB7XG4gICAgICAgIC8vIERvbuKAmXQgZG8gYW55dGhpbmcgaWYgdGhlcmUgaXMgb25lIGRhdGEgdG9rZW4uXG4gICAgICAgIGlmIChpbmRleCAhPT0gZW50ZXIgKyAyKSB7XG4gICAgICAgICAgZXZlbnRzW2VudGVyXVsxXS5lbmQgPSBldmVudHNbaW5kZXggLSAxXVsxXS5lbmRcbiAgICAgICAgICBldmVudHMuc3BsaWNlKGVudGVyICsgMiwgaW5kZXggLSBlbnRlciAtIDIpXG4gICAgICAgICAgaW5kZXggPSBlbnRlciArIDJcbiAgICAgICAgfVxuXG4gICAgICAgIGVudGVyID0gdW5kZWZpbmVkXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIGV4dHJhUmVzb2x2ZXIgPyBleHRyYVJlc29sdmVyKGV2ZW50cywgY29udGV4dCkgOiBldmVudHNcbiAgfVxufVxuXG4vKipcbiAqIEEgcmF0aGVyIHVnbHkgc2V0IG9mIGluc3RydWN0aW9ucyB3aGljaCBhZ2FpbiBsb29rcyBhdCBjaHVua3MgaW4gdGhlIGlucHV0XG4gKiBzdHJlYW0uXG4gKiBUaGUgcmVhc29uIHRvIGRvIHRoaXMgaGVyZSBpcyB0aGF0IGl0IGlzICptdWNoKiBmYXN0ZXIgdG8gcGFyc2UgaW4gcmV2ZXJzZS5cbiAqIEFuZCB0aGF0IHdlIGNhbuKAmXQgaG9vayBpbnRvIGBudWxsYCB0byBzcGxpdCB0aGUgbGluZSBzdWZmaXggYmVmb3JlIGFuIEVPRi5cbiAqIFRvIGRvOiBmaWd1cmUgb3V0IGlmIHdlIGNhbiBtYWtlIHRoaXMgaW50byBhIGNsZWFuIHV0aWxpdHksIG9yIGV2ZW4gaW4gY29yZS5cbiAqIEFzIGl0IHdpbGwgYmUgdXNlZnVsIGZvciBHRk1zIGxpdGVyYWwgYXV0b2xpbmsgZXh0ZW5zaW9uIChhbmQgbWF5YmUgZXZlblxuICogdGFibGVzPylcbiAqXG4gKiBAdHlwZSB7UmVzb2x2ZXJ9XG4gKi9cbmZ1bmN0aW9uIHJlc29sdmVBbGxMaW5lU3VmZml4ZXMoZXZlbnRzLCBjb250ZXh0KSB7XG4gIGxldCBldmVudEluZGV4ID0gMCAvLyBTa2lwIGZpcnN0LlxuXG4gIHdoaWxlICgrK2V2ZW50SW5kZXggPD0gZXZlbnRzLmxlbmd0aCkge1xuICAgIGlmIChcbiAgICAgIChldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoIHx8XG4gICAgICAgIGV2ZW50c1tldmVudEluZGV4XVsxXS50eXBlID09PSB0eXBlcy5saW5lRW5kaW5nKSAmJlxuICAgICAgZXZlbnRzW2V2ZW50SW5kZXggLSAxXVsxXS50eXBlID09PSB0eXBlcy5kYXRhXG4gICAgKSB7XG4gICAgICBjb25zdCBkYXRhID0gZXZlbnRzW2V2ZW50SW5kZXggLSAxXVsxXVxuICAgICAgY29uc3QgY2h1bmtzID0gY29udGV4dC5zbGljZVN0cmVhbShkYXRhKVxuICAgICAgbGV0IGluZGV4ID0gY2h1bmtzLmxlbmd0aFxuICAgICAgbGV0IGJ1ZmZlckluZGV4ID0gLTFcbiAgICAgIGxldCBzaXplID0gMFxuICAgICAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICAgICAgbGV0IHRhYnNcblxuICAgICAgd2hpbGUgKGluZGV4LS0pIHtcbiAgICAgICAgY29uc3QgY2h1bmsgPSBjaHVua3NbaW5kZXhdXG5cbiAgICAgICAgaWYgKHR5cGVvZiBjaHVuayA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICBidWZmZXJJbmRleCA9IGNodW5rLmxlbmd0aFxuXG4gICAgICAgICAgd2hpbGUgKGNodW5rLmNoYXJDb2RlQXQoYnVmZmVySW5kZXggLSAxKSA9PT0gY29kZXMuc3BhY2UpIHtcbiAgICAgICAgICAgIHNpemUrK1xuICAgICAgICAgICAgYnVmZmVySW5kZXgtLVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGlmIChidWZmZXJJbmRleCkgYnJlYWtcbiAgICAgICAgICBidWZmZXJJbmRleCA9IC0xXG4gICAgICAgIH1cbiAgICAgICAgLy8gTnVtYmVyXG4gICAgICAgIGVsc2UgaWYgKGNodW5rID09PSBjb2Rlcy5ob3Jpem9udGFsVGFiKSB7XG4gICAgICAgICAgdGFicyA9IHRydWVcbiAgICAgICAgICBzaXplKytcbiAgICAgICAgfSBlbHNlIGlmIChjaHVuayA9PT0gY29kZXMudmlydHVhbFNwYWNlKSB7XG4gICAgICAgICAgLy8gRW1wdHlcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBSZXBsYWNlbWVudCBjaGFyYWN0ZXIsIGV4aXQuXG4gICAgICAgICAgaW5kZXgrK1xuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gQWxsb3cgZmluYWwgdHJhaWxpbmcgd2hpdGVzcGFjZS5cbiAgICAgIGlmIChjb250ZXh0Ll9jb250ZW50VHlwZVRleHRUcmFpbGluZyAmJiBldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoKSB7XG4gICAgICAgIHNpemUgPSAwXG4gICAgICB9XG5cbiAgICAgIGlmIChzaXplKSB7XG4gICAgICAgIGNvbnN0IHRva2VuID0ge1xuICAgICAgICAgIHR5cGU6XG4gICAgICAgICAgICBldmVudEluZGV4ID09PSBldmVudHMubGVuZ3RoIHx8XG4gICAgICAgICAgICB0YWJzIHx8XG4gICAgICAgICAgICBzaXplIDwgY29uc3RhbnRzLmhhcmRCcmVha1ByZWZpeFNpemVNaW5cbiAgICAgICAgICAgICAgPyB0eXBlcy5saW5lU3VmZml4XG4gICAgICAgICAgICAgIDogdHlwZXMuaGFyZEJyZWFrVHJhaWxpbmcsXG4gICAgICAgICAgc3RhcnQ6IHtcbiAgICAgICAgICAgIF9idWZmZXJJbmRleDogaW5kZXhcbiAgICAgICAgICAgICAgPyBidWZmZXJJbmRleFxuICAgICAgICAgICAgICA6IGRhdGEuc3RhcnQuX2J1ZmZlckluZGV4ICsgYnVmZmVySW5kZXgsXG4gICAgICAgICAgICBfaW5kZXg6IGRhdGEuc3RhcnQuX2luZGV4ICsgaW5kZXgsXG4gICAgICAgICAgICBsaW5lOiBkYXRhLmVuZC5saW5lLFxuICAgICAgICAgICAgY29sdW1uOiBkYXRhLmVuZC5jb2x1bW4gLSBzaXplLFxuICAgICAgICAgICAgb2Zmc2V0OiBkYXRhLmVuZC5vZmZzZXQgLSBzaXplXG4gICAgICAgICAgfSxcbiAgICAgICAgICBlbmQ6IHsuLi5kYXRhLmVuZH1cbiAgICAgICAgfVxuXG4gICAgICAgIGRhdGEuZW5kID0gey4uLnRva2VuLnN0YXJ0fVxuXG4gICAgICAgIGlmIChkYXRhLnN0YXJ0Lm9mZnNldCA9PT0gZGF0YS5lbmQub2Zmc2V0KSB7XG4gICAgICAgICAgT2JqZWN0LmFzc2lnbihkYXRhLCB0b2tlbilcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBldmVudHMuc3BsaWNlKFxuICAgICAgICAgICAgZXZlbnRJbmRleCxcbiAgICAgICAgICAgIDAsXG4gICAgICAgICAgICBbJ2VudGVyJywgdG9rZW4sIGNvbnRleHRdLFxuICAgICAgICAgICAgWydleGl0JywgdG9rZW4sIGNvbnRleHRdXG4gICAgICAgICAgKVxuICAgICAgICAgIGV2ZW50SW5kZXggKz0gMlxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGV2ZW50SW5kZXgrK1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBldmVudHNcbn1cbiJdLCJuYW1lcyI6WyJvayIsImFzc2VydCIsImNvZGVzIiwiY29uc3RhbnRzIiwidHlwZXMiLCJyZXNvbHZlciIsInJlc29sdmVBbGwiLCJjcmVhdGVSZXNvbHZlciIsInN0cmluZyIsImluaXRpYWxpemVGYWN0b3J5IiwidGV4dCIsImZpZWxkIiwicmVzb2x2ZUFsbExpbmVTdWZmaXhlcyIsInVuZGVmaW5lZCIsInRva2VuaXplIiwiaW5pdGlhbGl6ZVRleHQiLCJlZmZlY3RzIiwic2VsZiIsImNvbnN0cnVjdHMiLCJwYXJzZXIiLCJhdHRlbXB0Iiwic3RhcnQiLCJub3RUZXh0IiwiY29kZSIsImF0QnJlYWsiLCJlb2YiLCJjb25zdW1lIiwiZW50ZXIiLCJkYXRhIiwiZXhpdCIsImxpc3QiLCJpbmRleCIsIkFycmF5IiwiaXNBcnJheSIsImxlbmd0aCIsIml0ZW0iLCJwcmV2aW91cyIsImNhbGwiLCJleHRyYVJlc29sdmVyIiwicmVzb2x2ZUFsbFRleHQiLCJldmVudHMiLCJjb250ZXh0IiwidHlwZSIsImVuZCIsInNwbGljZSIsImV2ZW50SW5kZXgiLCJsaW5lRW5kaW5nIiwiY2h1bmtzIiwic2xpY2VTdHJlYW0iLCJidWZmZXJJbmRleCIsInNpemUiLCJ0YWJzIiwiY2h1bmsiLCJjaGFyQ29kZUF0Iiwic3BhY2UiLCJob3Jpem9udGFsVGFiIiwidmlydHVhbFNwYWNlIiwiX2NvbnRlbnRUeXBlVGV4dFRyYWlsaW5nIiwidG9rZW4iLCJoYXJkQnJlYWtQcmVmaXhTaXplTWluIiwibGluZVN1ZmZpeCIsImhhcmRCcmVha1RyYWlsaW5nIiwiX2J1ZmZlckluZGV4IiwiX2luZGV4IiwibGluZSIsImNvbHVtbiIsIm9mZnNldCIsIk9iamVjdCIsImFzc2lnbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/micromark/dev/lib/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-combine-extensions */ \"(ssr)/./node_modules/micromark-util-combine-extensions/index.js\");\n/* harmony import */ var _initialize_content_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initialize/content.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/content.js\");\n/* harmony import */ var _initialize_document_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initialize/document.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/document.js\");\n/* harmony import */ var _initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./initialize/flow.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/flow.js\");\n/* harmony import */ var _initialize_text_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./initialize/text.js */ \"(ssr)/./node_modules/micromark/dev/lib/initialize/text.js\");\n/* harmony import */ var _constructs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constructs.js */ \"(ssr)/./node_modules/micromark/dev/lib/constructs.js\");\n/* harmony import */ var _create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./create-tokenizer.js */ \"(ssr)/./node_modules/micromark/dev/lib/create-tokenizer.js\");\n/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */ \n\n\n\n\n\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */ function parse(options) {\n    const settings = options || {};\n    const constructs = /** @type {FullNormalizedExtension} */ (0,micromark_util_combine_extensions__WEBPACK_IMPORTED_MODULE_0__.combineExtensions)([\n        _constructs_js__WEBPACK_IMPORTED_MODULE_1__,\n        ...settings.extensions || []\n    ]);\n    /** @type {ParseContext} */ const parser = {\n        constructs,\n        content: create(_initialize_content_js__WEBPACK_IMPORTED_MODULE_2__.content),\n        defined: [],\n        document: create(_initialize_document_js__WEBPACK_IMPORTED_MODULE_3__.document),\n        flow: create(_initialize_flow_js__WEBPACK_IMPORTED_MODULE_4__.flow),\n        lazy: {},\n        string: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.string),\n        text: create(_initialize_text_js__WEBPACK_IMPORTED_MODULE_5__.text)\n    };\n    return parser;\n    /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */ function create(initial) {\n        return creator;\n        /** @type {Create} */ function creator(from) {\n            return (0,_create_tokenizer_js__WEBPACK_IMPORTED_MODULE_6__.createTokenizer)(parser, initial, from);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/postprocess.js":
/*!*******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/postprocess.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   postprocess: () => (/* binding */ postprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/micromark-util-subtokenize/dev/index.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ \n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */ function postprocess(events) {\n    while(!(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_0__.subtokenize)(events)){\n    // Empty\n    }\n    return events;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrL2Rldi9saWIvcG9zdHByb2Nlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7Q0FFQyxHQUVxRDtBQUV0RDs7Ozs7Q0FLQyxHQUNNLFNBQVNDLFlBQVlDLE1BQU07SUFDaEMsTUFBTyxDQUFDRix1RUFBV0EsQ0FBQ0UsUUFBUztJQUMzQixRQUFRO0lBQ1Y7SUFFQSxPQUFPQTtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9taWNyb21hcmsvZGV2L2xpYi9wb3N0cHJvY2Vzcy5qcz83YjA3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7RXZlbnR9IGZyb20gJ21pY3JvbWFyay11dGlsLXR5cGVzJ1xuICovXG5cbmltcG9ydCB7c3VidG9rZW5pemV9IGZyb20gJ21pY3JvbWFyay11dGlsLXN1YnRva2VuaXplJ1xuXG4vKipcbiAqIEBwYXJhbSB7QXJyYXk8RXZlbnQ+fSBldmVudHNcbiAqICAgRXZlbnRzLlxuICogQHJldHVybnMge0FycmF5PEV2ZW50Pn1cbiAqICAgRXZlbnRzLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcG9zdHByb2Nlc3MoZXZlbnRzKSB7XG4gIHdoaWxlICghc3VidG9rZW5pemUoZXZlbnRzKSkge1xuICAgIC8vIEVtcHR5XG4gIH1cblxuICByZXR1cm4gZXZlbnRzXG59XG4iXSwibmFtZXMiOlsic3VidG9rZW5pemUiLCJwb3N0cHJvY2VzcyIsImV2ZW50cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/postprocess.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark/dev/lib/preprocess.js":
/*!******************************************************!*\
  !*** ./node_modules/micromark/dev/lib/preprocess.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preprocess: () => (/* binding */ preprocess)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */ /**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */ \nconst search = /[\\0\\t\\n\\r]/g;\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */ function preprocess() {\n    let column = 1;\n    let buffer = \"\";\n    /** @type {boolean | undefined} */ let start = true;\n    /** @type {boolean | undefined} */ let atCarriageReturn;\n    return preprocessor;\n    /** @type {Preprocessor} */ // eslint-disable-next-line complexity\n    function preprocessor(value, encoding, end) {\n        /** @type {Array<Chunk>} */ const chunks = [];\n        /** @type {RegExpMatchArray | null} */ let match;\n        /** @type {number} */ let next;\n        /** @type {number} */ let startPosition;\n        /** @type {number} */ let endPosition;\n        /** @type {Code} */ let code;\n        value = buffer + (typeof value === \"string\" ? value.toString() : new TextDecoder(encoding || undefined).decode(value));\n        startPosition = 0;\n        buffer = \"\";\n        if (start) {\n            // To do: `markdown-rs` actually parses BOMs (byte order mark).\n            if (value.charCodeAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.byteOrderMarker) {\n                startPosition++;\n            }\n            start = undefined;\n        }\n        while(startPosition < value.length){\n            search.lastIndex = startPosition;\n            match = search.exec(value);\n            endPosition = match && match.index !== undefined ? match.index : value.length;\n            code = value.charCodeAt(endPosition);\n            if (!match) {\n                buffer = value.slice(startPosition);\n                break;\n            }\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf && startPosition === endPosition && atCarriageReturn) {\n                chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturnLineFeed);\n                atCarriageReturn = undefined;\n            } else {\n                if (atCarriageReturn) {\n                    chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n                    atCarriageReturn = undefined;\n                }\n                if (startPosition < endPosition) {\n                    chunks.push(value.slice(startPosition, endPosition));\n                    column += endPosition - startPosition;\n                }\n                switch(code){\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.nul:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.replacementCharacter);\n                            column++;\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.ht:\n                        {\n                            next = Math.ceil(column / micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize) * micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.constants.tabSize;\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.horizontalTab);\n                            while(column++ < next)chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.virtualSpace);\n                            break;\n                        }\n                    case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lf:\n                        {\n                            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.lineFeed);\n                            column = 1;\n                            break;\n                        }\n                    default:\n                        {\n                            atCarriageReturn = true;\n                            column = 1;\n                        }\n                }\n            }\n            startPosition = endPosition + 1;\n        }\n        if (end) {\n            if (atCarriageReturn) chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.carriageReturn);\n            if (buffer) chunks.push(buffer);\n            chunks.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof);\n        }\n        return chunks;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark/dev/lib/preprocess.js\n");

/***/ })

};
;