"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-textarea-autosize";
exports.ids = ["vendor-chunks/react-textarea-autosize"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var use_latest__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-latest */ \"(ssr)/./node_modules/use-latest/dist/use-latest.esm.js\");\n/* harmony import */ var use_composed_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-composed-ref */ \"(ssr)/./node_modules/use-composed-ref/dist/use-composed-ref.esm.js\");\n\n\n\n\n\nvar isBrowser = typeof document !== \"undefined\";\nvar HIDDEN_TEXTAREA_STYLE = {\n    \"min-height\": \"0\",\n    \"max-height\": \"none\",\n    height: \"0\",\n    visibility: \"hidden\",\n    overflow: \"hidden\",\n    position: \"absolute\",\n    \"z-index\": \"-1000\",\n    top: \"0\",\n    right: \"0\",\n    display: \"block\"\n};\nvar forceHiddenStyles = function forceHiddenStyles(node) {\n    Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function(key) {\n        node.style.setProperty(key, HIDDEN_TEXTAREA_STYLE[key], \"important\");\n    });\n};\nvar forceHiddenStyles$1 = forceHiddenStyles;\nvar hiddenTextarea = null;\nvar getHeight = function getHeight(node, sizingData) {\n    var height = node.scrollHeight;\n    if (sizingData.sizingStyle.boxSizing === \"border-box\") {\n        // border-box: add border, since height = content + padding + border\n        return height + sizingData.borderSize;\n    }\n    // remove padding, since height = content\n    return height - sizingData.paddingSize;\n};\nfunction calculateNodeHeight(sizingData, value, minRows, maxRows) {\n    if (minRows === void 0) {\n        minRows = 1;\n    }\n    if (maxRows === void 0) {\n        maxRows = Infinity;\n    }\n    if (!hiddenTextarea) {\n        hiddenTextarea = document.createElement(\"textarea\");\n        hiddenTextarea.setAttribute(\"tabindex\", \"-1\");\n        hiddenTextarea.setAttribute(\"aria-hidden\", \"true\");\n        forceHiddenStyles$1(hiddenTextarea);\n    }\n    if (hiddenTextarea.parentNode === null) {\n        document.body.appendChild(hiddenTextarea);\n    }\n    var paddingSize = sizingData.paddingSize, borderSize = sizingData.borderSize, sizingStyle = sizingData.sizingStyle;\n    var boxSizing = sizingStyle.boxSizing;\n    Object.keys(sizingStyle).forEach(function(_key) {\n        var key = _key;\n        hiddenTextarea.style[key] = sizingStyle[key];\n    });\n    forceHiddenStyles$1(hiddenTextarea);\n    hiddenTextarea.value = value;\n    var height = getHeight(hiddenTextarea, sizingData);\n    // Double set and calc due to Firefox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1795904\n    hiddenTextarea.value = value;\n    height = getHeight(hiddenTextarea, sizingData);\n    // measure height of a textarea with a single row\n    hiddenTextarea.value = \"x\";\n    var rowHeight = hiddenTextarea.scrollHeight - paddingSize;\n    var minHeight = rowHeight * minRows;\n    if (boxSizing === \"border-box\") {\n        minHeight = minHeight + paddingSize + borderSize;\n    }\n    height = Math.max(minHeight, height);\n    var maxHeight = rowHeight * maxRows;\n    if (boxSizing === \"border-box\") {\n        maxHeight = maxHeight + paddingSize + borderSize;\n    }\n    height = Math.min(maxHeight, height);\n    return [\n        height,\n        rowHeight\n    ];\n}\nvar noop = function noop() {};\nvar pick = function pick(props, obj) {\n    return props.reduce(function(acc, prop) {\n        acc[prop] = obj[prop];\n        return acc;\n    }, {});\n};\nvar SIZING_STYLE = [\n    \"borderBottomWidth\",\n    \"borderLeftWidth\",\n    \"borderRightWidth\",\n    \"borderTopWidth\",\n    \"boxSizing\",\n    \"fontFamily\",\n    \"fontSize\",\n    \"fontStyle\",\n    \"fontWeight\",\n    \"letterSpacing\",\n    \"lineHeight\",\n    \"paddingBottom\",\n    \"paddingLeft\",\n    \"paddingRight\",\n    \"paddingTop\",\n    // non-standard\n    \"tabSize\",\n    \"textIndent\",\n    // non-standard\n    \"textRendering\",\n    \"textTransform\",\n    \"width\",\n    \"wordBreak\",\n    \"wordSpacing\",\n    \"scrollbarGutter\"\n];\nvar isIE = isBrowser ? !!document.documentElement.currentStyle : false;\nvar getSizingData = function getSizingData(node) {\n    var style = window.getComputedStyle(node);\n    if (style === null) {\n        return null;\n    }\n    var sizingStyle = pick(SIZING_STYLE, style);\n    var boxSizing = sizingStyle.boxSizing;\n    // probably node is detached from DOM, can't read computed dimensions\n    if (boxSizing === \"\") {\n        return null;\n    }\n    // IE (Edge has already correct behaviour) returns content width as computed width\n    // so we need to add manually padding and border widths\n    if (isIE && boxSizing === \"border-box\") {\n        sizingStyle.width = parseFloat(sizingStyle.width) + parseFloat(sizingStyle.borderRightWidth) + parseFloat(sizingStyle.borderLeftWidth) + parseFloat(sizingStyle.paddingRight) + parseFloat(sizingStyle.paddingLeft) + \"px\";\n    }\n    var paddingSize = parseFloat(sizingStyle.paddingBottom) + parseFloat(sizingStyle.paddingTop);\n    var borderSize = parseFloat(sizingStyle.borderBottomWidth) + parseFloat(sizingStyle.borderTopWidth);\n    return {\n        sizingStyle: sizingStyle,\n        paddingSize: paddingSize,\n        borderSize: borderSize\n    };\n};\nvar getSizingData$1 = getSizingData;\nfunction useListener(target, type, listener) {\n    var latestListener = (0,use_latest__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(listener);\n    react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(function() {\n        var handler = function handler(ev) {\n            return latestListener.current(ev);\n        };\n        // might happen if document.fonts is not defined, for instance\n        if (!target) {\n            return;\n        }\n        target.addEventListener(type, handler);\n        return function() {\n            return target.removeEventListener(type, handler);\n        };\n    }, []);\n}\nvar useFormResetListener = function useFormResetListener(libRef, listener) {\n    useListener(document.body, \"reset\", function(ev) {\n        if (libRef.current.form === ev.target) {\n            listener(ev);\n        }\n    });\n};\nvar useWindowResizeListener = function useWindowResizeListener(listener) {\n    useListener(window, \"resize\", listener);\n};\nvar useFontsLoadedListener = function useFontsLoadedListener(listener) {\n    useListener(document.fonts, \"loadingdone\", listener);\n};\nvar _excluded = [\n    \"cacheMeasurements\",\n    \"maxRows\",\n    \"minRows\",\n    \"onChange\",\n    \"onHeightChange\"\n];\nvar TextareaAutosize = function TextareaAutosize(_ref, userRef) {\n    var cacheMeasurements = _ref.cacheMeasurements, maxRows = _ref.maxRows, minRows = _ref.minRows, _ref$onChange = _ref.onChange, onChange = _ref$onChange === void 0 ? noop : _ref$onChange, _ref$onHeightChange = _ref.onHeightChange, onHeightChange = _ref$onHeightChange === void 0 ? noop : _ref$onHeightChange, props = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, _excluded);\n    if (props.style) {\n        if (\"maxHeight\" in props.style) {\n            throw new Error(\"Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.\");\n        }\n        if (\"minHeight\" in props.style) {\n            throw new Error(\"Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.\");\n        }\n    }\n    var isControlled = props.value !== undefined;\n    var libRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    var ref = (0,use_composed_ref__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(libRef, userRef);\n    var heightRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(0);\n    var measurementsCacheRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef();\n    var resizeTextarea = function resizeTextarea() {\n        var node = libRef.current;\n        var nodeSizingData = cacheMeasurements && measurementsCacheRef.current ? measurementsCacheRef.current : getSizingData$1(node);\n        if (!nodeSizingData) {\n            return;\n        }\n        measurementsCacheRef.current = nodeSizingData;\n        var _calculateNodeHeight = calculateNodeHeight(nodeSizingData, node.value || node.placeholder || \"x\", minRows, maxRows), height = _calculateNodeHeight[0], rowHeight = _calculateNodeHeight[1];\n        if (heightRef.current !== height) {\n            heightRef.current = height;\n            node.style.setProperty(\"height\", height + \"px\", \"important\");\n            onHeightChange(height, {\n                rowHeight: rowHeight\n            });\n        }\n    };\n    var handleChange = function handleChange(event) {\n        if (!isControlled) {\n            resizeTextarea();\n        }\n        onChange(event);\n    };\n    if (isBrowser) {\n        react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(resizeTextarea);\n        useFormResetListener(libRef, function() {\n            if (!isControlled) {\n                var currentValue = libRef.current.value;\n                requestAnimationFrame(function() {\n                    var node = libRef.current;\n                    if (node && currentValue !== node.value) {\n                        resizeTextarea();\n                    }\n                });\n            }\n        });\n        useWindowResizeListener(resizeTextarea);\n        useFontsLoadedListener(resizeTextarea);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n            onChange: handleChange,\n            ref: ref\n        }));\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n        onChange: onChange,\n        ref: ref\n    }));\n};\nvar index = /* #__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(TextareaAutosize);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js\n");

/***/ })

};
;