"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bail";
exports.ids = ["vendor-chunks/bail"];
exports.modules = {

/***/ "(ssr)/./node_modules/bail/index.js":
/*!************************************!*\
  !*** ./node_modules/bail/index.js ***!
  \************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bail: () => (/* binding */ bail)\n/* harmony export */ });\n/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */ function bail(error) {\n    if (error) {\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmFpbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7Ozs7OztDQU1DLEdBQ00sU0FBU0EsS0FBS0MsS0FBSztJQUN4QixJQUFJQSxPQUFPO1FBQ1QsTUFBTUE7SUFDUjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL25vZGVfbW9kdWxlcy9iYWlsL2luZGV4LmpzP2UyOWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaHJvdyBhIGdpdmVuIGVycm9yLlxuICpcbiAqIEBwYXJhbSB7RXJyb3J8bnVsbHx1bmRlZmluZWR9IFtlcnJvcl1cbiAqICAgTWF5YmUgZXJyb3IuXG4gKiBAcmV0dXJucyB7YXNzZXJ0cyBlcnJvciBpcyBudWxsfHVuZGVmaW5lZH1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGJhaWwoZXJyb3IpIHtcbiAgaWYgKGVycm9yKSB7XG4gICAgdGhyb3cgZXJyb3JcbiAgfVxufVxuIl0sIm5hbWVzIjpbImJhaWwiLCJlcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bail/index.js\n");

/***/ })

};
;