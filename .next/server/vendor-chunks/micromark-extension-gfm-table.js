"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @import {Event} from 'micromark-util-types'\n */ // Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */ /**\n * Tracks a bunch of edits.\n */ class EditMap {\n    /**\n   * Create a new edit map.\n   */ constructor(){\n        /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */ this.map = [];\n    }\n    /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */ add(index, remove, add) {\n        addImplementation(this, index, remove, add);\n    }\n    // To do: add this when moving to `micromark`.\n    // /**\n    //  * Create an edit: but insert `add` before existing additions.\n    //  *\n    //  * @param {number} index\n    //  * @param {number} remove\n    //  * @param {Array<Event>} add\n    //  * @returns {undefined}\n    //  */\n    // addBefore(index, remove, add) {\n    //   addImplementation(this, index, remove, add, true)\n    // }\n    /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */ consume(events) {\n        this.map.sort(function(a, b) {\n            return a[0] - b[0];\n        });\n        /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */ if (this.map.length === 0) {\n            return;\n        }\n        // To do: if links are added in events, like they are in `markdown-rs`,\n        // this is needed.\n        // // Calculate jumps: where items in the current list move to.\n        // /** @type {Array<Jump>} */\n        // const jumps = []\n        // let index = 0\n        // let addAcc = 0\n        // let removeAcc = 0\n        // while (index < this.map.length) {\n        //   const [at, remove, add] = this.map[index]\n        //   removeAcc += remove\n        //   addAcc += add.length\n        //   jumps.push([at, removeAcc, addAcc])\n        //   index += 1\n        // }\n        //\n        // . shiftLinks(events, jumps)\n        let index = this.map.length;\n        /** @type {Array<Array<Event>>} */ const vecs = [];\n        while(index > 0){\n            index -= 1;\n            vecs.push(events.slice(this.map[index][0] + this.map[index][1]), this.map[index][2]);\n            // Truncate rest.\n            events.length = this.map[index][0];\n        }\n        vecs.push(events.slice());\n        events.length = 0;\n        let slice = vecs.pop();\n        while(slice){\n            for (const element of slice){\n                events.push(element);\n            }\n            slice = vecs.pop();\n        }\n        // Truncate everything.\n        this.map.length = 0;\n    }\n}\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */ function addImplementation(editMap, at, remove, add) {\n    let index = 0;\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */ if (remove === 0 && add.length === 0) {\n        return;\n    }\n    while(index < editMap.map.length){\n        if (editMap.map[index][0] === at) {\n            editMap.map[index][1] += remove;\n            // To do: before not used by tables, use when moving to micromark.\n            // if (before) {\n            //   add.push(...editMap.map[index][2])\n            //   editMap.map[index][2] = add\n            // } else {\n            editMap.map[index][2].push(...add);\n            // }\n            return;\n        }\n        index += 1;\n    }\n    editMap.map.push([\n        at,\n        remove,\n        add\n    ]);\n} // /**\n //  * Shift `previous` and `next` links according to `jumps`.\n //  *\n //  * This fixes links in case there are events removed or added between them.\n //  *\n //  * @param {Array<Event>} events\n //  * @param {Array<Jump>} jumps\n //  */\n // function shiftLinks(events, jumps) {\n //   let jumpIndex = 0\n //   let index = 0\n //   let add = 0\n //   let rm = 0\n //   while (index < events.length) {\n //     const rmCurr = rm\n //     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n //       add = jumps[jumpIndex][2]\n //       rm = jumps[jumpIndex][1]\n //       jumpIndex += 1\n //     }\n //     // Ignore items that will be removed.\n //     if (rm > rmCurr) {\n //       index += rm - rmCurr\n //     } else {\n //       // ?\n //       // if let Some(link) = &events[index].link {\n //       //     if let Some(next) = link.next {\n //       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n //       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n //       //             add = jumps[jumpIndex].2;\n //       //             rm = jumps[jumpIndex].1;\n //       //             jumpIndex += 1;\n //       //         }\n //       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n //       //         index = next;\n //       //         continue;\n //       //     }\n //       // }\n //       index += 1\n //     }\n //   }\n // }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */ \nconst alignment = {\n    none: \"\",\n    left: ' align=\"left\"',\n    right: ' align=\"right\"',\n    center: ' align=\"center\"'\n};\n// To do: micromark@5: use `infer` here, when all events are exposed.\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */ function gfmTableHtml() {\n    return {\n        enter: {\n            table (token) {\n                const tableAlign = token._align;\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `_align`\");\n                this.lineEndingIfNeeded();\n                this.tag(\"<table>\");\n                this.setData(\"tableAlign\", tableAlign);\n            },\n            tableBody () {\n                this.tag(\"<tbody>\");\n            },\n            tableData () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                const align = alignment[tableAlign[tableColumn]];\n                if (align === undefined) {\n                    // Capture results to ignore them.\n                    this.buffer();\n                } else {\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<td\" + align + \">\");\n                }\n            },\n            tableHead () {\n                this.lineEndingIfNeeded();\n                this.tag(\"<thead>\");\n            },\n            tableHeader () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                const align = alignment[tableAlign[tableColumn]];\n                this.lineEndingIfNeeded();\n                this.tag(\"<th\" + align + \">\");\n            },\n            tableRow () {\n                this.setData(\"tableColumn\", 0);\n                this.lineEndingIfNeeded();\n                this.tag(\"<tr>\");\n            }\n        },\n        exit: {\n            // Overwrite the default code text data handler to unescape escaped pipes when\n            // they are in tables.\n            codeTextData (token) {\n                let value = this.sliceSerialize(token);\n                if (this.getData(\"tableAlign\")) {\n                    value = value.replace(/\\\\([\\\\|])/g, replace);\n                }\n                this.raw(this.encode(value));\n            },\n            table () {\n                this.setData(\"tableAlign\");\n                // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n                // but we do need to reset it to match a funky newline GH generates for\n                // list items combined with tables.\n                this.setData(\"slurpAllLineEndings\");\n                this.lineEndingIfNeeded();\n                this.tag(\"</table>\");\n            },\n            tableBody () {\n                this.lineEndingIfNeeded();\n                this.tag(\"</tbody>\");\n            },\n            tableData () {\n                const tableAlign = this.getData(\"tableAlign\");\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                if (tableColumn in tableAlign) {\n                    this.tag(\"</td>\");\n                    this.setData(\"tableColumn\", tableColumn + 1);\n                } else {\n                    // Stop capturing.\n                    this.resume();\n                }\n            },\n            tableHead () {\n                this.lineEndingIfNeeded();\n                this.tag(\"</thead>\");\n            },\n            tableHeader () {\n                const tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                this.tag(\"</th>\");\n                this.setData(\"tableColumn\", tableColumn + 1);\n            },\n            tableRow () {\n                const tableAlign = this.getData(\"tableAlign\");\n                let tableColumn = this.getData(\"tableColumn\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, \"expected `tableAlign`\");\n                (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === \"number\", \"expected `tableColumn`\");\n                while(tableColumn < tableAlign.length){\n                    this.lineEndingIfNeeded();\n                    this.tag(\"<td\" + alignment[tableAlign[tableColumn]] + \"></td>\");\n                    tableColumn++;\n                }\n                this.setData(\"tableColumn\", tableColumn);\n                this.lineEndingIfNeeded();\n                this.tag(\"</tr>\");\n            }\n        }\n    };\n}\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */ function replace($0, $1) {\n    // Pipes work, backslashes don’t (but can’t escape pipes).\n    return $1 === \"|\" ? $1 : $0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */ /**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */ \n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */ function gfmTableAlign(events, index) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === \"table\", \"expected table\");\n    let inDelimiterRow = false;\n    /** @type {Array<Align>} */ const align = [];\n    while(index < events.length){\n        const event = events[index];\n        if (inDelimiterRow) {\n            if (event[0] === \"enter\") {\n                // Start of alignment value: set a new column.\n                // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n                if (event[1].type === \"tableContent\") {\n                    align.push(events[index + 1][1].type === \"tableDelimiterMarker\" ? \"left\" : \"none\");\n                }\n            } else if (event[1].type === \"tableContent\") {\n                if (events[index - 1][1].type === \"tableDelimiterMarker\") {\n                    const alignIndex = align.length - 1;\n                    align[alignIndex] = align[alignIndex] === \"left\" ? \"center\" : \"right\";\n                }\n            } else if (event[1].type === \"tableDelimiterRow\") {\n                break;\n            }\n        } else if (event[0] === \"enter\" && event[1].type === \"tableDelimiterRow\") {\n            inDelimiterRow = true;\n        }\n        index += 1;\n    }\n    return align;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ /**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */ \n\n\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */ function gfmTable() {\n    return {\n        flow: {\n            null: {\n                name: \"table\",\n                tokenize: tokenizeTable,\n                resolveAll: resolveTable\n            }\n        }\n    };\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeTable(effects, ok, nok) {\n    const self = this;\n    let size = 0;\n    let sizeB = 0;\n    /** @type {boolean | undefined} */ let seen;\n    return start;\n    /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */ function start(code) {\n        let index = self.events.length - 1;\n        while(index > -1){\n            const type = self.events[index][1].type;\n            if (type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding || // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n            type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix) index--;\n            else break;\n        }\n        const tail = index > -1 ? self.events[index][1].type : null;\n        const next = tail === \"tableHead\" || tail === \"tableRow\" ? bodyRowStart : headRowBefore;\n        // Don’t allow lazy body rows.\n        if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n            return nok(code);\n        }\n        return next(code);\n    }\n    /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowBefore(code) {\n        effects.enter(\"tableHead\");\n        effects.enter(\"tableRow\");\n        return headRowStart(code);\n    }\n    /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowStart(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            return headRowBreak(code);\n        }\n        // To do: micromark-js should let us parse our own whitespace in extensions,\n        // like `markdown-rs`:\n        //\n        // ```js\n        // // 4+ spaces.\n        // if (markdownSpace(code)) {\n        //   return nok(code)\n        // }\n        // ```\n        seen = true;\n        // Count the first character, that isn’t a pipe, double.\n        sizeB += 1;\n        return headRowBreak(code);\n    }\n    /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowBreak(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n            // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n            return nok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n            if (sizeB > 1) {\n                sizeB = 0;\n                // To do: check if this works.\n                // Feel free to interrupt:\n                self.interrupt = true;\n                effects.exit(\"tableRow\");\n                effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n                effects.consume(code);\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n                return headDelimiterStart;\n            }\n            // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n            return nok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            // To do: check if this is fine.\n            // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n            // State::Retry(space_or_tab(tokenizer))\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        sizeB += 1;\n        if (seen) {\n            seen = false;\n            // Header cell count.\n            size += 1;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            // Whether a delimiter was seen.\n            seen = true;\n            return headRowBreak;\n        }\n        // Anything else is cell data.\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n        return headRowData(code);\n    }\n    /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headRowData(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n            return headRowBreak(code);\n        }\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? headRowEscape : headRowData;\n    }\n    /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */ function headRowEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.consume(code);\n            return headRowData;\n        }\n        return headRowData(code);\n    }\n    /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterStart(code) {\n        // Reset `interrupt`.\n        self.interrupt = false;\n        // Note: in `markdown-rs`, we need to handle piercing here too.\n        if (self.parser.lazy[self.now().line]) {\n            return nok(code);\n        }\n        effects.enter(\"tableDelimiterRow\");\n        // Track if we’ve seen a `:` or `|`.\n        seen = false;\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(self.parser.constructs.disable.null, \"expected `disabled.null`\");\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix, self.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize)(code);\n        }\n        return headDelimiterBefore(code);\n    }\n    /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterBefore(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            return headDelimiterValueBefore(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            seen = true;\n            // If we start with a pipe, we open a cell marker.\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            return headDelimiterCellBefore;\n        }\n        // More whitespace / empty row not allowed at start.\n        return headDelimiterNok(code);\n    }\n    /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterCellBefore(code) {\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterValueBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        return headDelimiterValueBefore(code);\n    }\n    /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterValueBefore(code) {\n        // Align: left.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            sizeB += 1;\n            seen = true;\n            effects.enter(\"tableDelimiterMarker\");\n            effects.consume(code);\n            effects.exit(\"tableDelimiterMarker\");\n            return headDelimiterLeftAlignmentAfter;\n        }\n        // Align: none.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            sizeB += 1;\n            // To do: seems weird that this *isn’t* left aligned, but that state is used?\n            return headDelimiterLeftAlignmentAfter(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            return headDelimiterCellAfter(code);\n        }\n        return headDelimiterNok(code);\n    }\n    /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterLeftAlignmentAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            effects.enter(\"tableDelimiterFiller\");\n            return headDelimiterFiller(code);\n        }\n        // Anything else is not ok after the left-align colon.\n        return headDelimiterNok(code);\n    }\n    /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterFiller(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n            effects.consume(code);\n            return headDelimiterFiller;\n        }\n        // Align is `center` if it was `left`, `right` otherwise.\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n            seen = true;\n            effects.exit(\"tableDelimiterFiller\");\n            effects.enter(\"tableDelimiterMarker\");\n            effects.consume(code);\n            effects.exit(\"tableDelimiterMarker\");\n            return headDelimiterRightAlignmentAfter;\n        }\n        effects.exit(\"tableDelimiterFiller\");\n        return headDelimiterRightAlignmentAfter(code);\n    }\n    /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterRightAlignmentAfter(code) {\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headDelimiterCellAfter, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        return headDelimiterCellAfter(code);\n    }\n    /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterCellAfter(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            return headDelimiterBefore(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            // Exit when:\n            // * there was no `:` or `|` at all (it’s a thematic break or setext\n            //   underline instead)\n            // * the header cell count is not the delimiter cell count\n            if (!seen || size !== sizeB) {\n                return headDelimiterNok(code);\n            }\n            // Note: in markdown-rs`, a reset is needed here.\n            effects.exit(\"tableDelimiterRow\");\n            effects.exit(\"tableHead\");\n            // To do: in `markdown-rs`, resolvers need to be registered manually.\n            // effects.register_resolver(ResolveName::GfmTable)\n            return ok(code);\n        }\n        return headDelimiterNok(code);\n    }\n    /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function headDelimiterNok(code) {\n        // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n        return nok(code);\n    }\n    /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowStart(code) {\n        // Note: in `markdown-rs` we need to manually take care of a prefix,\n        // but in `micromark-js` that is done for us, so if we’re here, we’re\n        // never at whitespace.\n        effects.enter(\"tableRow\");\n        return bodyRowBreak(code);\n    }\n    /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowBreak(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.enter(\"tableCellDivider\");\n            effects.consume(code);\n            effects.exit(\"tableCellDivider\");\n            return bodyRowBreak;\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n            effects.exit(\"tableRow\");\n            return ok(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        // Anything else is cell content.\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n        return bodyRowData(code);\n    }\n    /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowData(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data);\n            return bodyRowBreak(code);\n        }\n        effects.consume(code);\n        return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? bodyRowEscape : bodyRowData;\n    }\n    /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */ function bodyRowEscape(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n            effects.consume(code);\n            return bodyRowData;\n        }\n        return bodyRowData(code);\n    }\n}\n/** @type {Resolver} */ function resolveTable(events, context) {\n    let index = -1;\n    let inFirstCellAwaitingPipe = true;\n    /** @type {RowKind} */ let rowKind = 0;\n    /** @type {Range} */ let lastCell = [\n        0,\n        0,\n        0,\n        0\n    ];\n    /** @type {Range} */ let cell = [\n        0,\n        0,\n        0,\n        0\n    ];\n    let afterHeadAwaitingFirstBodyRow = false;\n    let lastTableEnd = 0;\n    /** @type {Token | undefined} */ let currentTable;\n    /** @type {Token | undefined} */ let currentBody;\n    /** @type {Token | undefined} */ let currentCell;\n    const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap();\n    while(++index < events.length){\n        const event = events[index];\n        const token = event[1];\n        if (event[0] === \"enter\") {\n            // Start of head.\n            if (token.type === \"tableHead\") {\n                afterHeadAwaitingFirstBodyRow = false;\n                // Inject previous (body end and) table end.\n                if (lastTableEnd !== 0) {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, \"there should be a table opening\");\n                    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody);\n                    currentBody = undefined;\n                    lastTableEnd = 0;\n                }\n                // Inject table start.\n                currentTable = {\n                    type: \"table\",\n                    start: Object.assign({}, token.start),\n                    // Note: correct end is set later.\n                    end: Object.assign({}, token.end)\n                };\n                map.add(index, 0, [\n                    [\n                        \"enter\",\n                        currentTable,\n                        context\n                    ]\n                ]);\n            } else if (token.type === \"tableRow\" || token.type === \"tableDelimiterRow\") {\n                inFirstCellAwaitingPipe = true;\n                currentCell = undefined;\n                lastCell = [\n                    0,\n                    0,\n                    0,\n                    0\n                ];\n                cell = [\n                    0,\n                    index + 1,\n                    0,\n                    0\n                ];\n                // Inject table body start.\n                if (afterHeadAwaitingFirstBodyRow) {\n                    afterHeadAwaitingFirstBodyRow = false;\n                    currentBody = {\n                        type: \"tableBody\",\n                        start: Object.assign({}, token.start),\n                        // Note: correct end is set later.\n                        end: Object.assign({}, token.end)\n                    };\n                    map.add(index, 0, [\n                        [\n                            \"enter\",\n                            currentBody,\n                            context\n                        ]\n                    ]);\n                }\n                rowKind = token.type === \"tableDelimiterRow\" ? 2 : currentBody ? 3 : 1;\n            } else if (rowKind && (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data || token.type === \"tableDelimiterMarker\" || token.type === \"tableDelimiterFiller\")) {\n                inFirstCellAwaitingPipe = false;\n                // First value in cell.\n                if (cell[2] === 0) {\n                    if (lastCell[1] !== 0) {\n                        cell[0] = cell[1];\n                        currentCell = flushCell(map, context, lastCell, rowKind, undefined, currentCell);\n                        lastCell = [\n                            0,\n                            0,\n                            0,\n                            0\n                        ];\n                    }\n                    cell[2] = index;\n                }\n            } else if (token.type === \"tableCellDivider\") {\n                if (inFirstCellAwaitingPipe) {\n                    inFirstCellAwaitingPipe = false;\n                } else {\n                    if (lastCell[1] !== 0) {\n                        cell[0] = cell[1];\n                        currentCell = flushCell(map, context, lastCell, rowKind, undefined, currentCell);\n                    }\n                    lastCell = cell;\n                    cell = [\n                        lastCell[1],\n                        index,\n                        0,\n                        0\n                    ];\n                }\n            }\n        } else if (token.type === \"tableHead\") {\n            afterHeadAwaitingFirstBodyRow = true;\n            lastTableEnd = index;\n        } else if (token.type === \"tableRow\" || token.type === \"tableDelimiterRow\") {\n            lastTableEnd = index;\n            if (lastCell[1] !== 0) {\n                cell[0] = cell[1];\n                currentCell = flushCell(map, context, lastCell, rowKind, index, currentCell);\n            } else if (cell[1] !== 0) {\n                currentCell = flushCell(map, context, cell, rowKind, index, currentCell);\n            }\n            rowKind = 0;\n        } else if (rowKind && (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data || token.type === \"tableDelimiterMarker\" || token.type === \"tableDelimiterFiller\")) {\n            cell[3] = index;\n        }\n    }\n    if (lastTableEnd !== 0) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, \"expected table opening\");\n        flushTableEnd(map, context, lastTableEnd, currentTable, currentBody);\n    }\n    map.consume(context.events);\n    // To do: move this into `html`, when events are exposed there.\n    // That’s what `markdown-rs` does.\n    // That needs updates to `mdast-util-gfm-table`.\n    index = -1;\n    while(++index < context.events.length){\n        const event = context.events[index];\n        if (event[0] === \"enter\" && event[1].type === \"table\") {\n            event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index);\n        }\n    }\n    return events;\n}\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */ // eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n    // `markdown-rs` uses:\n    // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n    const groupName = rowKind === 1 ? \"tableHeader\" : rowKind === 2 ? \"tableDelimiter\" : \"tableData\";\n    // `markdown-rs` uses:\n    // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n    const valueName = \"tableContent\";\n    // Insert an exit for the previous cell, if there is one.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //          ^-- exit\n    //           ^^^^-- this cell\n    // ```\n    if (range[0] !== 0) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(previousCell, \"expected previous cell enter\");\n        previousCell.end = Object.assign({}, getPoint(context.events, range[0]));\n        map.add(range[0], 0, [\n            [\n                \"exit\",\n                previousCell,\n                context\n            ]\n        ]);\n    }\n    // Insert enter of this cell.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //           ^-- enter\n    //           ^^^^-- this cell\n    // ```\n    const now = getPoint(context.events, range[1]);\n    previousCell = {\n        type: groupName,\n        start: Object.assign({}, now),\n        // Note: correct end is set later.\n        end: Object.assign({}, now)\n    };\n    map.add(range[1], 0, [\n        [\n            \"enter\",\n            previousCell,\n            context\n        ]\n    ]);\n    // Insert text start at first data start and end at last data end, and\n    // remove events between.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //            ^-- enter\n    //             ^-- exit\n    //           ^^^^-- this cell\n    // ```\n    if (range[2] !== 0) {\n        const relatedStart = getPoint(context.events, range[2]);\n        const relatedEnd = getPoint(context.events, range[3]);\n        /** @type {Token} */ const valueToken = {\n            type: valueName,\n            start: Object.assign({}, relatedStart),\n            end: Object.assign({}, relatedEnd)\n        };\n        map.add(range[2], 0, [\n            [\n                \"enter\",\n                valueToken,\n                context\n            ]\n        ]);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(range[3] !== 0);\n        if (rowKind !== 2) {\n            // Fix positional info on remaining events\n            const start = context.events[range[2]];\n            const end = context.events[range[3]];\n            start[1].end = Object.assign({}, end[1].end);\n            start[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText;\n            start[1].contentType = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText;\n            // Remove if needed.\n            if (range[3] > range[2] + 1) {\n                const a = range[2] + 1;\n                const b = range[3] - range[2] - 1;\n                map.add(a, b, []);\n            }\n        }\n        map.add(range[3] + 1, 0, [\n            [\n                \"exit\",\n                valueToken,\n                context\n            ]\n        ]);\n    }\n    // Insert an exit for the last cell, if at the row end.\n    //\n    // ```markdown\n    // > | | aa | bb | cc |\n    //                    ^-- exit\n    //               ^^^^^^-- this cell (the last one contains two “between” parts)\n    // ```\n    if (rowEnd !== undefined) {\n        previousCell.end = Object.assign({}, getPoint(context.events, rowEnd));\n        map.add(rowEnd, 0, [\n            [\n                \"exit\",\n                previousCell,\n                context\n            ]\n        ]);\n        previousCell = undefined;\n    }\n    return previousCell;\n}\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */ // eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n    /** @type {Array<Event>} */ const exits = [];\n    const related = getPoint(context.events, index);\n    if (tableBody) {\n        tableBody.end = Object.assign({}, related);\n        exits.push([\n            \"exit\",\n            tableBody,\n            context\n        ]);\n    }\n    table.end = Object.assign({}, related);\n    exits.push([\n        \"exit\",\n        table,\n        context\n    ]);\n    map.add(index + 1, 0, exits);\n}\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */ function getPoint(events, index) {\n    const event = events[index];\n    const side = event[0] === \"enter\" ? \"start\" : \"end\";\n    return event[1][side];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;